buildscript {
    ext.buildConfig = [
            'compileSdk' : 35,
            'minSdk'     : 21,
            'targetSdk'  : 35,
            "versionCode": 5401,
            "versionName": "3.88.3"
    ]

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url "https://jitpack.io" }
        flatDir {
            dirs 'libs'
        }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots" }
        maven { url "https://repo1.maven.org/maven2" }
    }
    dependencies {
        classpath "com.google.firebase:firebase-crashlytics-gradle:2.9.2"
        classpath "com.android.tools.build:gradle:8.1.4"
        classpath "com.google.gms:google-services:4.4.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24"
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.7.5"
        classpath "com.google.firebase:perf-plugin:1.4.1"
        classpath "com.google.dagger:hilt-android-gradle-plugin:2.48"
        classpath "com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:1.9.24-1.0.20"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        maven { url 'https://repo.survicate.com' }
        maven { url 'https://maven.zohodl.com'}
        maven { url "https://packages.bureau.id/api/packages/Bureau/maven" }
    }
}


task clean(type: Delete) {
    delete rootProject.buildDir
}
