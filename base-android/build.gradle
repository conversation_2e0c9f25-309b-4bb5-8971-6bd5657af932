plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
}

android {
    compileSdkVersion buildConfig.compileSdk

    defaultConfig {
        minSdkVersion buildConfig.minSdk
        targetSdkVersion buildConfig.targetSdk
    }

    dataBinding {
        enabled = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.bukuwarung.base_android'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "androidx.core:core-ktx:1.6.0"

    implementation "androidx.recyclerview:recyclerview:1.0.0"
    implementation "com.google.android.material:material:1.9.0"
    implementation "androidx.browser:browser:1.2.0"

    implementation "androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02"
    implementation "androidx.navigation:navigation-ui-ktx:2.3.0-alpha02"

    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.4.0"
}