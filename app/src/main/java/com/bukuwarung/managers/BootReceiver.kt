package com.bukuwarung.managers

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.bukuwarung.managers.local_notification.domain.DailyDueDateNotificationReceiver
import com.bukuwarung.session.SessionManager
import java.util.*
import java.util.concurrent.TimeUnit


class BootReceiver : BroadcastReceiver() {
    companion object {
        const val BOOT_RECEIVER_NAME = "BootReceiver"
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if ((intent?.action == Intent.ACTION_BOOT_COMPLETED || intent?.action == BOOT_RECEIVER_NAME) && context != null) {
            
            // Android 15+ restrictions: avoid starting certain foreground service types from BOOT_COMPLETED
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
                Log.i(TAG, "Android 15+ detected - applying foreground service restrictions")
                // Only schedule alarms, do not start foreground services directly
                SessionManager.getInstance().setDailyBroadcastRun()
                scheduleAlarmsForAndroid15(context)
            } else {
                // Pre-Android 15 behavior
                SessionManager.getInstance().setDailyBroadcastRun()
                scheduleAlarms(context)
            }
        }
    }

    /**
     * Schedule alarms for Android 15+ with restrictions in mind
     */
    private fun scheduleAlarmsForAndroid15(context: Context) {
        try {
            // Use AlarmManager instead of foreground services for Android 15+
            scheduleAlarms(context)
            Log.i(TAG, "Alarms scheduled successfully for Android 15+")
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling alarms for Android 15+", e)
        }
    }

    private fun scheduleAlarms(context: Context) {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 8)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        if (hasRunToday()) {       //if the alarm has run this day
            calendar.add(Calendar.DATE, 1) //schedule it to run again starting tomorrow
        }
        val firstRunTime = calendar.timeInMillis
        val mgr = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val alarmIntent = Intent(context, DailyDueDateNotificationReceiver::class.java)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(context, 0, alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getBroadcast(context, 0, alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        mgr.cancel(pendingIntent)
        mgr.setRepeating(AlarmManager.RTC_WAKEUP, firstRunTime, AlarmManager.INTERVAL_DAY, pendingIntent)
    }

    private fun hasRunToday(): Boolean {
        val alarmLastRun = SessionManager.getInstance().dailyDueDateLastRun
        if (alarmLastRun == -1L) return false
        //check by comparing day, month and year
        return Date().time - alarmLastRun < TimeUnit.DAYS.toMillis(1)
    }
}
