package com.bukuwarung.utils

import android.util.Log
import java.util.*

/**
 * Safe array utilities to handle Android 15+ OpenJDK changes
 * Fixes issues with Arrays.asList().toArray() component type changes
 */
object SafeArrayUtils {
    
    private const val TAG = "SafeArrayUtils"
    
    /**
     * Safe conversion from Array to typed array, avoiding Arrays.asList().toArray() issues
     */
    inline fun <reified T> toTypedArray(vararg elements: T): Array<T> {
        return try {
            arrayOf(*elements)
        } catch (e: Exception) {
            Log.e(TAG, "Error creating typed array", e)
            emptyArray<T>()
        }
    }
    
    /**
     * Safe conversion from Collection to typed array with proper component type
     */
    inline fun <reified T> Collection<T>.toTypedArraySafe(): Array<T> {
        return try {
            // Use Collection.toArray(T[]) instead of Arrays.asList().toArray()
            this.toTypedArray()
        } catch (e: Exception) {
            Log.e(TAG, "Error converting collection to typed array", e)
            emptyArray<T>()
        }
    }
    
    /**
     * Safe conversion from List to String array
     */
    fun List<String>.toStringArraySafe(): Array<String> {
        return try {
            // Use toArray(new String[0]) pattern instead of toArray()
            this.toTypedArray()
        } catch (e: Exception) {
            Log.e(TAG, "Error converting list to string array", e)
            emptyArray()
        }
    }
    
    /**
     * Create array from varargs safely
     */
    fun <T> createArraySafe(vararg elements: T): Array<T> {
        return try {
            @Suppress("UNCHECKED_CAST")
            when {
                elements.isEmpty() -> emptyArray<Any>() as Array<T>
                else -> {
                    val first = elements.first()
                    when (first) {
                        is String -> elements.toList().toTypedArray()
                        else -> elements.toList().toTypedArray()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating array safely", e)
            @Suppress("UNCHECKED_CAST")
            emptyArray<Any>() as Array<T>
        }
    }
    
    /**
     * Convert Arrays.asList() result to proper typed array
     */
    fun <T> List<T>.toArrayWithType(type: Class<T>): Array<T> {
        return try {
            @Suppress("UNCHECKED_CAST")
            when (type) {
                String::class.java -> {
                    val stringArray = Array(this.size) { "" }
                    this.forEachIndexed { index, item ->
                        stringArray[index] = item.toString()
                    }
                    stringArray as Array<T>
                }
                else -> {
                    this.toTypedArray()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error converting to typed array", e)
            this.toTypedArray()
        }
    }
    
    /**
     * Safe ArrayList creation from array
     */
    fun <T> arrayToListSafe(array: Array<T>): MutableList<T> {
        return try {
            array.toMutableList()
        } catch (e: Exception) {
            Log.e(TAG, "Error converting array to list", e)
            mutableListOf()
        }
    }
    
    /**
     * Safe array creation from list avoiding Arrays.asList() issues
     */
    fun <T> listToArraySafe(list: List<T>, arrayType: Class<T>): Array<T> {
        return try {
            @Suppress("UNCHECKED_CAST")
            val array = java.lang.reflect.Array.newInstance(arrayType, list.size) as Array<T>
            list.forEachIndexed { index, item ->
                array[index] = item
            }
            array
        } catch (e: Exception) {
            Log.e(TAG, "Error converting list to array with reflection", e)
            list.toTypedArray()
        }
    }
}
