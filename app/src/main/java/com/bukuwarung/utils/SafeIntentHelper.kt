package com.bukuwarung.utils

import android.content.ComponentName
import android.content.Intent
import android.os.Build
import android.util.Log

/**
 * Helper for creating safe intents compatible with Android 15+ security changes
 */
object SafeIntentHelper {
    
    private const val TAG = "SafeIntentHelper"
    
    /**
     * Create a safe explicit intent with proper action and component matching
     */
    fun createSafeExplicitIntent(
        packageName: String,
        className: String,
        action: String = Intent.ACTION_MAIN
    ): Intent {
        val intent = Intent()
        
        // Android 15+ requires intents to have actions
        intent.action = action
        
        // Set explicit component for security
        intent.component = ComponentName(packageName, className)
        
        // Add safety flags
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        
        return intent
    }
    
    /**
     * Create a safe implicit intent with required action
     */
    fun createSafeImplicitIntent(action: String): Intent {
        if (action.isEmpty()) {
            Log.w(TAG, "Creating intent with empty action - this may fail on Android 15+")
            return Intent(Intent.ACTION_MAIN)
        }
        
        val intent = Intent(action)
        
        // Android 15+ safety measures
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            // Ensure intent has proper action for Android 15+
            if (intent.action == null) {
                intent.action = Intent.ACTION_MAIN
            }
        }
        
        return intent
    }
    
    /**
     * Validate intent for Android 15+ compatibility
     */
    fun validateIntentForAndroid15(intent: Intent): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            return true // Pre-Android 15, no validation needed
        }
        
        // Android 15+ requirements
        return when {
            // Intents must have actions
            intent.action == null -> {
                Log.w(TAG, "Intent missing required action for Android 15+")
                false
            }
            
            // Check if explicit intent matches target filters (if component is set)
            intent.component != null -> {
                // For explicit intents, we assume they're valid if they have a component and action
                true
            }
            
            else -> {
                // Implicit intents need proper action
                intent.action?.isNotEmpty() ?: false
            }
        }
    }
    
    /**
     * Fix intent to be compatible with Android 15+
     */
    fun makeIntentSafeForAndroid15(intent: Intent): Intent {
        val safeIntent = Intent(intent)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            // Ensure intent has action
            if (safeIntent.action == null) {
                Log.w(TAG, "Adding default action to intent for Android 15+ compatibility")
                safeIntent.action = Intent.ACTION_MAIN
            }
            
            // Add safety flags
            safeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        
        return safeIntent
    }
    
    /**
     * Create safe share intent
     */
    fun createSafeShareIntent(type: String, text: String? = null, subject: String? = null): Intent {
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = type
        
        text?.let { intent.putExtra(Intent.EXTRA_TEXT, it) }
        subject?.let { intent.putExtra(Intent.EXTRA_SUBJECT, it) }
        
        return makeIntentSafeForAndroid15(intent)
    }
    
    /**
     * Create safe view intent
     */
    fun createSafeViewIntent(uri: String): Intent {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = android.net.Uri.parse(uri)
        
        return makeIntentSafeForAndroid15(intent)
    }
    
    /**
     * Create safe pick intent for file selection
     */
    fun createSafePickIntent(type: String = "*/*"): Intent {
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.type = type
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        
        return makeIntentSafeForAndroid15(intent)
    }
    
    /**
     * Create safe camera intent
     */
    fun createSafeCameraIntent(): Intent {
        val intent = Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE)
        return makeIntentSafeForAndroid15(intent)
    }
}
