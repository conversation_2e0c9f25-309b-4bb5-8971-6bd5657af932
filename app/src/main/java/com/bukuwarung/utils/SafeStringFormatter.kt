package com.bukuwarung.utils

import android.util.Log
import java.util.*

/**
 * Safe string formatter to handle Android 15+ OpenJDK changes
 * Fixes issues with argument indexing and validation
 */
object SafeStringFormatter {
    
    private const val TAG = "SafeStringFormatter"
    
    /**
     * Safe wrapper for String.format with proper argument index handling
     */
    fun format(format: String, vararg args: Any?): String {
        return try {
            // Fix argument indices that start from 0 (change to 1-based)
            val fixedFormat = fixArgumentIndices(format)
            
            when (args.size) {
                0 -> fixedFormat
                else -> String.format(Locale.getDefault(), fixedFormat, *args)
            }
        } catch (e: IllegalFormatArgumentIndexException) {
            Log.e(TAG, "Format error with illegal argument index: $format", e)
            handleFormatError(format, args)
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected format error: $format", e)
            handleFormatError(format, args)
        }
    }
    
    /**
     * Safe wrapper for String.format with specific locale
     */
    fun format(locale: Locale, format: String, vararg args: Any?): String {
        return try {
            val fixedFormat = fixArgumentIndices(format)
            
            when (args.size) {
                0 -> fixedFormat
                else -> String.format(locale, fixedFormat, *args)
            }
        } catch (e: IllegalFormatArgumentIndexException) {
            Log.e(TAG, "Format error with illegal argument index: $format", e)
            handleFormatError(format, args)
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected format error: $format", e)
            handleFormatError(format, args)
        }
    }
    
    /**
     * Fix argument indices in format string (convert 0-based to 1-based)
     */
    private fun fixArgumentIndices(format: String): String {
        // Pattern to match format specifiers like %0, %0$, etc.
        val pattern = "%0(?=\\$|[diosxXeEfFgGaAcbBhH%n])".toRegex()
        return format.replace(pattern, "%1")
    }
    
    /**
     * Handle format errors by providing fallback formatting
     */
    private fun handleFormatError(format: String, args: Array<out Any?>): String {
        return try {
            // Simple fallback: just replace placeholders with argument values
            var result = format
            args.forEachIndexed { index, arg ->
                result = result.replace("%${index + 1}", arg?.toString() ?: "null")
                result = result.replace("%s", arg?.toString() ?: "null")
                result = result.replace("%d", arg?.toString() ?: "0")
            }
            result
        } catch (e: Exception) {
            Log.e(TAG, "Fallback formatting also failed", e)
            // Last resort: return format string as is
            format
        }
    }
    
    /**
     * Validate format string for Android 15 compatibility
     */
    fun validateFormat(format: String): Boolean {
        return try {
            // Check for invalid argument indices (0-based)
            val invalidPattern = "%0(?=\\$|[diosxXeEfFgGaAcbBhH%n])".toRegex()
            !format.contains(invalidPattern)
        } catch (e: Exception) {
            Log.e(TAG, "Error validating format string", e)
            false
        }
    }
    
    /**
     * Check if a format string needs fixing for Android 15
     */
    fun needsFixing(format: String): Boolean {
        return try {
            val invalidPattern = "%0(?=\\$|[diosxXeEfFgGaAcbBhH%n])".toRegex()
            format.contains(invalidPattern)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if format needs fixing", e)
            false
        }
    }
}
