package com.bukuwarung.utils

import android.app.Activity
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.RequiresApi
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat

/**
 * Helper class to handle edge-to-edge display for Android 15+ compatibility
 */
object EdgeToEdgeHelper {
    
    /**
     * Applies edge-to-edge display with proper window insets handling
     */
    fun applyEdgeToEdge(activity: Activity, statusBarColor: Int = Color.TRANSPARENT, navigationBarColor: Int = Color.TRANSPARENT) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            setupForAndroid15Plus(activity, statusBarColor, navigationBarColor)
        } else {
            setupForPreAndroid15(activity, statusBarColor, navigationBarColor)
        }
    }
    
    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    private fun setupForAndroid15Plus(activity: Activity, statusBarColor: Int, navigationBarColor: Int) {
        // Android 15+ enforces edge-to-edge by default
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        
        // Set transparent system bars for Android 15
        activity.window.apply {
            statusBarColor = statusBarColor
            navigationBarColor = navigationBarColor
            
            // Ensure content draws behind system bars
            setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            )
        }
        
        // Force LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS for Android 15
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val layoutParams = activity.window.attributes
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS
            activity.window.attributes = layoutParams
        }
    }
    
    private fun setupForPreAndroid15(activity: Activity, statusBarColor: Int, navigationBarColor: Int) {
        // For Android versions before 15, use standard edge-to-edge setup
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        
        activity.window.apply {
            this.statusBarColor = statusBarColor
            this.navigationBarColor = navigationBarColor
        }
    }
    
    /**
     * Applies window insets padding to a view to prevent content from being hidden behind system bars
     */
    fun applyWindowInsetsPadding(
        view: View,
        left: Boolean = false,
        top: Boolean = false,
        right: Boolean = false,
        bottom: Boolean = false
    ) {
        val initialPadding = Insets(
            view.paddingLeft,
            view.paddingTop,
            view.paddingRight,
            view.paddingBottom
        )
        
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            v.setPadding(
                if (left) initialPadding.left + systemBars.left else initialPadding.left,
                if (top) initialPadding.top + systemBars.top else initialPadding.top,
                if (right) initialPadding.right + systemBars.right else initialPadding.right,
                if (bottom) initialPadding.bottom + systemBars.bottom else initialPadding.bottom
            )
            
            insets
        }
    }
    
    /**
     * Applies window insets padding to view with margins
     */
    fun applyWindowInsetsMargin(
        view: View,
        left: Boolean = false,
        top: Boolean = false,
        right: Boolean = false,
        bottom: Boolean = false
    ) {
        val initialMargin = getMargins(view)
        
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            val layoutParams = v.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.setMargins(
                if (left) initialMargin.left + systemBars.left else initialMargin.left,
                if (top) initialMargin.top + systemBars.top else initialMargin.top,
                if (right) initialMargin.right + systemBars.right else initialMargin.right,
                if (bottom) initialMargin.bottom + systemBars.bottom else initialMargin.bottom
            )
            v.layoutParams = layoutParams
            
            insets
        }
    }
    
    /**
     * Get the current margins of a view
     */
    private fun getMargins(view: View): Insets {
        val layoutParams = view.layoutParams
        return if (layoutParams is ViewGroup.MarginLayoutParams) {
            Insets(
                layoutParams.leftMargin,
                layoutParams.topMargin,
                layoutParams.rightMargin,
                layoutParams.bottomMargin
            )
        } else {
            Insets(0, 0, 0, 0)
        }
    }
    
    /**
     * Data class to hold inset values
     */
    data class Insets(
        val left: Int,
        val top: Int,
        val right: Int,
        val bottom: Int
    )
}
