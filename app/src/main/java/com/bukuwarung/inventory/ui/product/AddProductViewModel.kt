package com.bukuwarung.inventory.ui.product

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dao.InventoryDao
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.inventory.generateId
import com.bukuwarung.inventory.generateProductCode
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.inventory.usecases.StockUnit
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AddProductViewModel @Inject constructor(
    val productInventory: ProductInventory,
    val stockUnit: StockUnit,
    val backgroundDispatcher: CoroutineDispatcher,
    val inventoryDao: InventoryDao,
    val productCategoryUseCase: ProductCategoryUseCase
) : BaseViewModel() {
    private val addProduct = MutableLiveData<AddProductEvents>()
    val observerProductEvents: LiveData<AddProductEvents> = addProduct
    private var productName: String = ""
    private var minimumStock = 1
    private var allStock = 0.0
    private var bookId: String = ""
    var currentMeasurementId: String = ""
    private var currentMeasurementName: String = ""
    private var productSellingPrice = 0.0
    private var productBuyingPrice = 0.0
    private var isTransactionFlow: Boolean = false
    private var productCount = 0
    private var trackInventory = AppConst.INVENTORY_TRACKING_ENABLED

     var productCategoryIds = listOf<String>()

    fun init(bookId: String, isTransactionFlow: Boolean) {
        this.bookId = bookId
        this.isTransactionFlow = isTransactionFlow
        addProduct.value = AddProductEvents.HideStockNumberView
        viewModelScope.launch {
            withContext(backgroundDispatcher) {
                val unit = stockUnit.getDefaultMeasurement(bookId)
                currentMeasurementId = unit?.measurementId ?: ""
                currentMeasurementName = unit?.measurementName ?: ""
                withContext(Dispatchers.Main) {
                    addProduct.value = AddProductEvents.UpdateCurrentMeasurement(currentMeasurementName)
                }
            }

            addProduct.value = AddProductEvents.SetProductCount(productInventory.getProductCount(bookId))
        }
    }

    fun productNameChange(productName: String?) {
        if (productName != null && productName.trim().isNotEmpty()) {
            addProduct.value = AddProductEvents.ShowStockNumberView(0, 1, false)
            this.productName = productName.trim()
        }
    }

    fun trackInventoryStatusChanged(trackInventory: Int) {
        this.trackInventory = trackInventory
    }

    fun productPriceChange(price: String?) {
        try {
            if (price != null && price.trim().isNotEmpty()) {
                var cleanBalance = Utility.cleanBalance(price)
                this.productSellingPrice = cleanBalance.toDouble()
            } else {
                productSellingPrice = 0.0
            }
        } catch (e: Exception) {
            e.recordException()
            productSellingPrice = 0.0
        }
    }

    fun productBuyingPriceChange(price: String?) {
        try {
            if (price != null && price.trim().isNotEmpty()) {
                var cleanBalance = Utility.cleanBalance(price)
                this.productBuyingPrice = cleanBalance.toDouble()
            } else {
                productBuyingPrice = 0.0
            }
        } catch (e: Exception) {
            e.recordException()
            productBuyingPrice = 0.0
        }
    }

    fun minimumStockCountIncreaseClick() {
        minimumStock++
        addProduct.value = AddProductEvents.MinimumStock(minimumStock)
    }

    fun minimumStockCountDecreaseClick() {
        if (minimumStock == 0) return
        minimumStock--
        addProduct.value = AddProductEvents.MinimumStock(minimumStock)
    }

    fun minimumStockCountChange(minimumStockCountInputValue: String) {
        try {
            minimumStock = Integer.parseInt(minimumStockCountInputValue)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun allStockCountChnaged(allStockCountInputValue: String) {
        try {
            allStock = Utility.extractAmountFromText(allStockCountInputValue)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun maxStockCountDecreaseClick() {
        if (allStock == 0.0) return
        allStock--
        addProduct.value = AddProductEvents.AllStock(allStock)
    }

    fun maxStockCountIncreaseClick() {
        allStock++
        addProduct.value = AddProductEvents.AllStock(allStock)
    }

    fun onMeasurementClicked() {
        addProduct.value = AddProductEvents.OpenMeasurementBottomSheet(bookId, currentMeasurementId)
    }

    fun onMeasurementChanged(measurementId: String, measurementName: String) {
        currentMeasurementName = measurementName
        currentMeasurementId = measurementId
        addProduct.value = AddProductEvents.UpdateCurrentMeasurement(measurementName)
    }

    fun addNewMeasurementRequest() {
        addProduct.value = AddProductEvents.ShowAddNewMeasurementDialog
    }

    fun addNewMeasurement(name: String) {
        viewModelScope.launch {
            withContext(backgroundDispatcher) {
                val measurementEntity = MeasurementEntity()
                measurementEntity.measurementName = name
                measurementEntity.isDefault = 0
                measurementEntity.measurementId = measurementEntity.generateId()
                measurementEntity.bookId = bookId
                // try {
                stockUnit.addMeasurementUnit(measurementEntity)
                withContext(Dispatchers.Main) {
                    onMeasurementChanged(measurementEntity.measurementId, measurementEntity.measurementName)
                    addProduct.value = AddProductEvents.DismissAddNewMeasurementDialog
                }
                /* } catch (e: Exception) {
                     withContext(Dispatchers.Main) {
                         addProduct.value = AddProductEvents.ShowError(e.message ?: "Unknown error")
                     }
                 }*/
            }
        }
    }


    fun addProduct(source: String, isFav: Boolean, categories : List<String>) {
        viewModelScope.launch {
            addProduct.value = AddProductEvents.ShowLoading
            withContext(backgroundDispatcher) {
                val product = createProductEntity()
                product.favourite = isFav
                productInventory.addProductToInventory(product,source, categories)
                withContext(Dispatchers.Main) {
                    addProduct.value = AddProductEvents.HideLoading
                    if (isTransactionFlow) {
                        addProduct.value = AddProductEvents.FinishCurrentScreen(product)
                    } else {
                        addProduct.value = AddProductEvents.FinishScreen(product)
                    }
                }
            }
        }
    }

    fun addProduct(source:String) {
        viewModelScope.launch {
            addProduct.value = AddProductEvents.ShowLoading
            withContext(backgroundDispatcher) {
                val product = createProductEntity()
                productInventory.addProductToInventory(product, source)
                withContext(Dispatchers.Main) {
                    addProduct.value = AddProductEvents.HideLoading
                    if (isTransactionFlow) {
                        addProduct.value = AddProductEvents.FinishCurrentScreen(product)
                    } else {
                        addProduct.value = AddProductEvents.FinishScreen(product)
                    }
                }
            }
        }
    }

    private fun createProductEntity(): ProductEntity {
        val product = ProductEntity()
        product.productId = product.generateId()
        product.bookId = bookId
        product.name = productName
        product.minimumStock = minimumStock
        product.trackInventory = trackInventory
        product.stock = allStock
        product.initialStock = allStock
        product.measurementName = currentMeasurementName
        product.measurementId = currentMeasurementId
        product.code = product.generateProductCode()
        product.unitPrice = productSellingPrice
        product.buyingPrice = productBuyingPrice
        EntityHelper.fillProductMetadata(product)
        return product
    }

    sealed class AddProductEvents {
        object ShowLoading : AddProductEvents()
        object HideLoading : AddProductEvents()
        data class FinishScreen(val product: ProductEntity) : AddProductEvents()
        data class FinishCurrentScreen(val product: ProductEntity) : AddProductEvents()
        data class ShowError(val error: String) : AddProductEvents()
        data class MinimumStock(val count: Int) : AddProductEvents()
        data class AllStock(val count: Double) : AddProductEvents()
        data class ShowStockNumberView(
            val allStockDefault: Int,
            val minimumStockDefault: Int,
            val hideMaxMinStockUI: Boolean
        ) : AddProductEvents()

        object HideStockNumberView : AddProductEvents()
        data class OpenMeasurementBottomSheet(val bookId: String, val currentMeasurementId: String) : AddProductEvents()
        data class UpdateCurrentMeasurement(val measurementName: String) : AddProductEvents()
        data class MinMAxStockVisibility(val hide: Boolean) : AddProductEvents()
        object ShowAddNewMeasurementDialog : AddProductEvents()
        object DismissAddNewMeasurementDialog : AddProductEvents()
        data class SetProductCount(val count: Int) : AddProductEvents()

    }


}