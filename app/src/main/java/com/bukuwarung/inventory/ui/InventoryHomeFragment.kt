package com.bukuwarung.inventory.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.pos.PosActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.baseui.DefaultViewPager
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentInventoryHomeBinding
import com.bukuwarung.datasync.restore.ManualDataRestoreDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities.showTooltip
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.hideView
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class InventoryHomeFragment : BaseFragment() {

    private val viewModel: InventoryViewModel by viewModels()
    private lateinit var binding: FragmentInventoryHomeBinding
    private lateinit var viewPager: DefaultViewPager
    override fun setupView(view: View) {}
    private var normalBackPressedAction: () -> Unit = { requireActivity().finish() }
    private var adapter: InventoryHomePagerAdapter? = null
    private var startSendingEvents = false
    private var firstEventSent = false

    override fun subscribeState() {
        viewModel.runningOutProductListStatus.observe(viewLifecycleOwner, Observer {
            when (it) {
                is InventoryViewModel.ProductStatusEvent.UpdateLowProductList -> {
                    if (it.productList.isEmpty()) {
                        binding.inventoryHomeTabLayout.getTabAt(1)?.removeBadge()
                    } else {
                        binding.inventoryHomeTabLayout.getTabAt(1)?.orCreateBadge?.number = it.productList.size
                    }
                }
            }
        })
    }

    fun onBackPressed() {
        val currentFragment = adapter?.getItem(viewPager.currentItem)
        if (currentFragment is InventoryFragment) {
            currentFragment.onBackPressed()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = FragmentInventoryHomeBinding.inflate(inflater, container, false)
        viewPager = binding.inventoryHomeViewpager
        adapter = InventoryHomePagerAdapter(getFragmentList(), childFragmentManager)
        viewPager.adapter = adapter
        val position = arguments?.getInt(TAB_POSITION, 0) ?: 0
        setCurrentTab(position)

        binding.menuIcon.apply {
            if (RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()) {
                apply {
                    setImageDrawable(resources.getDrawable(R.drawable.ic_back))

                    setOnClickListener {
                        MainActivity.startActivitySingleTopToTab2(requireActivity(), TabName.HOME)
                    }
                }
            } else {

                setOnClickListener {
                    if (activity != null) {
                        (activity as MainActivity).handleSideMenuIconClick()
                    }
                }
            }
        }

        binding.tvPosIcon.setOnClickListener {
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.INVENTORY)
            AppAnalytics.trackEvent(AnalyticsConst.ENTER_POS_MODE_BUTTON_CLICKED, propBuilder)
            startActivity(Intent(activity, PosActivity::class.java))
        }

        binding.imgRefreshData.setOnClickListener {
            val manualDataRestoreDialog = ManualDataRestoreDialog(activity, activity,3)
            manualDataRestoreDialog.show()
        }

        viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            }

            override fun onPageSelected(position: Int) {
                trackPageChangeEvent(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }

        })
        // to get the number of running-out stock product as livedata
        viewModel.init(true)

        return binding.root
    }

    private fun getFragmentList(): List<Pair<InventoryFragment, String>> {
        var from: String? = null
        var openCatalogBottomSheet : Boolean = false
        arguments?.let {
            if (it.containsKey("from")) {
                from = it.getString("from")
            }

            openCatalogBottomSheet = it.getBoolean(OPEN_CATALOG_BOTTOM_SHEET, false)
            Log.d("OPEN_CATALOG", openCatalogBottomSheet.toString())
        }


        return listOf(
                Pair(InventoryFragment.createInstance(onlyOutStock = false, from = from, normalBackPressedAction = normalBackPressedAction, openCatalog = openCatalogBottomSheet), resources.getString(R.string.all_stocks)),
                Pair(InventoryFragment.createInstance(onlyOutStock=true,from = from, normalBackPressedAction = normalBackPressedAction), resources.getString(R.string.stock_running_out))
        )
    }

    override fun onResume() {
        super.onResume()
        if(MainActivity.isStockClickedFirstTime && !firstEventSent) {
            startSendingEvents = true
            trackPageChangeEvent(0)
            firstEventSent = true
        }
        if(!Utility.isBlank(User.getBusinessId())) {
            val bookEntity = BusinessRepository.getInstance(context).getBusinessByIdSync(User.getBusinessId())
            bookEntity?.let {
                binding.screenTitle.text = it.businessName
            }
        }

        val trxCount = TransactionRepository.getInstance(context).cashTransactionCountWithDeletedRecords
        if (trxCount > FeaturePrefManager.getInstance().posTooltipTarget &&
            !AppConfigManager.getInstance().posStokIntroTooltipShown
        ) {
            if (activity != null && context != null) {
                showTooltip(
                    requireActivity(), binding.tvPosIcon,
                    requireContext().getString(R.string.pos_intro_tooltip),
                    Gravity.BOTTOM
                )
                AppConfigManager.getInstance().posStokIntroTooltipShown = true
            }
        }
    }

    private fun trackPageChangeEvent(position: Int) {
        if(!MainActivity.isStockClickedFirstTime) return
        val propBuilder = PropBuilder()
        if (position == 0) {
            propBuilder.put("active_tab", "all_products")
        } else {
            propBuilder.put("active_tab", "critical_stock")
        }
        AppAnalytics.trackEvent(AnalyticsConst.INVENTORY_VIEW_PRODUCT_EVENT, propBuilder)

    }

    fun productAddedSuccess() {
        if (::viewPager.isInitialized) {
            viewPager.currentItem = 0
        } else {
            viewPager = binding.inventoryHomeViewpager
            viewPager.currentItem = 0
        }
    }

    fun setCurrentTab(i: Int) {
        if (i < adapter?.count ?: 0) {
            viewPager.currentItem = i
        } else {
            viewPager.currentItem = 0
        }
    }

    companion object {
        private const val TAB_POSITION = "tabPosition"
        private const val FROM = "from"
        const val OPEN_CATALOG_BOTTOM_SHEET = "open_catalog_bottom_sheet"

        @JvmStatic
        fun instance(position: Int, from: String?, openCatalog : Boolean = false, normalBackPressedAction: () -> Unit) = InventoryHomeFragment().apply {
            arguments = Bundle().apply {
                putInt(TAB_POSITION, position)
                putString(FROM, from)
                putBoolean(OPEN_CATALOG_BOTTOM_SHEET, openCatalog)
            }
            this.normalBackPressedAction = normalBackPressedAction
        }
    }

}