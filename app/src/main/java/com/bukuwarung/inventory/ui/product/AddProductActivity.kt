package com.bukuwarung.inventory.ui.product

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.bukuwarung.R
import com.bukuwarung.databinding.AddProductActivityBinding
import com.bukuwarung.utils.InputUtils
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddProductActivity : FragmentActivity() {

    private lateinit var binding: AddProductActivityBinding

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        binding = AddProductActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.ivClose.setOnClickListener {
            InputUtils.hideKeyboard(this)
            onBackPressed()
        }
        binding.back.setOnClickListener {
            InputUtils.hideKeyboard(this)
            onBackPressed()
        }

        supportFragmentManager.beginTransaction().add(R.id.container,
                AddProductFragment.instance(intent.extras?.getString(BOOK_ID) ?: "")).commit()

    }

    companion object {
        const val BOOK_ID = "book_id"
        @JvmStatic
        val PRODUCT_ADDED_SUCCESS = 100

    }

}