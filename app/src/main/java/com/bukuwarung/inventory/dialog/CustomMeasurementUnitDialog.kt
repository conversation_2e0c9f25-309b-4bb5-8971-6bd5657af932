package com.bukuwarung.inventory.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogMeasurementUnitBinding

class CustomMeasurementUnitDialog(
    context: Context, val addMeasurementDialogInterface: AddMeasurementInterface
) : BaseDialog(context) {
    private lateinit var binding: DialogMeasurementUnitBinding

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = DialogMeasurementUnitBinding.inflate(LayoutInflater.from(context))
        setupViewBinding(binding.root)
        setupView()
    }

    private fun setupView() {
        binding.btnCancel.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CANCEL_CREATE_NEW)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
            dismiss() }
        binding.btnSave.setOnClickListener {
            val newName = binding.etUnitName.text.toString()
            if (newName.isNotEmpty()) {
                addMeasurementDialogInterface.addMeasurement(newName)
            }
        }
    }

    interface AddMeasurementInterface {
        fun addMeasurement(measurement: String)
        fun dismiss()
    }
}