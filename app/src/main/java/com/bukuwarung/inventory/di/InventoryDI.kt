package com.bukuwarung.inventory.di

import com.bukuwarung.activities.catalogproduct.viewmodel.ProductCatalogUseCase
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.AppDatabase
import com.bukuwarung.database.dao.InventoryDao
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.inventory.InventoryProductProductRepository
import com.bukuwarung.inventory.InventoryProductRepo
import com.bukuwarung.inventory.StockUnitRepo
import com.bukuwarung.inventory.StockUnitRepository
import com.bukuwarung.inventory.datastore.ProductDataStore
import com.bukuwarung.inventory.datastore.ProductLocalDataStore
import com.bukuwarung.inventory.datastore.StockUnitDataStore
import com.bukuwarung.inventory.datastore.StockUnitLocalDataStore
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.session.SessionManager
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.Application
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import android.content.Context
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

@Module
@InstallIn(SingletonComponent::class)
class InventoryDI {

    @Provides
    fun provideProductDao(appDatabase: AppDatabase): ProductDao {
        return appDatabase.productDao()
    }

    @Provides
    fun provideInventoryDao(appDatabase: AppDatabase): InventoryDao {
        return appDatabase.inventoryDao()
    }

    @Provides
    fun provideProductDataStore(productDao: ProductDao): ProductLocalDataStore {
        return ProductDataStore(productDao)
    }

    @Provides
    fun provideStockUnitDataStore(inventoryDao: InventoryDao): StockUnitLocalDataStore {
        return StockUnitDataStore(inventoryDao)
    }

    @Provides
    fun provideProductRepo(productLocalDataStore: ProductLocalDataStore): InventoryProductRepo = InventoryProductProductRepository(productLocalDataStore)

    @Provides
    fun provideFeatureViewModelFactory(productInventory: ProductInventory, businessRepository: BusinessRepository, catalogUseCase: ProductCatalogUseCase, sessionManager: SessionManager, productCategoryUseCase: ProductCategoryUseCase) =
            InventoryViewModelFactory(productInventory, businessRepository, catalogUseCase, sessionManager, productCategoryUseCase)

    @Provides
    fun provideStockUnitRepo(stockUnitLocalDataStore: StockUnitLocalDataStore): StockUnitRepo {
        return StockUnitRepository(stockUnitLocalDataStore)
    }

    @Provides
    fun provideBackgroundDispatcher(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    fun provideProductInventory(
        inventoryProductRepo: InventoryProductRepo,
        productRepository: ProductRepository,
        sessionManager: SessionManager
    ) = ProductInventory(inventoryProductRepo, productRepository, sessionManager)

    @Provides
    fun provideCashListViewModelFactory(@ApplicationContext context: Context, inventory: ProductInventory): CashListViewModelFactory {
        return CashListViewModelFactory(context as Application, inventory)
    }
}