package com.bukuwarung.services

import android.app.Service
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi

/**
 * Base class for foreground services that need to handle Android 15+ timeout behavior
 */
abstract class BaseTimeoutForegroundService : Service() {
    
    companion object {
        private const val TAG = "BaseTimeoutForegroundService"
    }
    
    /**
     * Called when the service times out (Android 15+)
     * Services have a few seconds to call stopSelf() after this is called
     */
    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    override fun onTimeout(startId: Int, fgsType: Int) {
        Log.w(TAG, "Service timeout occurred for startId: $startId, fgsType: $fgsType")
        
        try {
            // Perform cleanup operations
            onServiceTimeout(startId, fgsType)
            
            // Stop the service
            stopSelf()
            
            Log.i(TAG, "Service stopped successfully after timeout")
        } catch (e: Exception) {
            Log.e(TAG, "Error during service timeout handling", e)
            // Still try to stop the service
            stopSelf()
        }
    }
    
    /**
     * Override this method to handle service-specific cleanup when timeout occurs
     */
    protected open fun onServiceTimeout(startId: Int, fgsType: Int) {
        // Default implementation - subclasses can override
        Log.i(TAG, "Service timeout - performing default cleanup")
    }
    
    /**
     * Check if the service is running on Android 15+ and may be subject to timeout
     */
    protected fun isSubjectToTimeout(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM
    }
    
    /**
     * Utility method to safely stop the service with proper logging
     */
    protected fun stopServiceSafely() {
        try {
            stopSelf()
            Log.i(TAG, "Service stopped successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping service", e)
        }
    }
}
