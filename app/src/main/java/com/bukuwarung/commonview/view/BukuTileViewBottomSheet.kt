package com.bukuwarung.commonview.view


import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.BukuTileViewBottomSheetBodyBlock
import com.bukuwarung.activities.homepage.data.BukuTileViewBottomSheetCoachMark
import com.bukuwarung.activities.homepage.view.NoInternetAvailableDialog
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.commonview.adapter.BukuTileBottomSheetItemAdapter
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.HOMEPAGE_SECTION
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.LayoutBukuTileBottomsheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.ppob.base.listeners.PpobProductsListener
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.tutor.view.OnboardingWidget.OnboardingWidgetListener
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getFragmentManager
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.returnObject
import com.bukuwarung.utils.setDrawableRightListener
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import java.util.LinkedList
import java.util.Queue
import javax.inject.Inject

@AndroidEntryPoint
class BukuTileViewBottomSheet : BaseBottomSheetDialogFragment(), OnboardingWidgetListener, Navigator {

    companion object {
        private const val PPOB = "ppob"
        const val TAG = "BukuTileViewBottomSheet"
        private const val CATEGORY = "category"
        private const val IS_PAYMENT_SCREEN = "is_payment_screen"
        private const val SHOW_TOOLS_SECTION = "show_tools_section"
        private const val ATUR_JADWAL_PENCATATAN = "Atur Jadwal Pencatatan"
        private const val ATUR_INFO_NOTA = "Atur Info Nota"
        private const val BUAT_KARTU_NAMA = "Buat Kartu Nama"

        fun createInstance(
            category: String?, isPaymentsScreen: Boolean, showToolsSection:Boolean = true
        ) = BukuTileViewBottomSheet().apply {
            arguments = Bundle().apply {
                putString(CATEGORY, category)
                putBoolean(IS_PAYMENT_SCREEN, isPaymentsScreen)
                putBoolean(SHOW_TOOLS_SECTION, showToolsSection)
            }
        }
    }



    private var _binding: LayoutBukuTileBottomsheetBinding? = null
    private val binding get() = _binding!!
    private val category by lazy { arguments?.getString(CATEGORY).orEmpty() }
    private val coachMarkInfoQueue: Queue<BukuTileViewBottomSheetCoachMark> = LinkedList(emptyList())
    var onboardingWidget: OnboardingWidget? = null
    private val coachMarKDelayTimeInMillis = 500L
    private val listOfOfflineFeatures =
        listOf(ATUR_JADWAL_PENCATATAN, ATUR_INFO_NOTA, BUAT_KARTU_NAMA)

    @Inject
    lateinit var neuro: Neuro
    private var ppobProductsListener: PpobProductsListener? = null
    private val isPaymentsScreen by lazy { arguments?.getBoolean(IS_PAYMENT_SCREEN) ?: false }
    private val showToolsSection by lazy { arguments?.getBoolean(SHOW_TOOLS_SECTION) ?: false }

    override fun onAttach(context: Context) {
        super.onAttach(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LayoutBukuTileBottomsheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }
        with(binding) {
            rvPpob.layoutManager = LinearLayoutManager(context)
            val bottomSheetJson: String = if (isPaymentsScreen) {
                RemoteConfigUtils.PembayaranTabConfig.getPaymentsBottomSheetBody()
            } else {
                RemoteConfigUtils.NewHomePage.getHomePageBottomSheetBody(category)
            }
            val type = object  : TypeToken<BukuTileViewBottomSheetBodyBlock>() {}.type
            val bottomSheetData: BukuTileViewBottomSheetBodyBlock? = type.returnObject(bottomSheetJson)

            tvTitle.text = if (!showToolsSection) getString(R.string.other_products_label) else bottomSheetData?.title
            tvTitle.visibility = bottomSheetData?.title.isNotNullOrBlank().asVisibility()
            tvTitle.setDrawableRightListener {
                dismiss()
            }

            tvSubTitle.text = bottomSheetData?.subTitle
            tvSubTitle.visibility = if (showToolsSection) bottomSheetData?.subTitle.isNotNullOrBlank().asVisibility() else View.INVISIBLE
            val fragmentBodyList =
                if (isPaymentsScreen && !showToolsSection) bottomSheetData?.fragmentBodyBlockList?.filter {
                    it.body_block_name != "promotions_ppob_body"
                }.orEmpty() else bottomSheetData?.fragmentBodyBlockList.orEmpty()
            rvPpob.adapter =
                BukuTileBottomSheetItemAdapter(
                    fragmentBodyList,
                    category,
                    { fragmentBody ->
                        if (isPaymentsScreen) {
                            ppobProductsListener?.onPpobSelected(fragmentBody)
                        } else {
                            processRedirectAndAnalytics(fragmentBody, category)
                        }
                        dismiss()
                    },
                    {
                        addCoachMarkInfoToQueue(it)
                    }
                )
        }
    }

    private fun scrollToPosition(position: Int) {
        (binding.rvPpob.layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(position, 0)
    }

    private fun processRedirectAndAnalytics(fragmentBody: BodyBlock?, category: String) {
        // Redirection/ deeplink/ flow
        val newRedirection = fragmentBody?.deeplinkAppNeuro.orEmpty()
        val redirection = fragmentBody?.deeplink_app.orEmpty()
        val deeplink = fragmentBody?.deeplink_web.orEmpty()
        when {
            newRedirection.isNotBlank() -> {
                redirect(newRedirection, category)
            }
            newRedirection.isBlank() && redirection.isNotBlank() -> {
                if (listOfOfflineFeatures.any {
                        it.equals(fragmentBody?.display_name, ignoreCase = true)
                    }) {
                    if (!Utility.hasInternet()) {
                        context?.getFragmentManager()?.let { NoInternetAvailableDialog.show(it) }
                    } else {
                        redirect(redirection, category)
                    }
                } else {
                    redirect(redirection, category)
                }
            }
            deeplink.isNotBlank() -> {
                if (Utility.hasInternet()) {
                    if (fragmentBody?.analytics_name?.contains("reward")!!) {
                        val webViewIntent = CustomWebviewActivity.createIntent(
                            activity,
                            deeplink, "Integrasi Institusi Keuangan", false,
                            "loyalty_account", "homepage"
                        )
                        startActivity(webViewIntent)
                    } else {
                        startActivity(
                            WebviewActivity.createIntent(
                                requireActivity(), deeplink,
                                fragmentBody.display_name
                            )
                        )
                    }
                } else {
                    dismiss()
                    context?.getFragmentManager()?.let { NoInternetAvailableDialog.show(it) }

                }
            }
        }
        handleAnalytics(fragmentBody, category)

    }

    private fun handleAnalytics(fragmentBody: BodyBlock?, category: String) {
        val prop = AppAnalytics.PropBuilder()

        prop.put(HOMEPAGE_SECTION, category)
        prop.put(AnalyticsConst.HomePage.BUTTON_NAME, fragmentBody?.analytics_name)
        prop.put(AnalyticsConst.HomePage.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        prop.put(AnalyticsConst.HomePage.HOMEPAGE_PPOB_PROMO_TAG_ENABLED, fragmentBody?.is_promo)
        prop.put(
            "entry_point_detail",
            AnalyticsConst.HomePage.HOMEPAGE.plus("_").plus(category).plus("_bottomsheet")
        )
        AppAnalytics.trackEvent(AnalyticsConst.HomePage.EVENT_HOMEPAGE_BUTTON_CLICK, prop)
    }

    private fun redirect(redirection: String, category: String) {
        if (category == PPOB && PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB)) {
            PaymentUtils.showKycKybStatusBottomSheet(parentFragmentManager, AnalyticsConst.HOME_PAGE)
            return
        }
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$redirection&from=home&launch=1"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun showCoachmarkWidget(coachMarkInfo: BukuTileViewBottomSheetCoachMark) {
        if(!isBottomSheetExpanded()) return
        coachMarkInfo.fragmentBlock.id?.let {
            if (!OnboardingPrefManager.getInstance().getHasFinishedForId(it)) {
                scrollToPosition(coachMarkInfo.anchorViewPosition)
                onboardingWidget = activity?.let { activity ->
                    OnboardingWidget.createInstance(
                        activity = activity,
                        listener = this,
                        id = it,
                        anchor = coachMarkInfo.anchorView,
                        res = null,
                        headerText = coachMarkInfo.fragmentBlock.coachmark_header.orEmpty(),
                        body = coachMarkInfo.fragmentBlock.coachmark_body.orEmpty(),
                        buttonText = "",
                        focusGravity = FocusGravity.CENTER,
                        shape = ShapeType.RECTANGLE,
                        currentStep = coachMarkInfo.fragmentBlock.coachmark_step ?: 1,
                        maxSteps = coachMarkInfo.fragmentBlock.coachmark_max_steps ?: 1,
                        sendAnalytics = true,
                        sendAnalyticsOnDismiss = true,
                        delay = 0,
                        view = binding.rvPpob.rootView as? ViewGroup
                    )
                }
            }
        }
    }

    private fun isBottomSheetExpanded(): Boolean{
        val bottomSheetDialog = dialog as? BottomSheetDialog
        return bottomSheetDialog?.behavior?.state == BottomSheetBehavior.STATE_EXPANDED
    }

    private fun addCoachMarkInfoToQueue(coachMarkInfo: BukuTileViewBottomSheetCoachMark) {
        coachMarkInfoQueue.add(coachMarkInfo)
        Handler().postDelayed(
            { if (onboardingWidget == null) showCoachmarkWidget(coachMarkInfoQueue.remove()) },
            coachMarKDelayTimeInMillis
        )
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
        if(coachMarkInfoQueue.isNotEmpty()){
            showCoachmarkWidget(coachMarkInfoQueue.remove())
        }
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
    }

    override fun onDestroyView() {
        super.onDestroyView()
        onboardingWidget?.dismiss(
            isFromButton = false,
            isFromCloseButton = false,
            isFromOutside = true
        )
        onboardingWidget = null
        _binding = null
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
    fun setPpobProductsListener(listener: PpobProductsListener?){
        ppobProductsListener = listener
    }
}
