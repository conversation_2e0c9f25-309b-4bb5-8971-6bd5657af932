package com.bukuwarung.commonview.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.databinding.ItemBukuTileBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFalseOrNull
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.listen
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable

class BukuTileBottomSheetAdapter(private val category: String, private var bukuTileContent: ArrayList<BodyBlock?>, val getOnClickData: (BodyBlock?, String) -> Unit): RecyclerView.Adapter<BukuTileBottomSheetAdapter.BukuTileViewHolder>()  {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BukuTileBottomSheetAdapter.BukuTileViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemBukuTileBinding.inflate(inflater, parent, false)
        return BukuTileViewHolder(binding).listen { position, type ->
            getOnClickData(bukuTileContent[position], category)
        }
    }

    override fun getItemCount(): Int {
        return bukuTileContent.size
    }

    inner class BukuTileViewHolder constructor(private val binding: ItemBukuTileBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        fun bind(fragmentBody: BodyBlock?) {
            Glide.with(binding.root.context)
                .load(fragmentBody?.icon)
                .placeholder(shimmerDrawable)
                .centerInside()
                .into(binding.ivTileImage)
            binding.tvTileName.text = fragmentBody?.display_name

            val shouldShowPromoTags = RemoteConfigUtils.NewHomePage.shouldShowPromoTags()

            if (shouldShowPromoTags) {
                if (fragmentBody?.is_new.isTrue && fragmentBody?.is_promo.isTrue) {
                    binding.tvNew.hideView()
                    binding.tvPromo.showView()
                } else {
                    binding.tvPromo.hideView()
                }

                if (fragmentBody?.is_promo.isFalseOrNull && fragmentBody?.is_new.isTrue) {
                    binding.tvNew.showView()
                } else {
                    binding.tvNew.hideView()
                }
            } else {
                binding.tvPromo.hideView()
                if (fragmentBody?.is_new.isTrue) {
                    binding.tvNew.showView()
                } else {
                    binding.tvNew.hideView()
                }
            }

            if (fragmentBody?.coming_soon.isTrue) {
                binding.tvComingSoon.showView()
                binding.cvTileImage.setCardBackgroundColor(
                    ContextCompat.getColor(
                        binding.root.context,
                        R.color.coming_soon
                    )
                )
            } else {
                binding.tvComingSoon.hideView()
            }
        }
    }

    override fun onBindViewHolder(holder: BukuTileViewHolder, position: Int) {
        holder.bind(bukuTileContent[position])
    }

}