package com.bukuwarung.activities.catalogproduct.view

import android.content.Context
import android.content.Intent
import android.text.SpannableStringBuilder
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.catalogproduct.adapter.CatalogProductAdapter
import com.bukuwarung.activities.catalogproduct.data.model.CatalogSubCategory
import com.bukuwarung.activities.catalogproduct.viewmodel.CatalogViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityProductCatalogBinding
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.boldText
import com.bukuwarung.utils.setupForSearch
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.google.android.material.textfield.TextInputLayout
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CatalogProductActivity : BaseActivity() {
    private lateinit var binding: ActivityProductCatalogBinding
    private val categoryName: String by lazy { intent.getStringExtra(CATEGORY_NAME) ?: "" }
    private var subCategories = ArrayList<CatalogSubCategory>()

    private val vm: CatalogViewModel by viewModels()
    private var productQuery: String = ""

    private val productAdapter = CatalogProductAdapter {
        vm.onEventReceived(CatalogViewModel.Event.OnSelectedProductsChanged(it))
    }

    override fun setupView() {
        binding = ActivityProductCatalogBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setUpToolbarWithHomeUp(binding.toolbar)
        supportActionBar?.title = categoryName

        vm.onEventReceived(CatalogViewModel.Event.OnCategoryNameSet(categoryName))

        binding.apply {

            btnFilter.setOnClickListener {
                openSubCatalogFilter()
            }

            etSearch.apply {
                fun submitQuery(newQuery: String) {
                    if (newQuery == productQuery) return

                    productQuery = newQuery
                    vm.onEventReceived(CatalogViewModel.Event.OnQueryTextChanged(newQuery))

                    // call notifyDataSetChanged() to update bold text based on new Query
                    productAdapter.apply {
                        updateQuery(newQuery)
                    }

                    val eventProp = AppAnalytics.PropBuilder().put(
                        AnalyticsConst.ENTRY_POINT,
                        AnalyticsConst.ProductCatalog.IMPORT_PRODUCT_CATALOG
                    )
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_SEARCH_PERFORMED, eventProp)
                }

                setupForSearch(lifecycleScope) { newQuery ->
                    submitQuery(newQuery)
                }

                // add action when endIcon (clear_text) is clicked
                (parent.parent as TextInputLayout).setEndIconOnClickListener {
                    setText("")
                    submitQuery(text.toString())
                }
            }

            rvProduct.apply {
                layoutManager = LinearLayoutManager(this@CatalogProductActivity)
                itemAnimator = DefaultItemAnimator()
                adapter = productAdapter
                addItemDecoration(
                    DividerItemDecoration(
                        this@CatalogProductActivity,
                        RecyclerView.VERTICAL
                    )
                )
            }

            btnSubmit.setOnClickListener {
                vm.onEventReceived(CatalogViewModel.Event.OnSubmitProduct)
            }

        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(vm.state) { state ->
            when (state) {
                is CatalogViewModel.State.SetSelectedSubCategory -> showSelectedSubCategory(state.subCategory)
                is CatalogViewModel.State.SetSubCategories -> {
                    showSelectedSubCategory(state.subCategories[0])
                    subCategories = state.subCategories
                }
                is CatalogViewModel.State.SetSelectedProducts -> {
                    updateButtonState(state.products.isNotEmpty(), state.products.size.toString())
                }
                CatalogViewModel.State.ProductsAddedToInventory -> {
                    finish()
                }
            }
        }

        vm.productData.observe(this) {
            lifecycleScope.launch {
                productAdapter.submitData(it)
            }
        }

        vm.pagingStatus.observe(this, Observer { status ->
            when (status) {
                PagingStatus.Loading, PagingStatus.LoadingNextPage -> {

                }
                is PagingStatus.Loaded, PagingStatus.EmptyNextPage -> {
                    binding.apply {
                        rvProduct.visibility = View.VISIBLE
                        placeholder.productPlaceholder.visibility = View.GONE
                        errorScreen.visibility = View.GONE
                    }
                }
                PagingStatus.Empty -> {
                    binding.apply {
                        rvProduct.visibility = View.GONE
                        placeholder.productPlaceholder.visibility = View.GONE

                        errorScreen.apply {
                            visibility = View.VISIBLE
                            setup {
                                setImageRes { R.drawable.stock_list_empty_image }
                                setMessage { context.getString(R.string.product_not_found_try_other_product) }
                            }
                        }
                    }
                }
                is PagingStatus.Error -> {
                    binding.apply {
                        rvProduct.visibility = View.GONE
                        placeholder.productPlaceholder.visibility = View.GONE

                        errorScreen.apply {
                            visibility = View.VISIBLE
                            setup {
                                setImageRes { R.drawable.stock_list_empty_image }
                                setTitle { getString(R.string.error_happened) }
                                setMessage { status.errorMessage }
                                setButtonTextAndAction(getString(R.string.reload)) {
                                    {
                                        vm.invalidateDataSource()
                                    }
                                }
                            }
                        }
                    }

                    vm.invalidateDataSource()
                }
                PagingStatus.NoInternet -> {
                    binding.apply {
                        rvProduct.visibility = View.GONE
                        placeholder.productPlaceholder.visibility = View.GONE

                        errorScreen.apply {
                            visibility = View.VISIBLE
                            setup {
                                setImageRes { R.drawable.ic_no_inet }
                                setTitle { getString(R.string.no_connection_title) }
                                setMessage { getString(R.string.payment_tab_error_state_message) }
                                setButtonTextAndAction(getString(R.string.reload)) {
                                    {
                                        // Try again if connection is established
                                        if (Utility.hasInternet()) {
                                            vm.invalidateDataSource()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        })
    }

    private fun updateButtonState(_isEnabled: Boolean, count: String?) {
        binding.btnSubmit.apply {
            isEnabled = _isEnabled

            text = if (isEnabled) {
                getString(R.string.add_product_placeholder, count.toString())
            } else {
                getString(R.string.add_product)
            }
        }
    }

    private fun openSubCatalogFilter() {
        AppAnalytics.trackEvent(AnalyticsConst.ProductCatalog.FILTER_PRODUCT_CLICK)

        CatalogFilterBottomSheet(this, subCategories, vm.getSelectedCategory()?.name) {
            vm.onEventReceived(CatalogViewModel.Event.OnSubCategoryChanged(it))
            productAdapter.apply {
                clearSelectedProduct()
            }
            Log.d("Catalog Category", it.name)
        }.show()
    }

    private fun showSelectedSubCategory(subCategory: CatalogSubCategory) {
        binding.tvSubCategory.text =
            SpannableStringBuilder("${subCategory.name} (${subCategory.count})").boldText(
                subCategory.name
            )
    }

    companion object {
        private const val CATEGORY_NAME = "CATEGORY_NAME"

        fun createIntent(context: Context, catalogCategoryName: String = ""): Intent {
            return Intent(context, CatalogProductActivity::class.java).apply {
                putExtra(CATEGORY_NAME, catalogCategoryName)
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }

    override fun setViewBinding() {
        // Not necessary as we already using delegated viewBinding :p
    }
}