package com.bukuwarung.activities.navigation.viewmodel;

import android.app.Application;
import android.content.Context;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.session.User;

import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.lifecycle.HiltViewModel;

@HiltViewModel
public final class BusinessViewModel extends AndroidViewModel {

    private LiveData<List<BookEntity>> businessEntityLiveData;
    private LiveData<BookEntity> businessLiveData;
    Context context;

    @Inject
    public BusinessViewModel(Application application) {
        super(application);
        context = application.getApplicationContext();
        this.businessEntityLiveData = BusinessRepository.getInstance(context).getBusinessList(User.getUserId());
        this.businessLiveData = BusinessRepository.getInstance(context).getBusinessById(User.getBusinessId());
    }

    public final LiveData<List<BookEntity>> getBusinessList() {
        return this.businessEntityLiveData;
    }

    public LiveData<BookEntity> getBusinessLiveData() {
        return businessLiveData;
    }

    public void saveUpdateBookEntity(BookEntity bookEntity) {
        BusinessRepository.getInstance(context).insertBookSync(bookEntity);
    }
}
