package com.bukuwarung.activities.home;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2;
import static com.bukuwarung.constants.AnalyticsConst.EVENT_SUCCESS_REGISTRATION;
import static com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL;
import static com.bukuwarung.lib.webview.util.Constant.EVENT_PROPS;
import static com.bukuwarung.lib.webview.util.Constant.KYB_SUBMITTED;
import static com.bukuwarung.lib.webview.util.Constant.KYC_SUBMITTED;
import static com.bukuwarung.lib.webview.util.Constant.QRIS_SUBMITTED;
import static com.bukuwarung.preference.FeaturePrefManager.USING_DAILY_BUSINESS_RECAP;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.content.res.Configuration;
import android.graphics.drawable.Icon;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModelProviders;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.appsflyer.AppsFlyerLib;
import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.R;
import com.bukuwarung.activities.EventLoggingReceiver;
import com.bukuwarung.activities.ExceptionLoggingReceiver;
import com.bukuwarung.activities.HelpCenterActivity;
import com.bukuwarung.activities.UrlReceiver;
import com.bukuwarung.activities.WebviewActivity;
import com.bukuwarung.activities.business.CreateBusinessActivity;
import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.activities.customer.TransactionTabHome;
import com.bukuwarung.activities.dailyupdatestates.DailyUpdateNoTxnActivity;
import com.bukuwarung.activities.expense.CashListViewModel;
import com.bukuwarung.activities.expense.CashListViewModelFactory;
import com.bukuwarung.activities.expense.IncomeExpenseTab;
import com.bukuwarung.activities.expense.NewCashTransactionActivity;
import com.bukuwarung.activities.home.constants.MainActivityConstants;
import com.bukuwarung.activities.home.data.AppUpdateBlock;
import com.bukuwarung.activities.home.data.BusinessListObserver;
import com.bukuwarung.activities.home.data.LoadStickerListAsync;
import com.bukuwarung.activities.homepage.view.HomeFragment;
import com.bukuwarung.activities.navigation.SideNavActivity;
import com.bukuwarung.activities.navigation.adapter.NavigationAdapter;
import com.bukuwarung.activities.navigation.viewmodel.BusinessViewModel;
import com.bukuwarung.activities.onboarding.InAppTnCBottomSheet;
import com.bukuwarung.activities.onboarding.LoginActivity;
import com.bukuwarung.activities.onboarding.NewLoginActivity;
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel;
import com.bukuwarung.activities.payment.PaymentTabFragment;
import com.bukuwarung.activities.profile.ProfileTabFragment;
import com.bukuwarung.activities.profile.ProfileTabViewModel;
import com.bukuwarung.activities.profile.businessprofile.BusinessProfileWebviewActivity;
import com.bukuwarung.activities.profile.businessprofile.CreateBusinessProfileActivity;
import com.bukuwarung.activities.profile.update.BusinessProfileFormViewModel;
import com.bukuwarung.activities.referral.leaderboard.LeaderboardWebviewActivity;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.transaction.customer.ProfileCompletionDialog;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.appsflyer.AppsFlyerViewModel;
import com.bukuwarung.baseui.DefaultViewPager;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.PaymentConst;
import com.bukuwarung.constants.RemoteConfigConst;
import com.bukuwarung.controllers.installreferrer.InstallReferrerController;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.utils.CoroutineHelper;
import com.bukuwarung.dialogs.login.LoginBottomSheetDialog;
import com.bukuwarung.dialogs.login.VerifyOtpBottomSheetDialog;
import com.bukuwarung.dialogs.transactions_pop_up_dialog.TransactionsPopUpDialog;
import com.bukuwarung.domain.notification.PostFcmTokenUseCase;
import com.bukuwarung.enums.GamifyTarget;
import com.bukuwarung.inventory.ui.InventoryHomeFragment;
import com.bukuwarung.inventory.usecases.ProductInventory;
import com.bukuwarung.managers.deeplink.DeepLinkData;
import com.bukuwarung.managers.deeplink.DeepLinkManager;
import com.bukuwarung.managers.deeplink.converters.UriToDeepLinkDataConverter;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.neuro.api.SourceLink;
import com.bukuwarung.payments.bottomsheet.KycKybBottomSheet;
import com.bukuwarung.payments.bottomsheet.KycProcessingBottomSheet;
import com.bukuwarung.payments.constants.KybStatus;
import com.bukuwarung.payments.constants.KycStatus;
import com.bukuwarung.payments.constants.KycStatusMetadata;
import com.bukuwarung.payments.constants.KycTier;
import com.bukuwarung.payments.data.model.PaymentMetadata;
import com.bukuwarung.payments.pref.PaymentPrefManager;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.AppsFlyerPrefManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.setup.ImplicitSchemeDeepLinkHandler;
import com.bukuwarung.setup.Setup;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.tracking.TraceConstants;
import com.bukuwarung.tutor.prefs.PreferencesManager;
import com.bukuwarung.utils.CollectionUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.LoginUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.PlaystoreLinkBuilder;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.bukuwarung.utils.EdgeToEdgeHelper;
import com.bumptech.glide.Glide;
import com.facebook.FacebookSdk;
import com.facebook.applinks.AppLinkData;
import com.google.android.material.badge.BadgeDrawable;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.snackbar.SnackbarContentLayout;
import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.install.model.AppUpdateType;
import com.google.android.play.core.install.model.UpdateAvailability;
import com.google.android.gms.tasks.Task;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.perf.metrics.AddTrace;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import kotlin.Unit;


//TODO: when all tabs have the same base fragment, need to create new base specifically for main tabs, as there are lots of duplicate codes to handle comm between mainActivity & tabs
@AndroidEntryPoint
public class MainActivity extends AppActivity implements BottomNavigationView.OnNavigationItemSelectedListener, View.OnClickListener,
        ProfileTabFragment.ProfileTabFragmentListener, PaymentTabFragment.PaymentTabFragmentListener, LoginBottomSheetDialog.LoginSheetListener,
        VerifyOtpBottomSheetDialog.OtpSheetListener, IncomeExpenseTab.IncomeExpenseTabListener, CustomerTab.CustomerTabListener,
        HomeFragment.HomeFragmentBackListener {

    private final Handler handler = new Handler();

    public DrawerLayout homelayout;
    private TabNavigationAdapter tabNavigationAdapter;
    private BottomNavigationView bottomNavigationView;
    private ActionBarDrawerToggle sideMenuToggle;
    private ImageView sideMenuBanner;
    private NavigationAdapter navAdapter;
    private RecyclerView navigationList;
    private BusinessViewModel businessViewModel;
    private boolean showPayments = false;
    public CoroutineHelper coroutineHandler = new CoroutineHelper();

    @Inject
    Neuro neuro;

    @Inject
    CashListViewModelFactory cashListViewModelFactory;
    private CashListViewModel cashListViewModel;
    private DetailedBusinessFormViewModel businessFormViewModel;
    private ProfileTabViewModel profileTabViewModel;
    @Inject
    AppsFlyerViewModel appsFlyerViewModel;
    BusinessProfileFormViewModel businessProfileFormViewModel;
    @Inject
    ProductInventory productInventory;
    @Inject
    ImplicitSchemeDeepLinkHandler implicitSchemeDeepLinkHandler;
    @Inject
    PostFcmTokenUseCase postFcmTokenUseCase;
    private DefaultViewPager tabPager;
    private CustomerTab customerTab;
    private ProfileTabFragment profileTabFragment;
    private PaymentTabFragment paymentTabFragment;
    private TransactionTabHome incomeExpenseTab;
    private IncomeExpenseTab transactionTab;
    private HomeFragment homeFragment;

    private InventoryHomeFragment inventoryHomeFragment;

    private MaterialButton btnCreateBusineess;

    private DeepLinkManager deepLinkManager;
    private InstallReferrerController installReferrerController;
    private UriToDeepLinkDataConverter converter;

    private Runnable appUpdateRunnable = this::requestAppUpdate;

    private CompositeDisposable compositeDisposable;
    private LoginBottomSheetDialog loginBtSheet;
    private VerifyOtpBottomSheetDialog otpBtSheet;
    private String currentEntryPoint = "";
    private boolean doubleTapToExitPressedOnce = false;

    private TransactionsPopUpDialog transactionsPopUpDialog;
    private List<TabName> tabNames = new ArrayList<>();
    public static boolean isStockClicked = false;
    private boolean firstCallOfOnSelect = false;
    private static boolean isAppStarted = true;
    private static boolean isUtangClicked = true;
    private static boolean isTransksiClicked = true;
    private static boolean isPembarayanClicked = true;
    private static boolean isProfileClicked = true;

    public static boolean isStockClickedFirstTime = false;
    private static final int PRODUCT_ADDED_SUCCESS = 100;
    private static boolean displayStockBadge = false;

    private int subTabId = 0;
    private boolean mergedTab = RemoteConfigUtils.TransactionTabConfig.INSTANCE.mergedTab();
    private String hamburgerMenuBannerUrl = "";
    private Boolean shouldShowHamburgerMenuBanner = true;
    private String hamburgerMenuBannerLanding = "";
    private SharedPreferences.OnSharedPreferenceChangeListener listener;

    private InAppTnCBottomSheet inAppTnCBottomSheet;
    private String referralCodeFromDeeplink = "";
    private boolean userProfileObserved = false;
    private EventLoggingReceiver eventLoggingReceiver = new EventLoggingReceiver();
    private ExceptionLoggingReceiver exceptionLoggingReceiver = new ExceptionLoggingReceiver();
    private UrlReceiver urlReceiver = new UrlReceiver();

    private final ActivityResultLauncher<String> requestPermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.RequestPermission(), isGranted -> {
                if (isGranted) {
                    // FCM SDK (and your app) can post notifications.
                } else {
                    askNotificationPermission();
                }
            });

    //Create an instance of HanselActionListener



    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    public static void startActivityAndClearTop(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(intent);

    }

    public static void startActivityAndClearTopAndLoadWebView(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra(context.getString(R.string.loan_book_key), context.getString(R.string.loan_book_value));
        context.startActivity(intent);
//        WebviewActivity.Companion.createIntent(context,RemoteConfigUtils.INSTANCE.getLoanUrl(), "");
    }

    public static void startNewTaskActivity(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void startActivityAndClearTop(Context context, String key, boolean value) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.putExtra(key, value);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(intent);
    }

    public static void startActivityAndClearTopWithTargetTab(Context context, String key, boolean value, TabName tabname) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.putExtra(key, value);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        if (tabname != null) intent.putExtra(MainActivityConstants.TAB_NAME, tabname.name());
        context.startActivity(intent);
    }

    public static void startActivityAndClearTop(Context context, int flag) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(flag);
        context.startActivity(intent);
    }

    public static void startActivitySingleTopToTab(Context context, TabName tabname) {
        startActivitySingleTopToTab(context, tabname, null);
    }

    public static void startActivitySingleTopToTab2(Context context, TabName tabname) {
        startActivitySingleTopToTab2(context, MainActivityConstants.TAB_NAME_HOME, null);
    }

    public static void startActivitySingleTopToTab(Context context, TabName tabname, Bundle bundle) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        if (tabname != null) intent.putExtra(MainActivityConstants.TAB_NAME, tabname.name());
        if (bundle != null) intent.putExtras(bundle);
        context.startActivity(intent);
    }

    public static void startActivitySingleTopToTab2(Context context, String tabname, Bundle bundle) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        if (tabname != null) intent.putExtra("tab_name_home", TabName.HOME.name());
        context.startActivity(intent);
    }

    /*
    refresh all tab data, it's required for txn update case or important setting refresh
     */
    public static void startActivityClearTopToTab(Context context, TabName tabname) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra(MainActivityConstants.TAB_NAME, tabname.name());
        context.startActivity(intent);
    }

    public static RecyclerView getSideNavigationRV(MainActivity mainActivity) {
        return mainActivity.navigationList;
    }

    public static NavigationAdapter getSideNavigationAdapter(MainActivity mainActivity) {
        return mainActivity.navAdapter;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        implicitSchemeDeepLinkHandler.handle(this, intent);
        handleDeepLinks(intent);
        showPayments = true;
        String tabNameString = intent.getStringExtra(MainActivityConstants.TAB_NAME);
        navigateTabFromIntentExtra(tabNameString, intent.getStringExtra(MainActivityConstants.TARGET_TAB));
        if (intent.hasExtra("openShowMore")) {
            if (paymentTabFragment != null && paymentTabFragment.isAdded())
                paymentTabFragment.setShowMore(intent.getBooleanExtra("openShowMore", false));
        }
        if (intent.getBooleanExtra(KYC_SUBMITTED, false)) {
            PaymentPrefManager.Companion.getInstance().setQrisRejectedTs(0, true);
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            AppAnalytics.addExtraEventProps(intent.getStringExtra(EVENT_PROPS), propBuilder);
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_KYC_DATA_SENT, propBuilder);
            KycProcessingBottomSheet kycProcessingBottomSheet = KycProcessingBottomSheet.Companion.createInstance(true);
            kycProcessingBottomSheet.show(getSupportFragmentManager(), KycProcessingBottomSheet.TAG);
            if (paymentTabFragment != null && paymentTabFragment.isAdded())
                paymentTabFragment.setSubmittedKyc();
        } else if (intent.getStringExtra(MainActivityConstants.PRODUCT_NAME) != null) {
            String productName = intent.getStringExtra(MainActivityConstants.PRODUCT_NAME);
            showSnackBarPpob(productName);
        } else if (intent.getBooleanExtra(QRIS_SUBMITTED, false)) {
            // We change active book ID to Qris book if result is 'success'
            String result = intent.getStringExtra(PaymentConst.QRIS_FORM_RESULT);
            String qrisBook = intent.getStringExtra(PaymentConst.QRIS_BOOK_ID);
            PaymentPrefManager.Companion.getInstance().setQrisRejectedTs(0, true);
            if (qrisBook != null && result != null && result.equals(PaymentConst.QRIS_FORM_SUCCESS)) {
                if (qrisBook.equals(SessionManager.getInstance().getBusinessId())) {
                    showQrisSuccessSnackbar();
                } else {
                    SessionManager.getInstance().setBusinessId(qrisBook);
                    SessionManager.getInstance().setAppState(AppConst.APP_STATE_ALL_DELETED);

                    Intent newIntent = new Intent(this, MainActivity.class);
                    newIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    newIntent.putExtra(PaymentConst.QRIS_FORM_SUCCESS, true);
                    startActivity(newIntent);
                    finish();
                }
            } else {
                showQrisSuccessSnackbar();
            }
        } else if (intent.getBooleanExtra(KYB_SUBMITTED, false)) {
            if (paymentTabFragment != null && paymentTabFragment.isAdded()) {
                paymentTabFragment.showKybSuccess();
            }
        } else if (intent.getBooleanExtra(PaymentConst.APPEAL_FLOW_SUBMITTED, false)) {
            showAppealFlowSuccessSnackbar();
        }

        if (intent.getStringExtra(PaymentConst.SUCCESS_TOP_MESSAGE) != null) {
            showSuccessSnackbar(intent.getStringExtra(PaymentConst.SUCCESS_TOP_MESSAGE));
        }

        if (intent.getExtras() != null) {
            if (intent.getExtras().getBoolean(DailyUpdateNoTxnActivity.Companion.getFROM_DAILY_UPDATE_NO_TXN())) {
                initiateTransaction();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        checkIfAppUpdateIsInProgress();
        FeaturePrefManager.getInstance().addListener(listener);
        if (getIntent().getStringExtra(MainActivityConstants.TAB_NAME) != null) {
            if (getIntent().hasExtra(LoginUtils.IS_NEW_LOGIN_EXTRA)) {
                if (getIntent().getBooleanExtra(LoginUtils.IS_NEW_LOGIN_EXTRA, false)) {
                    navigateToTab();
                    setIntent(new Intent());
                }
            } else {
                navigateToTab();
            }
        }

        LocalBroadcastManager.getInstance(this).registerReceiver(
                eventLoggingReceiver,
                new IntentFilter(EventLoggingReceiver.ACTION_LOG_EVENT)
        );
        LocalBroadcastManager.getInstance(this).registerReceiver(
                exceptionLoggingReceiver,
                new IntentFilter(ExceptionLoggingReceiver.ACTION_LOG_EXCEPTION)
        );
        LocalBroadcastManager.getInstance(this).registerReceiver(
                urlReceiver,
                new IntentFilter(UrlReceiver.ACTION_OPEN_URL)
        );
    }

    private void handleDeepLinks(Intent intent) {
        this.deepLinkManager = new DeepLinkManager(this);
        this.deepLinkManager.setNeuro(neuro);

        boolean referralEnabled = AppConfigManager.getInstance().useReferralSharing();

        FacebookSdk.setAutoInitEnabled(true);
        FacebookSdk.fullyInitialize();

        AppLinkData.fetchDeferredAppLinkData(this,
                appLinkData -> {
                    try {
                        Uri uri = appLinkData.getTargetUri();
                        Log.d("DeeplinkData promo::", uri.toString());
                        SessionManager.getInstance().setFbDeeplinkData(true);

                        String refCode = appLinkData.getPromotionCode();
                        ReferralPrefManager.getInstance().setTemporaryReferralCode(refCode);

                        redirect(uri.toString());
                    } catch (Exception ex) {
                    }
                }
        );
        if (referralEnabled) {
            try {
                Disposable disposable = installReferrerController
                        .startDetectingInstalls()
                        .subscribeOn(Schedulers.computation())
                        .subscribe(
                                referralCode -> {
                                    final boolean useReferral = AppConfigManager.getInstance().useReferral();
                                    if (useReferral) {
                                        ReferralPrefManager.getInstance().setTemporaryReferralCode(referralCode.get(PlaystoreLinkBuilder.REFERRAL_CODE_KEY));
                                        boolean shouldShowNewLoginScreen = RemoteConfigUtils.INSTANCE.shouldShowNewLoginScreen();
                                        Intent loginIntent = shouldShowNewLoginScreen ? NewLoginActivity.Companion.getIntent(this, referralCode.get(PlaystoreLinkBuilder.REFERRAL_CODE_KEY),
                                                referralCode.get(PlaystoreLinkBuilder.DESTINATION_CODE_KEY)) :
                                                LoginActivity.Companion.getIntent(this, referralCode.get(PlaystoreLinkBuilder.REFERRAL_CODE_KEY),
                                                        referralCode.get(PlaystoreLinkBuilder.DESTINATION_CODE_KEY));
                                        startActivity(loginIntent);
                                        finish();
                                    }
                                    // We'd still want to detect deeplink after this, but not Referral code deeplinks
                                    deepLinkManager.setNotDetectReferralCode();
                                    deepLinkManager.start(intent);
                                },
                                e -> {
                                    // GO START DEEPLINK MANAGER IF REFERRAL INSTALLS NOT SUCCEED
                                    deepLinkManager.start(intent);
                                }
                        );
                if (compositeDisposable != null)
                    compositeDisposable.add(disposable);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {
            Log.d("DeeplinkManager", "start else part");
            if (!SessionManager.getInstance().hasFbDeeplinkData()) {
                deepLinkManager.start(intent);
            }
        }
    }

    private void navigateToTab() {
        if (!showPayments) {
            String tabNameString = getIntent().getStringExtra(MainActivityConstants.TAB_NAME);
            if (getIntent().hasExtra(MainActivityConstants.TAB_NAME_HOME)) {
                tabNameString = TabName.HOME.name();
            }
            navigateTabFromIntentExtra(tabNameString, getIntent().getStringExtra(MainActivityConstants.TARGET_TAB));
        }
    }

    @AddTrace(name = TraceConstants.navigateTabFromExtraIntent, enabled = true)
    private void navigateTabFromIntentExtra(String tabNameString, String targetTabString) {
        if (tabNameString != null) {
            try {
                TabName tabName = TabName.valueOf(tabNameString);
                setCurrentTabFromTabName(tabName);
            } catch (Exception ignored) {
                //do nothing
            }
        } else if (targetTabString != null) {
            try {
                int targetTab = Integer.parseInt(targetTabString);
                if (targetTab == 0 || targetTab == 1) {
                    int index = tabNames.indexOf(TabName.TRANSACTION_HOME);
                    tabPager.setCurrentItem(index);
                    Fragment currentFragment = tabNavigationAdapter.getItem(index);
                    if (currentFragment instanceof TransactionTabHome) {
                        ((TransactionTabHome) currentFragment).setCurrentTab(targetTab);
                    }
                } else setCurrentTab(targetTab);
            } catch (Exception e) {
                //do nothing
            }
        }
    }

    @AddTrace(name = TraceConstants.onCreateMainActivity, enabled = true)
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Apply edge-to-edge for Android 15 compatibility
        EdgeToEdgeHelper.applyEdgeToEdge(this);
        
        setContentView(R.layout.activity_main);
        
        // Setup window insets for main content
        View mainContentFrame = findViewById(R.id.main_content_frame);
        if (mainContentFrame != null) {
            EdgeToEdgeHelper.applyWindowInsetsPadding(mainContentFrame, false, true, false, false);
        }
        
        // Setup window insets for bottom navigation
        View tabNavigation = findViewById(R.id.navigation);
        if (tabNavigation != null) {
            EdgeToEdgeHelper.applyWindowInsetsPadding(tabNavigation, false, false, false, true);
        }

        AppAnalytics.setUserProperty(AnalyticsConst.APP_VERSION_NAME, BuildConfig.VERSION_NAME);

        // Initialize ViewModels
        profileTabViewModel = new ViewModelProvider(this).get(ProfileTabViewModel.class);
        businessFormViewModel = new ViewModelProvider(this).get(DetailedBusinessFormViewModel.class);
        businessProfileFormViewModel = new ViewModelProvider(this).get(BusinessProfileFormViewModel.class);

        tabNames = FeaturePrefManager.getInstance().getTabNames();
        sendAppsFlyerId();
        compositeDisposable = new CompositeDisposable();

        // for testing daily business update
        if (RemoteConfigUtils.INSTANCE.enableDailyBusinessUpdateForTesting()) {
            FeaturePrefManager.getInstance().setDailyBusinessRecap(true);
            FeaturePrefManager.getInstance().setDailyHighlightSettingsVisible(true);
            HomeHelper.Companion.dailyUpdateStatusReset(this);
        } else {
            //schedule daily update highlights to enable at midnight for first session
            if (!FeaturePrefManager.getInstance().isDailyHighlightSettingsVisible()) {
                if (RemoteConfigUtils.INSTANCE.enableDailyBusinessUpdate()) {
                    HomeHelper.Companion.dailyUpdateActivation(this);
                    HomeHelper.Companion.dailyUpdateStatusReset(this);
                }
            } else {
                FeaturePrefManager.getInstance().setDailyBusinessActivatedFlag(true);
            }
        }


        //avoid setup before layout init
        initSetup();
        implicitSchemeDeepLinkHandler.handle(this, getIntent());
        SurvicateAnalytics.setUserIdentifier();
        this.homelayout = findViewById(R.id.homepage_layout);
        this.sideMenuToggle = new ActionBarDrawerToggle(this, this.homelayout, R.string.app_name, R.string.app_name);
        this.homelayout.addDrawerListener(sideMenuToggle);
        this.bottomNavigationView = findViewById(R.id.navigation);
        this.bottomNavigationView.setOnNavigationItemSelectedListener(this);
        this.tabPager = (DefaultViewPager) findViewById(R.id.main_container);
        this.btnCreateBusineess = findViewById(R.id.add_new_business_btn);
        btnCreateBusineess.setOnClickListener(this);
        String tabString = AppConfigManager.getInstance().getDefaultTabName();
        int tabId = tabNames.indexOf(TabName.valueOf(tabString));
        hamburgerMenuBannerUrl = RemoteConfigUtils.INSTANCE.getHamburgerMenuBannerUrl();
        shouldShowHamburgerMenuBanner = RemoteConfigUtils.INSTANCE.enableHamburgerMenuBanner();
        hamburgerMenuBannerLanding = RemoteConfigUtils.INSTANCE.getHamurgerMenuBannerLanding();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            if (SessionManager.getInstance().isLoggedIn() && !SessionManager.getInstance().isGuestUser()) {
                updateAppShortcut();
            }
        }
        try {
            if (SessionManager.getInstance().isLoggedIn() && !SessionManager.getInstance().hasCheckedAdId() && Utility.isBlank(SessionManager.getInstance().getAdvertisingId())) {
                Thread mThread = new Thread() {
                    @Override
                    public void run() {
                        SessionManager.getInstance().hasCheckedAdId(true);
                        String advertId = AppAnalytics.getAdId(Application.getAppContext());
                        SessionManager.getInstance().setAdvertisingId(advertId);
                    }
                };
                mThread.start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        PaymentMetadata metadata = PaymentPrefManager.Companion.getInstance().getPaymentMetadata();
        if (metadata != null && metadata.getKyc() != null && KycStatusMetadata.PENDING == metadata.getKyc().getStatus()) {
            FeaturePrefManager.getInstance().hasCompletedKYC(true);
        }
        setKycInfo();
        //TODO: optimize when refactor
        if (getIntent().hasExtra(MainActivityConstants.TAB_NAME) || (getIntent().getData() != null && getIntent().getData().getQueryParameter(MainActivityConstants.TAB_NAME) != null)) {
//            tabId = tabNames.indexOf(TabName.valueOf(tabString));
//            sessionManager.setPrevTab(tabId);
            try {
                tabString = getIntent().getStringExtra(MainActivityConstants.TAB_NAME);
                if (tabString == null)
                    tabString = getIntent().getData().getQueryParameter(MainActivityConstants.TAB_NAME);
                TabName tabName = TabName.valueOf(tabString);
                if (tabNames.contains(tabName)) {
                    tabId = tabNames.indexOf(tabName);
                } else if (tabName == TabName.CUSTOMER || tabName == TabName.TRANSACTION) { // check for sub tabs
                    tabId = tabNames.indexOf(TabName.TRANSACTION_HOME);
                    if (tabName == TabName.CUSTOMER) subTabId = MainActivityConstants.CUSTOMER_TAB_POSITION;
                    else subTabId = MainActivityConstants.CASH_TRANSACTION_TAB_POSITION;
                } else if (tabName == TabName.RUN_OUT_STOCK || tabName == TabName.CURRENT_STOCK) {
                    tabId = tabNames.indexOf(TabName.STOCK);
                    if (tabName == TabName.RUN_OUT_STOCK) subTabId = MainActivityConstants.RUN_OUT_STOCK_TAB_POSITION;
                    else subTabId = MainActivityConstants.CURRENT_STOCK_TAB_POSITION;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                FirebaseCrashlytics.getInstance().log("wrong tab name in tab_name parameter");
            }
        } else if (getIntent().hasExtra(MainActivityConstants.TARGET_TAB) || (getIntent().getData() != null && getIntent().getData().getQueryParameter(MainActivityConstants.TARGET_TAB) != null)) {
            try {
                String targetTabString = getIntent().getExtras().getString(MainActivityConstants.TARGET_TAB);
                if (mergedTab) {
                    if (targetTabString == null)
                        targetTabString = getIntent().getData().getQueryParameter(MainActivityConstants.TARGET_TAB);
                    tabId = Integer.parseInt(targetTabString);
                    if (tabId == MainActivityConstants.CASH_TRANSACTION_TAB_POSITION || tabId == MainActivityConstants.CUSTOMER_TAB_POSITION) {
                        if (tabId == 0)
                            subTabId = MainActivityConstants.CUSTOMER_TAB_POSITION; // 0 is old customer tab index
                        else subTabId = MainActivityConstants.CASH_TRANSACTION_TAB_POSITION;
                        tabId = tabNames.indexOf(TabName.TRANSACTION_HOME);
                    }
                } else {
                    if (targetTabString != null)
                        tabId = Integer.parseInt(targetTabString);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                FirebaseCrashlytics.getInstance().log("Sending target_tab that aren't an integer");
            }
        }
        if (getIntent().hasExtra(AppConst.SHOW_LEADERBOARD_DIALOG)) {
            showLeaderboardDialog();
        }
        if (getIntent().hasExtra(MainActivityConstants.KYB_STATUS)) {
            try {
                KybStatus kybStatus = KybStatus.valueOf(getIntent().getStringExtra(MainActivityConstants.KYB_STATUS));
                switch (kybStatus) {
                    case REJECTED:
                    case MANUALLY_REJECTED: {
                        KycKybBottomSheet.Companion.createInstance(
                                KycKybBottomSheet.UseCase.KYB_REJECTED,
                                getIntent().getStringExtra(MainActivityConstants.REJECTION_REASON),
                                AnalyticsConst.DEEPLINK
                        ).show(getSupportFragmentManager(), KycKybBottomSheet.TAG);
                        break;
                    }
                    case VERIFIED:
                    case MANUALLY_VERIFIED: {
                        AuthHelper.newSession();
                        LiveData<KycTier> kycTierLive = PaymentPrefManager.Companion.getInstance()
                                .getKycTierData();
                        kycTierLive.observe(this, it -> {
                            kycTierLive.removeObservers(this);
                            if (it == KycTier.SUPREME) {
                                KycKybBottomSheet.Companion.createInstance(
                                        KycKybBottomSheet.UseCase.KYB_VERIFIED, null,
                                        AnalyticsConst.DEEPLINK
                                ).show(getSupportFragmentManager(), KycKybBottomSheet.TAG);
                            }
                        });
                        break;
                    }
                }
            } catch (Exception ex) {
                FirebaseCrashlytics.getInstance().recordException(ex);
            }
        }
        if (KycStatus.MANUALLY_VERIFIED.toString().equals(getIntent().getStringExtra(MainActivityConstants.STATUS)) ||
                KycStatus.VERIFIED.toString().equals(getIntent().getStringExtra(MainActivityConstants.STATUS))) {
            AuthHelper.newSession();
            KycKybBottomSheet.Companion.createInstance(
                    KycKybBottomSheet.UseCase.KYC_VERIFIED, null, AnalyticsConst.DEEPLINK
            ).show(getSupportFragmentManager(), KycKybBottomSheet.TAG);
        }
        if (KycStatus.REJECTED.toString().equals(getIntent().getStringExtra(MainActivityConstants.STATUS))) {
            String rejectionReason = getIntent().getStringExtra(MainActivityConstants.REJECTION_REASON);
            KycKybBottomSheet.Companion.createInstance(
                    KycKybBottomSheet.UseCase.KYC_REJECTED, rejectionReason, AnalyticsConst.DEEPLINK
            ).show(getSupportFragmentManager(), KycKybBottomSheet.TAG);
        }

        if (!hasBusiness() && SetupManager.getInstance().hasRestored() && (getIntent().hasExtra("open_side_menu") || getIntent().hasExtra(LoginUtils.IS_NEW_LOGIN_EXTRA))) {
            BusinessRepository businessRepository = BusinessRepository.getInstance(Application.getAppContext());
            BookEntity bookEntity = businessRepository.createBusiness(User.getUserId(), User.getDeviceId(), getString(R.string.default_owner_name), this.getString(R.string.mybusiness), -1, "");
            changeAppState(bookEntity);
            AppAnalytics.trackEvent(EVENT_SUCCESS_REGISTRATION);
            SurvicateAnalytics.invokeEventTracker(EVENT_SUCCESS_REGISTRATION, this);
            FeaturePrefManager.getInstance().setNewUserFlag(true);
            SetupManager.getInstance().setSyncedBookData(true);
        } else {
            SetupManager.getInstance().setSyncedBookData(false);
        }
        validateUserContext();
        if (SetupManager.getInstance().hasRestored()) {

            profileTabViewModel.getUserProfile(User.getUserId()).observe(this, it -> {
                if (!userProfileObserved && it != null && it.getDirty() == 1) {
                    userProfileObserved = true;
                    profileTabViewModel.saveUserProfileToRemote(it);
                } else if (!userProfileObserved && it == null) {
                    userProfileObserved = true;
                    profileTabViewModel.getUserProfileFromRemote();
                }
            });

            setupTabNavigationPager(this.tabPager);
            initSideMenu(this);
            if (getIntent().hasExtra("open_side_menu") || getIntent().hasExtra(LoginUtils.HAS_MULTIPLE_BOOKS)) {
                handleSideMenuAfterDeleteBusiness();
            }

            final boolean referralFeatureActive = AppConfigManager.getInstance().useReferral();
            final String receivedReferralCode = ReferralPrefManager.getInstance().getTemporaryReferralCode();
            if (referralFeatureActive && getCurrentBook() != null
                    && receivedReferralCode != null && !receivedReferralCode.isEmpty()) {
                String businessOwnerName = getCurrentBook().businessOwnerName;

                ReferralRepository.getInstance().registerWithRefCode(
                        receivedReferralCode,
                        businessOwnerName,
                        false
                );

                ReferralPrefManager.getInstance().clearTemporaryReferralCode();
            }
            if (Utility.isBlank(User.getBusinessId())) {
                initReturningUser();
            }

            AuthHelper.refreshUserSession();

            listener = new SharedPreferences.OnSharedPreferenceChangeListener() {
                public void onSharedPreferenceChanged(SharedPreferences prefs, String key) {
                    // your stuff
                    if (!TextUtils.isEmpty(key) && key.equals(USING_DAILY_BUSINESS_RECAP)) {
                        initSideMenu(MainActivity.this);
                    }
                }
            };
        }

        if (!FeaturePrefManager.getInstance().areBookCategoriesUpdated()) {
            // map to new categories
            String[] oldBusinessTypes = getResources().getStringArray(R.array.BusinessCategory);
            String[] newBusinessTypes = getResources().getStringArray(R.array.NewBusinessCategory);
            Utility.updateBusinessCategory(oldBusinessTypes, newBusinessTypes);
        }

        this.installReferrerController = new InstallReferrerController(getApplicationContext());
        Intent intent = getIntent();
        if (intent != null) {
            handleDeepLinks(intent);
        }
        if (this.tabPager != null) {
            if (FeaturePrefManager.getInstance().cashModuleEnabled()) {
                int numOfTrx = 0;
                try {
                    numOfTrx = TransactionRepository.getInstance(this).countAllUtangTrans();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                // safe to remove? dont think its still relevant
//                if (SessionManager.getInstance().getPrevTab() > 0) {
//                    // has prev tab before
//                    setCurrentTab(SessionManager.getInstance().getPrevTab());
//                } else
                boolean shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage();
                if (shouldShowNewHomePage) {
                    setCurrentTab(0);
                } else if (numOfTrx == 0) {
                    String tabName = AppConfigManager.getInstance().getDefaultTabName();
                    // doesn't have prev tab before, new user
                    int defaultTab = 0;
                    try {
                        defaultTab = tabNames.indexOf(TabName.valueOf(tabName));
                        if (defaultTab < 0) defaultTab = 0;
                    } catch (Exception e) {
                        // do nothing
                    }
                    setCurrentTab(defaultTab);
                } else {
                    setCurrentTab(tabId);
                }
            } else {
                setCurrentTab(tabId);
            }
        }

        handler.postDelayed(appUpdateRunnable, 2000);

        subscribeState();

        this.homelayout.addDrawerListener(new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {
            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                initSideMenu(MainActivity.this);
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
            }

            @Override
            public void onDrawerStateChanged(int newState) {
            }
        });

        //check daily business update visibility
        if (!RemoteConfigUtils.INSTANCE.enableDailyBusinessUpdate()) {
            FeaturePrefManager.getInstance().setDailyBusinessRecap(false);
            FeaturePrefManager.getInstance().setDailyHighlightSettingsVisible(false);
        }

        inAppTnCBottomSheet = new InAppTnCBottomSheet(this, getSupportFragmentManager());
        ReferralPrefManager.getInstance().getReferralCode().observe(this, s -> referralCodeFromDeeplink = s);

        // Check if intent was triggered after qris form submission
        if (getIntent().getBooleanExtra(PaymentConst.QRIS_FORM_SUCCESS, false)) {
            // If QRIS is success, we show success snackbar only after selecting the active book ID
            showQrisSuccessSnackbar();
        }

        if (getIntent().hasExtra(getString(R.string.loan_book_key)))
            startActivity(WebviewActivity.Companion.createIntent(this, RemoteConfigUtils.INSTANCE.getLoanUrl(), ""));

        if (getIntent().hasExtra(LoginUtils.IS_NEW_LOGIN_EXTRA) && getIntent().getBooleanExtra(LoginUtils.IS_NEW_LOGIN_EXTRA, false)) {
            AuthHelper.callBureau("LOGIN", FeaturePrefManager.getInstance().getInstallionMode(),
                    SessionManager.getInstance().getBukuwarungToken(),
                    SessionManager.getInstance().getUUID());
        }
        askNotificationPermission();
        postFcmTokenUseCase.invokeIfNeeded();
        Utilities.INSTANCE.bwLog(null, new Exception("Homepage Check Point"));
    }

    private void askNotificationPermission() {
        // This is only necessary for API level >= 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) ==
                    PackageManager.PERMISSION_GRANTED) {
                // FCM SDK (and your app) can post notifications.
            }  else {
                // Directly ask for the permission
                if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.POST_NOTIFICATIONS)) {
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS);
                } else {
                    // User has already denied for the location permission
                }
            }
        }
    }

    public void initiateTransaction() {
        Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
        if (currentFragment instanceof IncomeExpenseTab && currentFragment.isAdded())
            ((IncomeExpenseTab) currentFragment).initiateTransactionFromDailyBusinessUpdate(this, false);
    }

    private void showQrisSuccessSnackbar() {
        if (paymentTabFragment != null && paymentTabFragment.isAdded()) {
            paymentTabFragment.showQrisSuccess();
        }
    }

    private void showAppealFlowSuccessSnackbar() {
        if (paymentTabFragment != null && paymentTabFragment.isAdded()) {
            paymentTabFragment.showAppealFlowSuccess();
        }
    }

    private void showSuccessSnackbar(String message) {
        if (paymentTabFragment != null && paymentTabFragment.isAdded()) {
            paymentTabFragment.showSuccess(message);
        }
    }

    private void showLeaderboardDialog() {
        transactionsPopUpDialog = new TransactionsPopUpDialog(this, () -> {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(AnalyticsConst.CAMPAIGN, "referral microsite");
            propBuilder.put(AnalyticsConst.METHOD, "tap_outside");
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_DISMISS_IN_APP_BANNER, propBuilder);
            String url = AppConfigManager.getInstance().getReferralLeaderboardUrl();
            Intent webViewIntent = LeaderboardWebviewActivity.Companion.createIntent(this, url, getString(R.string.leaderboard_program), "In_app_banner");
            webViewIntent.putExtra(LeaderboardWebviewActivity.WEBVIEW_PARAM_IS_LEADERBOARD, true);
            startActivity(webViewIntent);
            return Unit.INSTANCE;
        }, () -> {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(AnalyticsConst.CAMPAIGN, "referral microsite");
            propBuilder.put(AnalyticsConst.METHOD, "cross");
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_DISMISS_IN_APP_BANNER, propBuilder);
            return Unit.INSTANCE;
        }, R.drawable.leaderboard_banner, RemoteConfigUtils.INSTANCE.getLandingBannerHeader(), RemoteConfigUtils.INSTANCE.getLandingBannerBody(), RemoteConfigUtils.INSTANCE.getLandingBannerButtonText(), RemoteConfigUtils.INSTANCE.getLandingBannerUrl());
        transactionsPopUpDialog.show();
    }

    private void subscribeState() {


    }

    public final void handleSideMenuAfterDeleteBusiness() {
        List businessList = BusinessRepository.getInstance(this).getBusinessListRaw(User.getUserId());
        if (businessList.size() != 0) {
            BookEntity bookEntity = (BookEntity) businessList.get(0);
            sessionManager.setBusinessId(bookEntity.bookId);
//            AppAnalytics.trackEvent("home_tap_menu");
            if (businessList.size() == 1) {
                this.homelayout.closeDrawer(GravityCompat.START);
            } else {
                this.homelayout.openDrawer(GravityCompat.START);
                this.homelayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED);
            }
        } else {
            finish();
            Intent intent = new Intent(this, SideNavActivity.class);
            startActivity(intent);
        }
    }

    public final void handleSideMenuIconClick() {
        List businessList = BusinessRepository.getInstance(this).getBusinessListRaw(User.getUserId());
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        if (businessList.size() != 0) {
            BookEntity bookEntity = (BookEntity) businessList.get(0);
            prop.put(AnalyticsConst.SELECTED_BUSINESS_NAME, bookEntity.businessName);
            prop.put(AnalyticsConst.NO_OF_BUSINESS, String.valueOf(businessList.size()));
            prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.HAMBURGER_CLICK);
            prop.put(AnalyticsConst.BANNER_VISIBLE, shouldShowHamburgerMenuBanner);
            PreferencesManager prefManager = new PreferencesManager(this);
            long installTime = prefManager.getFirstInstallDateTime();
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            Date currentDate = cal.getTime();
            if (FeaturePrefManager.getInstance().isDailyBusinessActivatedFlag()) {
                prop.put(AnalyticsConst.BW_STORY_ENABLE, FeaturePrefManager.getInstance().useDailyBusinessRecap());
            }
        }
//        AppAnalytics.trackEvent("open_side_menu");
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_LEFT_DRAWER, prop, false, false, false);
        if (this.homelayout.isDrawerOpen(GravityCompat.START)) {
            this.homelayout.closeDrawer(GravityCompat.START);
            return;
        }
        fetchBookDataFromBackend();
        this.homelayout.openDrawer(GravityCompat.START);
    }

    public void onBackPressed() {
        if (this.homelayout.isDrawerOpen(GravityCompat.START)) {
            this.homelayout.closeDrawer(GravityCompat.START);
            return;
        }
        sendOnBackPressedEventToFragment();
    }

    @AddTrace(name = TraceConstants.sendOnBackPressedEventToFragment)
    private void sendOnBackPressedEventToFragment() {
        Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
        if (currentFragment instanceof TransactionTabHome) {
            ((TransactionTabHome) currentFragment).onBackPressed();
        } else if (currentFragment instanceof ProfileTabFragment) {
            doubleTapToExitPressedOnce = false;
            ((ProfileTabFragment) currentFragment).onBackPressed();
        } else if (currentFragment instanceof InventoryHomeFragment) {
            doubleTapToExitPressedOnce = false;
            ((InventoryHomeFragment) currentFragment).onBackPressed();
        } else if (currentFragment instanceof CustomerTab) {
            if (RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage()) {
                doubleTapToExitPressedOnce = false;
            }
            ((CustomerTab) currentFragment).onBackPressed();
        } else if (currentFragment instanceof IncomeExpenseTab) {
            doubleTapToExitPressedOnce = false;
            ((IncomeExpenseTab) currentFragment).onBackPressed();
        } else if (currentFragment instanceof PaymentTabFragment) {
            doubleTapToExitPressedOnce = false;
            ((PaymentTabFragment) currentFragment).onBackPressed();
        } else if (currentFragment instanceof HomeFragment) {
            ((HomeFragment) currentFragment).onBackPressed();
        } else {
            if (this.tabPager != null) {
                setCurrentTab(tabNames.indexOf(TabName.TRANSACTION_HOME));
                handleDoubleBackPress(false);
            }
        }
    }

    @Override
    public void handleNormalBackPressedFromCustomer() {
        handleDoubleBackPress(false);
    }

    @Override
    public void handleNormalBackPressedFromIncomeExpense() {
        boolean shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage();
        if (this.tabPager != null) {
            if (shouldShowNewHomePage) {
                setCurrentTab(0);
            } else {
                setCurrentTab(tabNames.indexOf(TabName.CUSTOMER));
            }
        }
        handleDoubleBackPress(false);
    }

    private void handleDoubleBackPress(boolean fromHome) {
        boolean shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage();
        if (shouldShowNewHomePage) {
            if (doubleTapToExitPressedOnce) {
                super.onBackPressed();
                return;
            }

            if (fromHome) {
                doubleTapToExitPressedOnce = true;
                NotificationUtils.alertToast(getApplicationContext().getString(R.string.double_tap_message));
            }
            setCurrentTab(0);
            new Handler().postDelayed(() -> doubleTapToExitPressedOnce = false, 3000);
            return;
        }
        if (doubleTapToExitPressedOnce) {
            super.onBackPressed();
            return;
        }
        doubleTapToExitPressedOnce = true;
        NotificationUtils.alertToast(getApplicationContext().getString(R.string.double_tap_message));
        new Handler().postDelayed(() -> doubleTapToExitPressedOnce = false, 3000);

    }

    private boolean hasBusiness() {
        return BusinessRepository.getInstance(Application.getAppContext()).businessCount(User.getUserId()) > 0;
    }

    private void changeAppState(BookEntity bookEntity) {
        sessionManager.setBusinessId(bookEntity.bookId);
        sessionManager.setAppState(1);
    }

    @AddTrace(name = TraceConstants.initSideMenu)
    private void initSideMenu(Context context) {
        MaterialButton createBusinessButton = findViewById(R.id.add_new_business_btn);
        this.navigationList = (RecyclerView) findViewById(R.id.business_list);
        createBusinessButton.setOnClickListener(this);

        if (context != null) {
            try {
                sideMenuBanner = findViewById(R.id.img_nav_payment_banner);
                if (!shouldShowHamburgerMenuBanner || SessionManager.getInstance().isGuestUser()) {
                    this.sideMenuBanner.setVisibility(View.GONE);
                } else {
                    Glide.with(this).load(hamburgerMenuBannerUrl)
                            .placeholder(R.drawable.payment_hamburger_banner)
                            .into(sideMenuBanner);
                    sideMenuBanner.setOnClickListener(this);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            this.navAdapter = new NavigationAdapter(this.navigationList, this);
            this.navigationList.setAdapter(this.navAdapter);
            this.navigationList.setLayoutManager(new LinearLayoutManager(this));
            this.navigationList.setNestedScrollingEnabled(false);

            this.businessViewModel = new ViewModelProvider(this).get(BusinessViewModel.class);

            BookEntity bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
            String businessCategory = null;
            if (bookEntity != null) {
                businessCategory = bookEntity.bookTypeName;
            }
            if (!FeaturePrefManager.getInstance().stockTabEnabled()) {
                cashListViewModel = new ViewModelProvider(this, cashListViewModelFactory).get(CashListViewModel.class);
                this.cashListViewModel.getTrxCountLiveData().observe(this, trxCount -> {
                    boolean refreshRequired = false;
                    if (trxCount >= AppConfigManager.getInstance().getStockTransactionTarget() && !FeaturePrefManager.getInstance().stockTabEnabled()) {
                        FeaturePrefManager.getInstance().setStockTabEnabled(true);
                        if (FeaturePrefManager.getInstance().stockTabEnabledFromSettings() == FeaturePrefManager.STOCK_NOT_USED) {
                            FeaturePrefManager.getInstance().setStockTabEnabledFromSettings(FeaturePrefManager.STOCK_SELECTED);
                        }
                        refreshRequired = true;
                        if (!FeaturePrefManager.getInstance().isStockBadgeFirstTimeDisplayConditions()) {
                            FeaturePrefManager.getInstance().stockBadgeFirstTimeDisplayConditions(true);
                            FeaturePrefManager.getInstance().stockBadgeFirstTimeDisplayed(false);
                        }
                    }
                    if (refreshRequired) {
                        refreshTabs();
                    }
                });
            }
            LiveData businessList = this.businessViewModel.getBusinessList();
            LifecycleOwner lifecycleOwner = this;
            businessList.observe(lifecycleOwner, new BusinessListObserver(this, btnCreateBusineess));
            try {
                TextView versionTv = findViewById(R.id.appVersion);
                PackageInfo pInfo = getApplicationContext().getPackageManager().getPackageInfo(getPackageName(), 0);
                versionTv.setText("version " + pInfo.versionName + "(" + pInfo.versionCode + ")");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void enablePaymentsTab() {
        FeaturePrefManager.getInstance().setPaymentTabEnabled(true);
        customerTab = null;
        transactionTab = null;
        incomeExpenseTab = null;
        profileTabFragment = null;
        paymentTabFragment = null;
        inventoryHomeFragment = null;
        setTabs();
    }

    private void initSetup() {
        new Setup().startAppSetup(this);
    }

    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.add_first_business:
                Intent intent = new Intent(this, CreateBusinessActivity.class);
                intent.putExtra(CreateBusinessActivity.EVENT_TYPE, CreateBusinessActivity.FIRST_BUSINESS);
                startActivity(intent);
                return;
            case R.id.add_new_business_btn:
                if (SessionManager.getInstance().isGuestUser()) {
                    callLoginBottomsheet(true, AnalyticsConst.CREATE_BUSINESS_BTN);
                } else {
                    redirectToBusinessCreationFlow();
                    AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                    prop.put(ENTRY_POINT2, AnalyticsConst.HAMBURGER_MENU);
                    AppAnalytics.trackEvent("customer_add_new_business", prop);
                }
                return;
            case R.id.img_nav_payment_banner:
                if (Utility.isBlank(hamburgerMenuBannerLanding)) {
                    AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                    propBuilder.put(AnalyticsConst.BANNER_LOCATION, AnalyticsConst.HAMBURGER_MENU);
                    propBuilder.put(AnalyticsConst.BANNER_NAME, AnalyticsConst.NEW_FEATURE_PAYMENT_PULSA_BANNER);
                    AppAnalytics.trackEvent(AnalyticsConst.BANNER_CLICK, propBuilder);
                    this.homelayout.closeDrawer(GravityCompat.START);
                    MainActivity.startActivitySingleTopToTab(this, TabName.PAYMENT);

                } else {
                    intent = new Intent(Intent.ACTION_VIEW, Uri.parse(hamburgerMenuBannerLanding));
                    startActivity(intent);
                }
            default:
                return;
        }
    }

    public final void initResurrectUser() {
        List<BookEntity> businessList = BusinessRepository.getInstance(this).getBusinessListRaw(User.getUserId());
        if (CollectionUtils.isEmptyList(businessList) && SetupManager.getInstance().hasRestored()) {
            Intent intent = new Intent(this, CreateBusinessActivity.class);
            intent.putExtra(CreateBusinessActivity.EVENT_TYPE, CreateBusinessActivity.FIRST_BUSINESS);
            startActivity(intent);
        } else if (businessList.size() > 0) {
            sessionManager.setAppState(AppConst.APP_STATE_ALL_DELETED);
            sessionManager.setBusinessId(businessList.get(0).bookId);
            if (businessList.size() == 1) {
                this.homelayout.closeDrawer(GravityCompat.START);
                return;
            }
            this.homelayout.openDrawer(GravityCompat.START);
        }
    }

    public final void initReturningUser() {
        try {
            List<BookEntity> businessList = BusinessRepository.getInstance(this).getBusinessListRaw(User.getUserId());
            if (!ListUtils.isEmpty(businessList)) {
                sessionManager.setBusinessId(businessList.get(0).bookId);
            } else {
                BusinessRepository businessRepository = BusinessRepository.getInstance(Application.getAppContext());
                BookEntity bookEntity = businessRepository.createBusiness(User.getUserId(), User.getDeviceId(), "BukuWarung", this.getString(R.string.mybusiness), -1, "");
                changeAppState(bookEntity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setupTabNavigationPager(ViewPager navPager) {
        FragmentManager supportFragmentManager = getSupportFragmentManager();
        tabNavigationAdapter = new TabNavigationAdapter(supportFragmentManager);
        setTabs();
        if (navPager != null) {
            navPager.setAdapter(tabNavigationAdapter);
            navPager.setOffscreenPageLimit(4);
            navPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                }

                @Override
                public void onPageSelected(int position) {
                    Menu menu = bottomNavigationView.getMenu();
                    MenuItem menuItem = menu.getItem(position);
                    bottomNavigationView.setSelectedItemId(menuItem.getItemId());
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                }
            });
        }
    }

    private void setFragmentList() {
        List<Fragment> list = new ArrayList<>();
        for (TabName tabName : tabNames) {
            if (tabName.equals(TabName.HOME)) {
                if (homeFragment == null) {
                    homeFragment = HomeFragment.Companion.instance();
                }
                list.add(homeFragment);
            } else if (tabName.equals(TabName.TRANSACTION_HOME)) {
                if (incomeExpenseTab == null)
                    incomeExpenseTab = TransactionTabHome.instance(subTabId);
                list.add(incomeExpenseTab);
            } else if (tabName.equals(TabName.CUSTOMER)) {
                if (customerTab == null)
                    customerTab = new CustomerTab();
                list.add(customerTab);
            } else if (tabName.equals(TabName.PAYMENT)) {
                if (paymentTabFragment == null)
                    paymentTabFragment = PaymentTabFragment.Companion.createIntent(getIntent().getStringExtra("from"));
                list.add(paymentTabFragment);
            } else if (tabName.equals(TabName.TRANSACTION)) {
                if (transactionTab == null)
                    transactionTab = new IncomeExpenseTab();
                list.add(transactionTab);
            } else if (tabName.equals(TabName.STOCK)) {
                if (inventoryHomeFragment == null) {
                    boolean shouldOpenCatalogBottomSheet = Boolean.parseBoolean(getIntent().getStringExtra(InventoryHomeFragment.OPEN_CATALOG_BOTTOM_SHEET));
                    Log.d("CATALOG DeepLink", String.valueOf(shouldOpenCatalogBottomSheet));
                    inventoryHomeFragment = InventoryHomeFragment.instance(subTabId, null, shouldOpenCatalogBottomSheet, () -> {
                        handleNormalBackPressed();
                        return Unit.INSTANCE;
                    });
                }
                list.add(inventoryHomeFragment);
            } else {
                if (profileTabFragment == null)
                    profileTabFragment = new ProfileTabFragment();
                list.add(profileTabFragment);
            }
        }

        if (tabNavigationAdapter != null) {
            int pos = tabPager.getCurrentItem();
            try {
                tabNavigationAdapter.setFragmentList(list);
                tabPager.setAdapter(tabNavigationAdapter);
                Menu menu = bottomNavigationView.getMenu();
                MenuItem menuItem = menu.getItem(pos);
                bottomNavigationView.setSelectedItemId(menuItem.getItemId());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void setTabs() {
        Menu menu = bottomNavigationView.getMenu();
        menu.clear();
        boolean shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage();
        if (shouldShowNewHomePage) {
            tabNames = FeaturePrefManager.getInstance().getNewTabNames();
        } else {
            tabNames = FeaturePrefManager.getInstance().getTabNames();
        }
        for (int i = 0; i < tabNames.size(); i++) {
            TabName tabEnum = tabNames.get(i);
            if (tabEnum.equals(TabName.HOME)) {
                //home
                menu.add(Menu.NONE, R.id.navigation_home, i, getString(R.string.tab_home))
                        .setIcon(R.drawable.ic_new_home);
            } else if (tabEnum.equals(TabName.CUSTOMER)) {
                //utang
                menu.add(Menu.NONE, R.id.navigation_customers, i, getString(R.string.tab_customers_label))
                        .setIcon(R.drawable.ic_new_utang);
            } else if (tabEnum.equals(TabName.PAYMENT)) {
                menu.add(Menu.NONE, R.id.navigation_payment, i, getString(R.string.label_payment_only))
                        .setIcon(R.drawable.ic_payment_new);
            } else if (tabEnum.equals(TabName.TRANSACTION)) {
                // transaction
                menu.add(Menu.NONE, R.id.navigation_cash, i, getString(R.string.tab_expense_label))
                        .setIcon(R.drawable.ic_new_transaksi);
            } else if (tabEnum.equals(TabName.TRANSACTION_HOME)) {
                //home
                menu.add(Menu.NONE, R.id.navigation_cash, i, getString(R.string.tab_transaction_label))
                        .setIcon(R.drawable.icon_transcation_tab);
            } else if (!shouldShowNewHomePage && tabEnum.equals(TabName.STOCK)) {
                menu.add(Menu.NONE, R.id.navigation_stock, i, getString(R.string.stock))
                        .setIcon(R.drawable.ic_stock_tab);
                if (!shouldShowNewHomePage && !FeaturePrefManager.getInstance().isStockBadgeFirstTimeDisplayed()) {
                    BadgeDrawable badge = bottomNavigationView.getOrCreateBadge(R.id.navigation_stock);
                    badge.setVisible(true);
                    displayStockBadge = true;
                }
// An icon only badge will be displayed unless a number is set:
            } else if (tabEnum.equals(TabName.OTHERS)) {
                menu.add(Menu.NONE, R.id.navigation_profile, i, getString(R.string.tab_others_label))
                        .setIcon(R.drawable.ic_new_lainnya);
            }
        }
        setFragmentList();
    }


    @Override
    public boolean onNavigationItemSelected(MenuItem menuItem) {
        int i = 0;
        //check tab role(future) and session before switch
        AuthHelper.refreshUserSession();
        if (menuItem.getItemId() != R.id.navigation_stock) {
            // menu item calls multiple times on click to reset and track the stock tab click event
            firstCallOfOnSelect = false;
        }
        InputUtils.hideKeyboard(this);
        switch (menuItem.getItemId()) {
            case R.id.navigation_home:

                if (isAppStarted) {
                    isAppStarted = false;
                }

                i = tabNames.indexOf(TabName.HOME);
                break;

            case R.id.navigation_customers:
                i = tabNames.indexOf(TabName.CUSTOMER);

                AppAnalytics.trackAmpEvent("customer_list_tab_visit");
                if (isUtangClicked) {
                    isUtangClicked = false;
                }
                fetchCustomerDataFromBackend();
                break;

            case R.id.navigation_payment:
                paymentTabFragment.setOpenShowMore(false);
                if (isPembarayanClicked) {
                    isPembarayanClicked = false;
                }
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PEMBAYARAN_VISIT, this);
                i = tabNames.indexOf(TabName.PAYMENT);
                break;

            case R.id.navigation_cash:
                AppAnalytics.trackAmpEvent("expense_tab_visit");
                if (isTransksiClicked) {
                    isTransksiClicked = false;
                }

                if (incomeExpenseTab != null) {
                    i = tabNames.indexOf(TabName.TRANSACTION_HOME);
//                    i = tabNames.indexOf(TabName.HOME);
                } else {
                    i = tabNames.indexOf(TabName.TRANSACTION);
                }
                fetchCashTransactionDataFromBackend();
                break;

            case R.id.navigation_profile:
                if (isProfileClicked) {
                    isProfileClicked = false;
                }
                FeaturePrefManager.getInstance().setHasShownLainnyaMenu(true);
                i = tabNames.indexOf(TabName.OTHERS);
                // we will not put profile as prevtab
                break;

            case R.id.navigation_stock:
                i = tabNames.indexOf(TabName.STOCK);
                if (displayStockBadge) {
                    bottomNavigationView.removeBadge(R.id.navigation_stock);
                    FeaturePrefManager.getInstance().stockBadgeFirstTimeDisplayed(true);
                }
                if (!firstCallOfOnSelect) {
                    isStockClicked = true;
                    firstCallOfOnSelect = true;
                } else {
                    firstCallOfOnSelect = false;
                }
                isStockClickedFirstTime = true;
                // we will not put profile as prevtab
                break;
            default:
                i = 0;
                break;
        }
        if (i != tabNames.indexOf(TabName.OTHERS))
            SessionManager.getInstance().setPrevTab(i);
        if (this.tabPager != null) {
            setCurrentTab(i);
        }
        return true;
    }

    private void setCurrentTab(int i) {
        try {
            if (i < tabNames.size()) {
                tabPager.setCurrentItem(i);
            } else {
                tabPager.setCurrentItem(0);
            }

            checkTnC(tabNames.get(i));
        } catch (Exception ignored) {
        }
    }

    private void checkTnC(TabName tabName) {
        List<String> configuredTabsForTnc = RemoteConfigUtils.INSTANCE.getTncTabConfig();
        if (configuredTabsForTnc.contains(tabName.name()) && !SessionManager.getInstance().getHasAgreedTnc() && !SessionManager.getInstance().isGuestUser()) {
            if (!inAppTnCBottomSheet.isShowing()) {
                inAppTnCBottomSheet.setEntryPointAndShow(tabName);
            }
        }
    }

    private void setCurrentTabFromTabName(TabName tabName) {
        if (tabNavigationAdapter == null || tabPager == null || tabName == null) return;
        if (tabNames.contains(tabName)) {
            setCurrentTab(tabNames.indexOf(tabName));
        } else if (tabName == TabName.CUSTOMER || tabName == TabName.TRANSACTION) { // check for sub tabs
            setCurrentTab(tabNames.indexOf(TabName.TRANSACTION_HOME));
            Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
            if (currentFragment instanceof TransactionTabHome) {
                if (tabName == TabName.CUSTOMER)
                    ((TransactionTabHome) currentFragment).setCurrentTab(MainActivityConstants.CUSTOMER_TAB_POSITION);
                else
                    ((TransactionTabHome) currentFragment).setCurrentTab(MainActivityConstants.CASH_TRANSACTION_TAB_POSITION);
            }
        } else if (tabName == TabName.RUN_OUT_STOCK || tabName == TabName.CURRENT_STOCK) {
            setCurrentTab(tabNames.indexOf(TabName.STOCK));
            Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
            if (currentFragment instanceof TransactionTabHome) {
                if (tabName == TabName.RUN_OUT_STOCK)
                    ((InventoryHomeFragment) currentFragment).setCurrentTab(MainActivityConstants.RUN_OUT_STOCK_TAB_POSITION);
                else
                    ((InventoryHomeFragment) currentFragment).setCurrentTab(MainActivityConstants.CURRENT_STOCK_TAB_POSITION);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FeaturePrefManager.getInstance().removeListener(listener);
        handler.removeCallbacks(appUpdateRunnable);
        if (compositeDisposable != null && !compositeDisposable.isDisposed())
            compositeDisposable.clear();
        try {
            if (installReferrerController != null)
                installReferrerController.onDestroy();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        LocalBroadcastManager.getInstance(this).unregisterReceiver(eventLoggingReceiver);
        LocalBroadcastManager.getInstance(this).unregisterReceiver(exceptionLoggingReceiver);
        LocalBroadcastManager.getInstance(this).unregisterReceiver(urlReceiver);
        coroutineHandler.cancel();
    }

    private void fetchBookDataFromBackend() {
        if (SetupManager.getInstance().hasRestored()) {
            if (!SetupManager.getInstance().hasSyncedBookData()) {
//                SetupManager.getInstance().setCustomerTabSync(true);
                Log.d(TAG, "book sync started");
                //new AsyncBookDataSync(this).execute(new Void[0]);
                coroutineHandler.syncBookData(this);
            }
        }
    }

    private void fetchCustomerDataFromBackend() {
        if (SetupManager.getInstance().hasRestored()) {
            if (!SetupManager.getInstance().hasSynchedCustomerTab() && AppConfigManager.getInstance().getPullRefresh() == 1) {
//                SetupManager.getInstance().setCustomerTabSync(true);
                //new AsyncCustomerDataSync().execute();
                coroutineHandler.syncCustomerData();
            }
        }
    }

    private void fetchCashTransactionDataFromBackend() {
        if (SetupManager.getInstance().hasRestored()) {
            if (!SetupManager.getInstance().hasSynchedCashTab() && AppConfigManager.getInstance().getPullRefresh() == 1) {
                //find different way to optimize
                SetupManager.getInstance().setCashTabSync(true);
                //new AsyncCashTransactionDataSync().execute();
                coroutineHandler.syncCashTransactionData();
            }
            if (!SetupManager.getInstance().hasSynchedPaymentData()) {
                //new AsyncPaymentCashTransactionDataSync().execute();
                coroutineHandler.syncPaymentCashTransactionData();
            }
        }
    }

    private void requestAppUpdate() {
        AppUpdateBlock appUpdateBlock;
        try {
            String appUpdateVersionCodeBlock =
                    RemoteConfigUtils.APP_UPDATE_VERSION_CODE_BLOCK.INSTANCE.getAppUpdateOrderBlock();
            Gson gson = new GsonBuilder().create();
            Type jsonType = new TypeToken<AppUpdateBlock>() {
            }.getType();
            appUpdateBlock = gson.fromJson(appUpdateVersionCodeBlock, jsonType);
        } catch (Exception e) {
            appUpdateBlock = new AppUpdateBlock();
        }

        boolean softUpdate = false;
        boolean forceUpdate = false;
        if (appUpdateBlock != null) {
            softUpdate = BuildConfig.VERSION_CODE <= appUpdateBlock.getSoftUpdateVersionCode();
            forceUpdate = BuildConfig.VERSION_CODE <= appUpdateBlock.getHardUpdateVersionCode();
        }

        int actionType;
        if (forceUpdate) {
            actionType = AppUpdateType.IMMEDIATE;
        } else if (softUpdate) {
            actionType = AppUpdateType.FLEXIBLE;
        } else {
            return; // both are false, no update needed, return!
        }

        AppUpdateManager appUpdateManager = AppUpdateManagerFactory.create(this);

        Task<AppUpdateInfo> appUpdateInfoTask = appUpdateManager.getAppUpdateInfo();

        appUpdateInfoTask.addOnSuccessListener(appUpdateInfo -> {
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                try {
                    appUpdateManager.startUpdateFlowForResult(
                            appUpdateInfo,
                            actionType,
                            this,
                            MainActivityConstants.UPDATE_REQUEST_CODE);
                } catch (IntentSender.SendIntentException e) {
                    e.printStackTrace();
                }
            }
        });
    }


    // Handle the update if already in progress
    private void checkIfAppUpdateIsInProgress() {
        AppUpdateManager appUpdateManager = AppUpdateManagerFactory.create(this);
        appUpdateManager
                .getAppUpdateInfo()
                .addOnSuccessListener(
                        appUpdateInfo -> {
                            if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                                try {
                                    appUpdateManager.startUpdateFlowForResult(
                                            appUpdateInfo,
                                            AppUpdateType.IMMEDIATE,
                                            this,
                                            MainActivityConstants.UPDATE_REQUEST_CODE);
                                } catch (IntentSender.SendIntentException e) {
                                    e.printStackTrace();
                                }
                            }
                        });
    }

    /**
     * User context object should not contain empty or default bookId after login and data restore.
     * If bookId or userId is null even after login and successfull registration it can cause NPE at multiple places.
     * assign one of user created books as default book if bookId assigned during data restore or new book creation was failed.
     */
    private void validateUserContext() {
        //identify users with null book id
        if (Utility.isBlank(User.getBusinessId())) {
            try {
                //get list of non deleted user books
                List<BookEntity> bookEntityList = BusinessRepository.getInstance(Application.getAppContext()).getBusinessListRaw(User.getUserId());
                //iterate user books to find book with valid id -> bookId != null or bookId is not default System ID
                for (BookEntity bookEntity : bookEntityList) {
                    if (!Utility.isBlank(bookEntity.bookId) && !Utility.areEqual(bookEntity.bookId, User.DEF_USER_STR)) {
                        SessionManager.getInstance().setBusinessId(bookEntity.bookId);
                        break;
                    }
                }
                return;
            } catch (Exception e) {
                FirebaseCrashlytics.getInstance().setUserId(User.getUserId());
                FirebaseCrashlytics.getInstance().recordException(e);
            }
            try {
                //record invalid bookId state for debugging
                throw new IllegalStateException("user select book id is null");
            } catch (Exception e) {
                FirebaseCrashlytics.getInstance().setUserId(User.getUserId());
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
        try {
            if (AppConfigManager.getInstance().eligibleForProfileSetup() && RemoteConfigUtils.INSTANCE.shouldShowProfileSetupDialog()) {
                BookEntity book = getCurrentBook();
                if (book != null && !Utility.hasBusinessName(book.businessName)) {
                    FeaturePrefManager.getInstance().setStreaksDialogEnabled(false);
                    showOnBoardingQuestion();
                } else {
                    AppConfigManager.getInstance().setEligibleForProfileSetup(false);
                }
            }
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().setUserId(User.getUserId());
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    private void showOnBoardingQuestion() {
        int layoutParam = MATCH_PARENT;
        int onBoardingVariant = RemoteConfigUtils.OnBoarding.INSTANCE.getOnBoardingVariant();
        if (onBoardingVariant == 0) {
            ProfileCompletionDialog profileCompletionDialog = new ProfileCompletionDialog(this, this, businessName -> {
                businessProfileFormViewModel.updateBusinessName(businessName);
                return Unit.INSTANCE;
            }, AnalyticsConst.LANDING_POPUP, true, false, false, "", false);
            profileCompletionDialog.show();
            Window window = profileCompletionDialog.getWindow();
            window.setLayout(layoutParam, layoutParam);
        }
    }

    public void callLoginFromProfileTab(@NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(false);
    }

    public void callLoginFromPaymentTab(@NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(false);
    }

    @Override
    public void goToVerifyOtp(@NotNull String phone, @NotNull String countryCode, @NotNull String method) {
        otpBtSheet = VerifyOtpBottomSheetDialog.Companion.newInstance(phone, countryCode, currentEntryPoint, this);
        otpBtSheet.show(getSupportFragmentManager(), VerifyOtpBottomSheetDialog.TAG);
    }

    @Override
    public void callLoginBottomsheet(boolean openKeyboard, @NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(openKeyboard);
    }

    private void showLogin(boolean openKeyboard) {
        loginBtSheet = LoginBottomSheetDialog.Companion.newInstance(currentEntryPoint, this);
        loginBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
        loginBtSheet.keyboardState(openKeyboard);
    }

    @Override
    public void onFinishOtp() {
        BusinessRepository.getInstance(this).mergeGuestRecords();
        CashRepository.getInstance(this).mergeGuestRecords();
        ProductRepository.getInstance(this).mergeGuestRecords();
        SessionManager.getInstance().isGuestUser(false);
        SetupManager.getInstance().hasRestored(false);
        Thread mThread = new Thread() {
            @Override
            public void run() {
                TransactionUtil.backupAllTransactions();
                MainActivity.startActivityAndClearTop(MainActivity.this);
            }
        };
        mThread.start();
    }

    public boolean isUpdateAvailable() {
        PackageInfo packageInfo;
        try {
            packageInfo = getApplicationContext().getPackageManager().getPackageInfo(getPackageName(), 0);
            return packageInfo.versionCode < Long.parseLong(AppConfigManager.getInstance().getLatestVersion());
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        return false;
    }

    public void showGamifyPopup(GamifyTarget target) {
        if (RemoteConfigUtils.INSTANCE.getGamifyDialogStatus() == AppConst.GAMIFY_DIALOG
                && FeaturePrefManager.getInstance().getEnableGamifyDialog() == AppConst.GAMIFY_DIALOG) {
            int txnCount = TransactionRepository.getInstance(this).getTransactionCountWithDeletedRecords();
            if (txnCount == 1) {
                onGamifyShow(AnalyticsConst.FIRST_RECORD);
                transactionsPopUpDialog = new TransactionsPopUpDialog(this, () -> {
                    redirectToTransactionScreen(target);
                    onGamifyDismissed(false, AnalyticsConst.FIRST_RECORD);
                    return Unit.INSTANCE;
                }, () -> {
                    onGamifyDismissed(true, AnalyticsConst.FIRST_RECORD);
                    return Unit.INSTANCE;
                }, R.drawable.first_pop_up_image, getString(R.string.first_transaction_dialog_heading), getString(R.string.first_transaction_dialog_body), getString(R.string.transaction_dialog_button), "");
                transactionsPopUpDialog.getWindow().setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker);
                transactionsPopUpDialog.show();
                FeaturePrefManager.getInstance().setEnableGamifyDialog(0);
            } else if (txnCount == 3) {
                onGamifyShow(AnalyticsConst.THIRD_RECORD);
                transactionsPopUpDialog = new TransactionsPopUpDialog(this, () -> {
                    redirectToTransactionScreen(target);
                    onGamifyDismissed(false, AnalyticsConst.THIRD_RECORD);
                    return Unit.INSTANCE;
                }, () -> {
                    onGamifyDismissed(true, AnalyticsConst.THIRD_RECORD);
                    return Unit.INSTANCE;
                }, R.drawable.third_pop_up_image, getString(R.string.third_transaction_dialog_heading), getString(R.string.third_transaction_dialog_body), getString(R.string.transaction_dialog_button), "");
                transactionsPopUpDialog.getWindow().setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker);
                transactionsPopUpDialog.show();
                FeaturePrefManager.getInstance().setEnableGamifyDialog(0);
            } else if (txnCount == 5) {
                onGamifyShow(AnalyticsConst.FIFTH_RECORD);
                transactionsPopUpDialog = new TransactionsPopUpDialog(this, () -> {
                    redirectToStickersActivity();
                    onGamifyDismissed(false, AnalyticsConst.FIFTH_RECORD);
                    return Unit.INSTANCE;
                }, () -> {
                    onGamifyDismissed(true, AnalyticsConst.FIFTH_RECORD);
                    return Unit.INSTANCE;
                }, R.drawable.fifth_pop_up_image, getString(R.string.fifth_transaction_dialog_heading), getString(R.string.fifth_transaction_dialog_body), getString(R.string.download_stickers), "");
                transactionsPopUpDialog.getWindow().setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker);
                transactionsPopUpDialog.show();
                FeaturePrefManager.getInstance().setEnableGamifyDialog(0);
            }
        }
    }

    private void onGamifyDismissed(boolean shouldShowTooltip, String recordStateMessage) {
        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put(AnalyticsConst.STATE, recordStateMessage);
        Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
        if (currentFragment instanceof TransactionTabHome) {
            currentFragment = ((TransactionTabHome) currentFragment).getFragment();
            if (currentFragment instanceof CustomerTab) {
                propBuilder.put(AnalyticsConst.LAST_RECORD, AnalyticsConst.UTANG);
                ((CustomerTab) currentFragment).onGamifyDismissed(shouldShowTooltip);
            } else if (currentFragment instanceof IncomeExpenseTab) {
                propBuilder.put(AnalyticsConst.LAST_RECORD, AnalyticsConst.CASH_TRX);
                ((IncomeExpenseTab) currentFragment).onGamifyDismissed(shouldShowTooltip);
            }
        }
    }

    private void onGamifyShow(String recordStateMessage) {
        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put(AnalyticsConst.STATE, recordStateMessage);
        Fragment currentFragment = tabNavigationAdapter.getItem(tabPager.getCurrentItem());
        if (currentFragment instanceof TransactionTabHome) {
            currentFragment = ((TransactionTabHome) currentFragment).getFragment();
            if (currentFragment instanceof CustomerTab) {
                propBuilder.put(AnalyticsConst.LAST_RECORD, AnalyticsConst.UTANG);
                ((CustomerTab) currentFragment).onGamifyShow();
            } else if (currentFragment instanceof IncomeExpenseTab) {
                propBuilder.put(AnalyticsConst.LAST_RECORD, AnalyticsConst.CASH_TRX);
                ((IncomeExpenseTab) currentFragment).onGamifyShow();
            }
        }
    }

    private void redirectToStickersActivity() {
        new LoadStickerListAsync(this).execute(new Void[0]);
    }

    public void redirectToTransactionTab() {
        bottomNavigationView.setSelectedItemId(R.id.navigation_cash);
    }

    public void redirectToProfileTab() {
        bottomNavigationView.setSelectedItemId(R.id.navigation_profile);
    }

    private void redirectToTransactionScreen(GamifyTarget target) {
        Intent intent = new Intent(this, target.getClassValue());
        startActivity(intent);
    }

    @Override
    public void handleNormalBackPressedFromProfile() {
        handleNormalBackPressed();
    }

    @Override
    public void redirectToPaymentTab() {
        if (this.tabPager != null)
            setCurrentTab(tabNames.indexOf(TabName.PAYMENT));
    }

    @Override
    public void handleNormalBackPressedFromPayment() {
        handleNormalBackPressed();
    }

    private void handleNormalBackPressed() {
        boolean shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage();
        if (this.tabPager != null) {
            if (shouldShowNewHomePage) {
                setCurrentTab(0);
            } else {
                setCurrentTab(tabNames.indexOf(TabName.CUSTOMER));
            }
        }
        handleDoubleBackPress(false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == PRODUCT_ADDED_SUCCESS && resultCode == RESULT_OK) {
            if (inventoryHomeFragment != null) {
                inventoryHomeFragment.productAddedSuccess();
            }
        }

        if (requestCode == MainActivityConstants.UPDATE_REQUEST_CODE) {
            if (resultCode != RESULT_OK) {
                appUpdateRunnable.run();
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N_MR1)
    private void updateAppShortcut() {

        boolean showOldForm = RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm();

        ShortcutManager shortcutManager = getSystemService(ShortcutManager.class);

        Intent infoIntent;

        infoIntent = new Intent(this, HelpCenterActivity.class);

        infoIntent.setAction(Intent.ACTION_VIEW);
        infoIntent.setType(getString(R.string.show_profit));

        ShortcutInfo shortcutInfo = new ShortcutInfo.Builder(MainActivity.this, getString(R.string.shortcut_1))
                .setShortLabel(getString(R.string.learn_bukuwarung))
                .setLongLabel(getString(R.string.learn_bukuwarung))
                .setIntents(new Intent[]{
                        new Intent(Intent.ACTION_MAIN, Uri.EMPTY, this, MainActivity.class).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK),
                        infoIntent
                })
                .setIcon(Icon.createWithResource(this, R.drawable.ic_info))
                .setRank(1)
                .build();


        Intent transactionIntent;

        transactionIntent = NewCashTransactionActivity.createIntent(this);
        transactionIntent.putExtra(NewCashTransactionActivity.TRX_TYPE, false);

        transactionIntent.setAction(Intent.ACTION_VIEW);
        transactionIntent.setType(getString(R.string.show_profit));

        ShortcutInfo transaction = new ShortcutInfo.Builder(MainActivity.this, getString(R.string.shortcut_2))
                .setShortLabel(getString(R.string.record_transaksi))
                .setLongLabel(getString(R.string.record_transaksi))
                .setIntents(new Intent[]{
                        new Intent(Intent.ACTION_MAIN, Uri.EMPTY, this, MainActivity.class).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK),
                        transactionIntent
                })
                .setIcon(Icon.createWithResource(this, R.drawable.ic_transaksi))
                .setRank(2)
                .build();

        if (shortcutManager != null) {
            shortcutManager.setDynamicShortcuts(Arrays.asList(transaction, shortcutInfo));
        }

    }

    private void showSnackBarPpob(String productName) {
        Snackbar snackBar = Snackbar.make(findViewById(R.id.snackbar_guideline), getString(R.string.saldo_snackbar, productName), Snackbar.LENGTH_SHORT);
        findViewById(R.id.snackbar_guideline).setTag(R.id.hansel_ignore_view_excluding_children_1, true);
//        Snackbar snackBar = Snackbar.make(findViewById(R.id.snackbar_guideline), productName, Snackbar.LENGTH_SHORT);
        ImageView imgClose = new ImageView(this);
        imgClose.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        ViewGroup.LayoutParams layImageParams = new ViewGroup.LayoutParams(WRAP_CONTENT, MATCH_PARENT);
        imgClose.setImageResource(R.drawable.ic_close_button_green);
        TextView textViewAction = snackBar.getView().findViewById(R.id.snackbar_action);
        ((SnackbarContentLayout) textViewAction.getParent()).addView(imgClose, layImageParams);
        imgClose.setOnClickListener(view -> {
            AppAnalytics.trackEvent("home_banner_dismiss");
            snackBar.dismiss();
        });
        snackBar.setBackgroundTint(ContextCompat.getColor(this, R.color.green_5));
        TextView textView = (TextView) snackBar.getView().findViewById(R.id.snackbar_text);
        textView.setTextAppearance(this, R.style.SubHeading2);
        snackBar.setTextColor(ContextCompat.getColor(this, R.color.green_80));
        ActionBar.LayoutParams layoutParams = new ActionBar.LayoutParams(snackBar.getView().getLayoutParams());
        layoutParams.gravity = Gravity.TOP;
        snackBar.getView().setLayoutParams(layoutParams);
        snackBar.show();
    }

    private void refreshTabs() {
        customerTab = null;
        transactionTab = null;
        incomeExpenseTab = null;
        profileTabFragment = null;
        paymentTabFragment = null;
        inventoryHomeFragment = null;
        setTabs();
    }

    private void redirect(String link) {
        SourceLink sourceLink = new SourceLink(this, link);
        neuro.route(
                sourceLink,
                this::startActivity,
                () -> {
                    SessionManager.getInstance().setFbDeeplinkData(false);
                    AppAnalytics.trackEvent("open_deep_link", "activity", link);
                    return null;
                },
                (throwable) -> {
                    redirectWithLegacyLink(link);
                    return null;
                }
        );
    }

    private void redirectWithLegacyLink(String query) {
        String link = DEEPLINK_INTERNAL_URL + "?" + query;
        Uri uri = Uri.parse(link);

        converter = new UriToDeepLinkDataConverter();
        DeepLinkData deepLinkData = converter.convert(uri);
        if (deepLinkData == null) return;

        SourceLink sourceLink = new SourceLink(this, link + "&launch=2");
        neuro.route(
                sourceLink,
                this::startActivity,
                () -> {
                    SessionManager.getInstance().setFbDeeplinkData(false);
                    AppAnalytics.trackEvent("open_deep_link", "activity", deepLinkData.data);
                    return null;
                },
                (throwable) -> {
                    Log.e(TAG, "Something wrong when redirecting to processing", throwable);
                    return null;
                }
        );
    }

    @Override
    public void setKycInfo() {
        FeaturePrefManager.getInstance().hasCompletedKYC(true);
        KycTier kycTier = PaymentPrefManager.Companion.getInstance().getKycTier();
        View premiumLayout = findViewById(R.id.premium_layout);
        View topMargin = findViewById(R.id.top_margin);
        View blueBg = findViewById(R.id.blue_bg);
        ImageView badgeIcon = findViewById(R.id.iv_verified_badge);
        TextView kycTitle = findViewById(R.id.verified_title_txt);
        TextView kycSubtitle = findViewById(R.id.verified_subtitle_txt);
        premiumLayout.setVisibility(View.VISIBLE);
        blueBg.setVisibility(View.VISIBLE);
        topMargin.setBackgroundResource(R.color.blue_80);
        switch (kycTier) {
            case NON_KYC: {
                badgeIcon.setImageResource(R.drawable.ic_kyc_badge_standard);
                kycTitle.setText(R.string.membership_standard);
                kycSubtitle.setText(R.string.verify_identity_for_full_service);
                break;
            }
            case ADVANCED: {
                badgeIcon.setImageResource(R.drawable.ic_kyc_badge_premium);
                kycTitle.setText(R.string.membership_premium);
                kycSubtitle.setText(R.string.verify_data_complete_premium);
                break;
            }
            case SUPREME: {
                badgeIcon.setImageResource(R.drawable.ic_kyc_badge_priority);
                kycTitle.setText(R.string.membership_priority);
                kycSubtitle.setText(R.string.verify_data_complete_prioritas);
                break;
            }
        }
    }

    public void sendAppsFlyerId() {
        try {
            if (!SessionManager.getInstance().isGuestUser() && !AppsFlyerPrefManager.Companion.getInstance().getHasFinishedForId(AppsFlyerPrefManager.SEND_APPS_FLYER_ID + User.getUserId() + SessionManager.getInstance().getAdvertisingId()) && appsFlyerViewModel != null) {
                AppsFlyerPrefManager.Companion.getInstance().setHasFinishedForId(AppsFlyerPrefManager.SEND_APPS_FLYER_ID + User.getUserId() + SessionManager.getInstance().getAdvertisingId());
                appsFlyerViewModel.sendAppsFlyerId(AppsFlyerLib.getInstance().getAppsFlyerUID(Application.getAppContext()),
                        SessionManager.getInstance().getAdvertisingId());
            }
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void saveUpdatedBookEntity(BookEntity bookEntity) {
        businessViewModel.saveUpdateBookEntity(bookEntity);
    }

    private void redirectToBusinessCreationFlow() {
        if (RemoteConfigUtils.INSTANCE.getBusinessProfileVariant() == RemoteConfigConst.BUSINESS_PROFILE_VARIANT_OLD) {
            Intent intent = new Intent(this, CreateBusinessActivity.class);
            startActivity(intent);
        } else if (RemoteConfigUtils.INSTANCE.getBusinessProfileVariant() == RemoteConfigConst.BUSINESS_PROFILE_VARIANT_NEW_WEB) {
            String url = RemoteConfigUtils.INSTANCE.getBusinessProfileUrl();
            Intent webViewIntent = BusinessProfileWebviewActivity.Companion.createIntent(this, url, "", "", true);
            webViewIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
            webViewIntent.putExtra(BusinessProfileWebviewActivity.WEBVIEW_PARAM_IS_DAILY_UPDATE, true);
            this.startActivity(webViewIntent);
        } else {
            Intent intent2 = CreateBusinessProfileActivity.Companion.createIntent(this, "", false, CreateBusinessProfileActivity.UseCase.DEFAULT);
            startActivity(intent2);
        }
    }

    @Override
    public void handleNormalBackPressedFromHome() {
        handleDoubleBackPress(true);
    }
}
