package com.bukuwarung.activities.successmessage

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.R
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityProfileSuccessMessageBinding
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.RemoteConfigUtils.shouldSendSmsTransaction
import com.bukuwarung.utils.RemoteConfigUtils.shouldShowNewPosInvoice

class ProfileSuccessMessageActivity : AppCompatActivity() {
    private lateinit var binding: ActivityProfileSuccessMessageBinding
    private val showOldForm: Boolean by lazy { RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm() }
    private val isExitDialogEnabled: Boolean by lazy { RemoteConfigUtils.shouldShowExitDialog() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProfileSuccessMessageBinding.inflate(layoutInflater)
        setContentView(binding.root)



        binding.closeMessage.setOnClickListener { onBackPressed() }

        binding.recordUtang.setOnClickListener {
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PROFILE)
            propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled)

            propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, RemoteConfigUtils.FavoriteCustomer.isEnabled())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder)
            val utangIntent = Intent(this, AddCustomerActivity::class.java)
            utangIntent.putExtra("ShowCustomerTutorial", !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN))
            startActivity(utangIntent)
            finish()
        }

        binding.recordTransaksi.setOnClickListener {
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PROFILE)
            propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled)
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, shouldSendSmsTransaction())
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, shouldShowNewPosInvoice())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder)
            val intent =
                NewCashTransactionActivity.createIntent(this, true, !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN))
            startActivity(intent)
            finish()
        }

    }
}