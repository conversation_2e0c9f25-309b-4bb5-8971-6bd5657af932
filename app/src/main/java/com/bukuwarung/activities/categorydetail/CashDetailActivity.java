package com.bukuwarung.activities.categorydetail;

import android.content.Intent;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModelProvider;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.categorydetail.tasks.UpdateCashAsyncTask;
import com.bukuwarung.activities.expense.category.Category;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.transaction.category.TransactionListViewModel;
import com.bukuwarung.activities.transaction.category.TransactionViewModelFactory;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.utils.NotificationUtils;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public final class CashDetailActivity extends AppActivity implements OnClickListener {

    public CashCategoryEntity customer;

    private EditText name;
    private String customerId;


    private LinearLayout deleteLayout;
    private MaterialButton save;

    private TransactionListViewModel viewModel;

    public static final CashCategoryEntity getCustomer(CashDetailActivity cashDetailActivity) {
        return cashDetailActivity.customer;
    }

    public static final EditText getName(CashDetailActivity cashDetailActivity) {
        return cashDetailActivity.name;
    }

    public static View getDeleteLayout(CashDetailActivity cashDetailActivity) {
        return cashDetailActivity.deleteLayout;
    }


    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);

        setContentView((int) R.layout.activity_category_info);
        View toolbarView = findViewById(R.id.toolbar);
        setSupportActionBar((Toolbar) toolbarView);

        this.customerId = getIntent().getStringExtra("categoryId");
        this.deleteLayout = findViewById(R.id.deleteContainer);
        this.save = findViewById(R.id.save);
        this.name = findViewById(R.id.customerName);

        try {


            this.save.setOnClickListener(this);

            TextView toolBarLabel = findViewById(R.id.toolBarTitle);
            toolBarLabel.setText(getString(R.string.edit_category_details));

            ActionBar supportActionBar = getSupportActionBar();
            supportActionBar.setDisplayHomeAsUpEnabled(true);

            FragmentActivity fragmentActivity = this;
            Application application = new Application();
            this.viewModel = new ViewModelProvider(fragmentActivity, new TransactionViewModelFactory(application, this.customerId, "", false, "", "")).get(TransactionListViewModel.class);

            TransactionListViewModel transactionListViewModel = this.viewModel;
            LiveData customerEntity = transactionListViewModel.getCategoryLiveData();
            customerEntity.observe(this, new CashDetailObserver(this));


        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
    }


    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == 16908332) {
            onBackPressed();
        }
        return true;
    }

    public void onClick(View view) {
        try {
            switch (view.getId()) {
                case R.id.save:
                    String categoryName = this.name.getText().toString();
                    if (!(categoryName == null || categoryName.length() == 0)) {
                        this.customer.name = this.name.getText().toString();
                        UpdateCashAsyncTask updateCashAsyncTask = new UpdateCashAsyncTask();
                        CashCategoryEntity[] customerEntityArr = new CashCategoryEntity[1];
                        customerEntityArr[0] = this.customer;
                        updateCashAsyncTask.execute(customerEntityArr);

                        String oldCategoryId = this.customer.cashCategoryId;
                        if (oldCategoryId.contains("::"))
                            oldCategoryId = oldCategoryId.split("::")[0];

                        String newName = this.name.getText().toString();

                        Category oldCategory = new Category(
                                oldCategoryId,
                                this.customer.name,
                                this.customer.name,
                                this.customer.type
                        );

//                        AppConfigManager.getInstance().editCashCategory(
//                                oldCategory,
//                                newName,
//                                this.customer.type
//                        );

                        onBackPressed();
                    } else {
                        NotificationUtils.alertToast(getString(R.string.enter_category_name));
                        return;
                    }
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        if (i == PermissionConst.REQ_TAKE_PICTURE_PERMISSON) {
            if (VERSION.SDK_INT >= 23) {
                if (!(ContextCompat.checkSelfPermission(this, "android.permission.CAMERA") == 0)) {
                    NotificationUtils.alertToast(getString(R.string.camera_permission_denied_message));
                    return;
                }
                AppAnalytics.trackEvent("granted_camera_permission");
            }
        }
    }

}
