package com.bukuwarung.activities.geolocation.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.viewmodel.GeoLocationViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentBusinessAddressBinding
import com.bukuwarung.utils.emptyLambda
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.validateEditTextsNotEmpty
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessAddressFragment : BaseFragment() {
    private lateinit var binding: FragmentBusinessAddressBinding

    private val vm: GeoLocationViewModel by activityViewModels()

    // object for event tracking
    private var address: Address? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = FragmentBusinessAddressBinding.inflate(inflater)
        .also { binding = it }
        .root

    override fun setupView(view: View) {
        binding.apply {
            tvChangeAddress.setOnClickListener {
                AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BUSINESS_ADDRESS)
                    put(AnalyticsConst.ACTION, if (vm.isEditingAddressForFirstTime().isTrue) AnalyticsConst.NEW else AnalyticsConst.EDIT)
                    AppAnalytics.trackEvent(AnalyticsConst.EDIT_BUSINESS_ADDRESS, this)
                }

                vm.onEventReceipt(GeoLocationViewModel.Event.OnChangeAddressRequest)
            }

            btnSave.setOnClickListener {
                trackSaveButtonClickEvent()
                vm.onEventReceipt(GeoLocationViewModel.Event.OnSaveButtonClick)
            }

            etWard.addTextChangedListener {
                vm.onEventReceipt(GeoLocationViewModel.Event.OnWardChanged(it.toString()))
                validateForm()
            }
            etPostalCode.addTextChangedListener {
                vm.onEventReceipt(GeoLocationViewModel.Event.OnPostalCodeChanged(it.toString()))
                validateForm()
            }
            etAddress.addTextChangedListener {
                vm.onEventReceipt(GeoLocationViewModel.Event.OnFullAddressEdited(it.toString()))
                validateForm()
            }
        }

        validateForm()
    }

    override fun subscribeState() {
        vm.state.observe(viewLifecycleOwner, Observer { eventWrapper ->
            when (val state = eventWrapper.peekContent()) {
                is GeoLocationViewModel.State.OnLocationSelected -> {
                    address = state.address
                    populateAddress(state.address)
                }
                is GeoLocationViewModel.State.OnLocationResult -> emptyLambda
                else -> {}
            }
        })
    }

    private fun populateAddress(address: Address?) {
        address ?: return
        binding.apply {
            tvChosenAddress.text = address.name
            etProvince.setText(address.province)
            etCity.setText(address.city)
            etDistrict.setText(address.district)
            etWard.setText(address.subDistrict)
            etPostalCode.setText(address.postalCode)
            etAddress.setText(address.fullAddress)
        }

        validateForm()
    }

    private fun validateForm() {
        binding.apply {
            btnSave.isEnabled = validateEditTextsNotEmpty(
                etProvince,
                etCity,
                etDistrict,
                etWard,
                etPostalCode,
                etAddress
            )
        }
    }

    private fun trackSaveButtonClickEvent(){
        address?.let {
            AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT_NEW, vm.getEntryPoint())
                put(AnalyticsConst.ACTION, if (vm.isEditingAddressForFirstTime().isTrue) AnalyticsConst.NEW else AnalyticsConst.EDIT)
                put(AnalyticsConst.BUSINESS_PROVINCE, it.province)
                put(AnalyticsConst.BUSINESS_CITY, it.city)
                put(AnalyticsConst.BUSINESS_DISTRICT, it.district)
                put(AnalyticsConst.BUSINESS_SUB_DISTRICT, it.subDistrict)
                put(AnalyticsConst.BUSINESS_POSTAL_CODE, it.postalCode)
                put(AnalyticsConst.BUSINESS_STREET_ADDRESS, it.fullAddress)


                AppAnalytics.trackEvent( AnalyticsConst.SAVE_BUSINESS_ADDRESS,this)
            }
        }
    }

    companion object{
        const val TAG = "BusinessAddressFragment"
    }
}