package com.bukuwarung.activities.geolocation.view

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.IntentSender
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.work.WorkInfo
import com.bukuwarung.R
import com.bukuwarung.activities.geolocation.viewmodel.GeoLocationViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.databinding.FragmentGeolocationBinding
import com.bukuwarung.location.LocationUtil
import com.bukuwarung.location.toUserLocationDto
import com.bukuwarung.utils.*
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GeoLocationFragment : BaseFragment() {
    private lateinit var binding: FragmentGeolocationBinding

    private val viewModel: GeoLocationViewModel by activityViewModels()

    private val addressAdapter: AddressAdapter by lazy {
        AddressAdapter { address ->
            viewModel.onEventReceipt(GeoLocationViewModel.Event.OnLocationSelected(address))
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = FragmentGeolocationBinding.inflate(inflater)
        .also { binding = it }
        .root

    override fun setupView(view: View) {
        binding.apply {
            errorView.setup {
                setTitle { getString(R.string.no_connection_title) }
                setMessage { getString(R.string.no_connection_message) }
                setImageRes { R.drawable.ic_no_inet }
                setButtonTextAndAction(getString(R.string.retry)) {
                    {
                        viewModel.onEventReceipt(GeoLocationViewModel.Event.SearchLocation(tvQuery.text.toString()))
                    }
                }
            }

            tvCurrentLocation.setOnClickListener {
                InputUtils.hideKeyBoardWithCheck(requireActivity())
                if (PermissonUtil.hasLocationPermission()) {
                    useCurrentLocation()
                } else {
                    requestPermissions(
                        arrayOf(
                            Manifest.permission.ACCESS_COARSE_LOCATION,
                            Manifest.permission.ACCESS_FINE_LOCATION
                        ),
                        PermissionConst.ACCESS_LOCATION
                    )
                }
            }

            tvQuery.setupForSearch(lifecycleScope) { query ->
                binding.pbContainer.apply {
                    visibility = View.VISIBLE
                    setBackgroundColor(requireContext().getColorCompat(R.color.transparent))
                }
                viewModel.onEventReceipt(GeoLocationViewModel.Event.SearchLocation(query))
            }

            rvAddress.apply {
                adapter = addressAdapter
                layoutManager = LinearLayoutManager(requireContext())
                itemAnimator = DefaultItemAnimator()
                addItemDecoration(DividerItemDecoration(requireContext(), RecyclerView.VERTICAL))
            }

            tvQuery.requestFocus()

            tvNotFound.text = SpannableStringBuilder()
                .append(getString(R.string.no_address_found_message))
                .boldText(getString(R.string.use_current_location), true)
        }
    }

    override fun subscribeState() {
        viewModel.state.observe(viewLifecycleOwner, Observer { eventWrapper ->
            when (val state = eventWrapper.peekContent()) {
                is GeoLocationViewModel.State.OnLocationResult -> {
                    binding.pbContainer.visibility = View.GONE
                    addressAdapter.updateData(state.locations)

                    binding.apply {
                        tvNotFound.visibility = state.locations.isEmpty().asVisibility()
                        rvAddress.visibility = state.locations.isNotEmpty().asVisibility()
                        errorView.visibility = View.GONE
                    }
                }
                is GeoLocationViewModel.State.OnLocationSelected -> {
                    binding.pbContainer.visibility = View.GONE
                }
                is GeoLocationViewModel.State.OnNetworkStateChange -> {
                    binding.apply {
                        pbContainer.visibility = View.GONE
                        rvAddress.visibility = state.hasInternet.asVisibility()
                        errorView.visibility = (!state.hasInternet).asVisibility()
                    }
                }
                else -> {}
            }
        })
    }

    @SuppressLint("MissingPermission")
    private fun useCurrentLocation() {
        InputUtils.hideKeyBoardWithCheck(requireActivity())

        if (!Utility.hasInternet()) return
        val locationManager =
            requireContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager

        if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) &&
            !locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        ) {
            showToast(getString(R.string.location_is_disabled))
            checkForLocationSettings()
            return
        }


        binding.pbContainer.apply {
            visibility = View.VISIBLE
            setBackgroundColor(requireContext().getColorCompat(R.color.dialog_transparent))
        }

        try {
            val workerId =
                LocationUtil.getLocation(requireContext(), AnalyticsConst.BUSINESS_PROFILE)
            if (workerId != null) {
                workerId.observe(viewLifecycleOwner, Observer { info->
                    Log.d("GELOCATION", info.state.javaClass.toString())
                    when(info.state){
                        WorkInfo.State.ENQUEUED,  WorkInfo.State.RUNNING -> {
                            Log.d("GEOLOCATION", "enqueue")
                            // Loading is already shown
                        }
                        WorkInfo.State.SUCCEEDED -> {
                            val userLocationDto = info.outputData.toUserLocationDto()
                            viewModel.onEventReceipt(GeoLocationViewModel.Event.UseCurrentLocation(userLocationDto))
                        }
                        WorkInfo.State.FAILED, WorkInfo.State.BLOCKED, WorkInfo.State.CANCELLED -> {
                            binding.pbContainer.visibility = View.GONE
                            showToast(R.string.cant_find_location)
                        }
                        else -> {}
                    }

                })
            } else {

                showToast(R.string.error_happened)
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                var grantStatus = AnalyticsConst.STATUS_DENY
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    grantStatus = AnalyticsConst.STATUS_ALLOW
                    useCurrentLocation()
                }

                AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.STATUS, grantStatus)

                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST, this)

                }
            }
        }
    }

    private fun checkForLocationSettings() {
        activity?.let {
            val builder = LocationSettingsRequest.Builder()
                .addLocationRequest(LocationRequest.create().apply {
                    priority = LocationRequest.PRIORITY_HIGH_ACCURACY
                })
            val task = LocationServices.getSettingsClient(it)
                .checkLocationSettings(builder.build())
            task.addOnFailureListener { exception ->
                if (exception is ResolvableApiException) {
                    // Location settings are not satisfied, but this can be fixed
                    // by showing the user a dialog.
                    try {
                        // Show the dialog by calling startResolutionForResult(),
                        // and check the result in onActivityResult().
                        // Since startActivityForResult is not started from this fragment,
                        // results are passed on to the activity
                        exception.startResolutionForResult(
                            it,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (sendEx: IntentSender.SendIntentException) {
                        // Ignore the error.
                    }
                }
            }
        }
    }

    companion object {
        const val TAG = "GeoLocationFragment"
        const val REQUEST_CHECK_SETTINGS = 42
    }
}