package com.bukuwarung.activities.geolocation.view


import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.location.LocationManager
import android.net.Uri
import android.os.Looper
import android.provider.Settings
import androidx.activity.viewModels
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.location.LocationManagerCompat
import com.bukuwarung.R
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.viewmodel.MapsViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.databinding.ActivityMapsBinding
import com.bukuwarung.lib.webview.camera.CameraKycActivity
import com.bukuwarung.lib.webview.geocoding.LocationNotDetectedBottomSheet
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.payments.data.model.MapConfig
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MapsActivity : BaseActivity(), OnMapReadyCallback,
    LocationNotDetectedBottomSheet.ICommunicator {

    private var map: GoogleMap? = null
    private lateinit var binding: ActivityMapsBinding

    private val viewModel: MapsViewModel by viewModels()
    private val useCase by lazy { intent?.getSerializableExtra(USE_CASE) as? UseCase ?: UseCase.LOCATION }
    private val imageType by lazy { intent?.getStringExtra(IMAGE_TYPE) }
    private val mapConfig by lazy { intent?.getParcelableExtra(MAP_CONFIG) as? MapConfig }
    private var currentLocationMarkedOnMap: Boolean = false
    private lateinit var fusedLocationProviderClient: FusedLocationProviderClient
    private var hasOpenedSettings: Boolean = false
    private val zoom: Float = mapConfig?.zoom ?: DEFAULT_ZOOM
    private val defaultLocation = mapConfig?.defaultLocation ?: DEFAULT_LOCATION
    private var locationRequestStartedTimeInMillis = 0L
    private var locationFoundTimeInMillis = 0L
    private val locationRequest: LocationRequest = LocationRequest.create().apply {
        interval = mapConfig?.interval ?: DEFAULT_INTERVAL
        fastestInterval = mapConfig?.fastestInterval ?: DEFAULT_FASTEST_INTERVAL
        priority = mapConfig?.priority ?: DEFAULT_PRIORITY
        maxWaitTime = mapConfig?.maxWaitTime ?: DEFAULT_MAX_WAIT_TIME
    }

    private var locationCallback: LocationCallback = object : LocationCallback() {
        override fun onLocationResult(locationResult: LocationResult) {
            val locationList = locationResult.locations
            if (locationList.isNotEmpty()) {
                val location = locationList.last()
                location?.let {
                    if (!currentLocationMarkedOnMap) {
                        locationFoundTimeInMillis = System.currentTimeMillis()
                        pinLocationOnMap(LatLng(it.latitude, it.longitude))
                        map?.isMyLocationEnabled = true
                        map?.uiSettings?.isMyLocationButtonEnabled = true
                        currentLocationMarkedOnMap = true
                    }
                }
            }
        }
    }

    companion object {
        private const val GPS_REQUEST_CODE = 777
        private const val USE_CASE = "use_case"
        private const val IMAGE_TYPE = "image_type"
        private const val MAP_CONFIG = "map_config"
        private const val DEFAULT_INTERVAL: Long = 30
        private const val DEFAULT_FASTEST_INTERVAL: Long = 10
        private const val DEFAULT_MAX_WAIT_TIME: Long = 60
        private const val DEFAULT_PRIORITY = LocationRequest.PRIORITY_HIGH_ACCURACY
        private const val DEFAULT_ZOOM = 13F
        // Default location is National Monument, Jakarta
        private val DEFAULT_LOCATION = LatLng(-6.171808417274204, 106.82718254005289)

        fun createIntent(context: Context, useCase: UseCase? = UseCase.LOCATION, imageType: String? = null, mapConfig: MapConfig?): Intent {
            val i = Intent(context, MapsActivity::class.java)
            i.putExtra(USE_CASE, useCase)
            i.putExtra(IMAGE_TYPE, imageType)
            i.putExtra(MAP_CONFIG, mapConfig)
            return i
        }
    }

    override fun setViewBinding() {
        binding = ActivityMapsBinding.inflate(layoutInflater)
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(this)
        setContentView(binding.root)
    }

    override fun setupView() {
        getMapAsync()
        with(binding.includeToolBar) {
            tbPpob.navigationIcon =
                ContextCompat.getDrawable(this@MapsActivity, R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                InputUtils.hideKeyBoardWithCheck(this@MapsActivity)
                onBackPressed()
            }
            ivHelp.hideView()
            tvHelp.hideView()
            toolBarLabel.text = getString(R.string.confirm_store_location)
        }
    }

    private fun getMapAsync() {
        val mapFragment = supportFragmentManager
            .findFragmentById(R.id.map) as SupportMapFragment
        mapFragment.getMapAsync(this)
    }

    override fun onMapReady(googleMap: GoogleMap) {
        map = googleMap
        map?.setOnMapClickListener { latLng ->
            pinLocationOnMap(latLng)
        }
        moveToDefaultLocation()
        checkLocationPermission()
    }

    private fun pinLocationOnMap(latLng: LatLng) {
        map?.clear()
        map?.addMarker(
            MarkerOptions()
                .position(latLng)
                .icon(bitmapDescriptorFromVector(this@MapsActivity, R.drawable.ic_location_marker))
        )
        map?.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, zoom))
        viewModel.geocodeAddress(latLng)
    }

    private fun bitmapDescriptorFromVector(context: Context, vectorResId: Int): BitmapDescriptor? {
        return ContextCompat.getDrawable(context, vectorResId)?.run {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
            val bitmap = Bitmap.createBitmap(intrinsicWidth, intrinsicHeight, Bitmap.Config.ARGB_8888)
            draw(Canvas(bitmap))
            BitmapDescriptorFactory.fromBitmap(bitmap)
        }
    }

    private fun moveToDefaultLocation() {
        pinLocationOnMap(defaultLocation)
        map?.uiSettings?.isMyLocationButtonEnabled = false
        map?.isMyLocationEnabled = false
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is MapsViewModel.Event.AddressFound -> {
                    with(binding) {
                        tvLocation.text = it.address.fullAddress
                        clAddressLayout.showView()
                        btnConfirm.setSingleClickListener {
                            addressConfirmed(it.address)
                        }
                    }
                }
                is MapsViewModel.Event.AddressError -> {
                    with(binding) {
                        tvLocation.text = getString(R.string.adress_error)
                        clAddressLayout.showView()
                        btnConfirm.isEnabled = false
                    }
                }
            }
        }
    }

    private fun addressConfirmed(address: Address) {
        trackLocationEvent()
        if (useCase == UseCase.LOCATION_AND_IMAGE) {
            val intent = CameraKycActivity.createIntent(this@MapsActivity, imageType)
            intent.putExtra(Constant.ADDRESS, Gson().toJson(address))
            intent.flags = Intent.FLAG_ACTIVITY_FORWARD_RESULT
            startActivity(intent)
        } else {
            setResult(Activity.RESULT_OK, Intent()
                .putExtra(Constant.ADDRESS, Gson().toJson(address)))
        }
        finish()
    }

    private fun checkLocationPermission() {
        if (PermissonUtil.hasLocationPermission()) {
            startLocationCallback()
        } else {
            if (ActivityCompat.shouldShowRequestPermissionRationale(
                    this, Manifest.permission.ACCESS_FINE_LOCATION)) {
                showLocationNotDetectedBottomSheet()
            } else {
                PermissonUtil.requestLocationPermission(this@MapsActivity)
            }
        }
    }

    private fun startLocationCallback() {
        val builder = LocationSettingsRequest.Builder()
            .addLocationRequest(locationRequest)
        LocationServices
            .getSettingsClient(this)
            .checkLocationSettings(builder.build())
            .addOnSuccessListener(this) {
                startLocationUpdates()
            }
            .addOnFailureListener(this) { ex: Exception? ->
                if (ex is ResolvableApiException) {
                    // Location settings are NOT satisfied,  but this can be fixed  by showing the user a dialog.
                    try {
                        // Show the dialog by calling startResolutionForResult(),  and check the result in onActivityResult().
                        ex.startResolutionForResult(this@MapsActivity, GPS_REQUEST_CODE)
                    } catch (sendEx: IntentSender.SendIntentException) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                }
            }
    }

    private fun startLocationUpdates() {
        locationRequestStartedTimeInMillis = System.currentTimeMillis()
        fusedLocationProviderClient.requestLocationUpdates(
            locationRequest,
            locationCallback,
            Looper.getMainLooper()
        )
    }

    override fun onResume() {
        super.onResume()
        if (PermissonUtil.hasLocationPermission() && hasOpenedSettings) {
            hasOpenedSettings = false
            if (isLocationEnabled()) {
                startLocationUpdates()
            } else {
                startLocationCallback()
            }
        } else {
            if (hasOpenedSettings) {
                showLocationNotDetectedBottomSheet()
                hasOpenedSettings = false
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (PermissonUtil.hasLocationPermission()) {
            fusedLocationProviderClient.removeLocationUpdates(locationCallback)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (GPS_REQUEST_CODE == requestCode) {
            if (RESULT_OK == resultCode) {
                startLocationUpdates()
            } else {
                showLocationNotDetectedBottomSheet()
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startLocationCallback()
                } else {
                    showLocationNotDetectedBottomSheet()
                }
            }
            else -> super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        }
    }

    override fun handleBottomSheetClick(useCase: LocationNotDetectedBottomSheet.UseCase) {
        if (!PermissonUtil.hasLocationPermission()) {
            if (!ActivityCompat.shouldShowRequestPermissionRationale(this,
                    Manifest.permission.ACCESS_FINE_LOCATION)) {
                hasOpenedSettings = true
                startActivity(
                    Intent(
                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                        Uri.fromParts("package", this.packageName, null),
                    ),
                )
            } else {
                PermissonUtil.requestLocationPermission(this@MapsActivity)
            }
        } else if (!isLocationEnabled()) {
            startLocationCallback()
        }
    }

    private fun isLocationEnabled(): Boolean {
        val manager: LocationManager = this.getSystemService(LOCATION_SERVICE) as LocationManager
        return LocationManagerCompat.isLocationEnabled(manager)
    }

    private fun showLocationNotDetectedBottomSheet() {
        LocationNotDetectedBottomSheet.createIntent(isCancellable = true).show(supportFragmentManager, LocationNotDetectedBottomSheet.TAG)
    }

    private fun trackLocationEvent() {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_KYB_TAKE_STORE_PHOTO_CLICKED,
            AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.GET_LOCATION_PERMISSION, if (PermissonUtil.hasLocationPermission()) AnalyticsConst.YES else AnalyticsConst.NO)
                put("get_location_success_status", if (currentLocationMarkedOnMap) AnalyticsConst.YES else AnalyticsConst.NO)
                put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.KYB)
                if (currentLocationMarkedOnMap) {
                    put(AnalyticsConst.GET_LOCATION_TIME_IN_SECONDS, (locationFoundTimeInMillis - locationRequestStartedTimeInMillis)/1000)
                }
            }, true, true, false, true
        )
    }

    enum class UseCase {
        LOCATION, LOCATION_AND_IMAGE
    }
}