package com.bukuwarung.activities.selfreminder.viewmodel;

import android.app.Application;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.activities.selfreminder.adapter.dataholder.EmptyReminderDataHolder;
import com.bukuwarung.activities.selfreminder.adapter.dataholder.SelfReminderDataHolder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.database.entity.SelfReminderEntity;
import com.bukuwarung.database.repository.SelfReminderRepository;
import com.bukuwarung.session.User;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.hilt.android.lifecycle.HiltViewModel;

@HiltViewModel
public final class SelfReminderViewModel extends AndroidViewModel {

    private MediatorLiveData<List<DataHolder>> liveDataMerger = new MediatorLiveData<>();
    private List<SelfReminderEntity> selfReminderList;

    @Inject
    public SelfReminderViewModel(Application application) {
        super(application);
        this.liveDataMerger.addSource(SelfReminderRepository.getInstance(application).getObservableReminderByUser(User.getUserId()), new Observer<List<SelfReminderEntity>>() {
            @Override
            public void onChanged(List<SelfReminderEntity> list) {
                selfReminderList = list;
                convertToViewObject();
            }
        });
    }

    public final LiveData<List<DataHolder>> getDataHolderList() {
        return this.liveDataMerger;
    }

    public void convertToViewObject() {
        ArrayList convertedList = new ArrayList();
        if (selfReminderList != null) {
            for (SelfReminderEntity selfReminderEntity : selfReminderList) {
                convertedList.add(new SelfReminderDataHolder(selfReminderEntity));
            }
            convertedList.add(new DataHolder.LastRowHolder());
        } else {
            convertedList.add(new EmptyReminderDataHolder());
        }
        this.liveDataMerger.setValue(convertedList);
    }
}
