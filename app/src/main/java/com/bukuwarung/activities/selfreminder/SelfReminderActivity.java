package com.bukuwarung.activities.selfreminder;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.selfreminder.adapter.SelfReminderAdapter;
import com.bukuwarung.activities.selfreminder.viewmodel.SelfReminderViewModel;
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.enums.ReminderCategory;
import com.bukuwarung.database.repository.SelfReminderRepository;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;

import java.util.ArrayList;
import java.util.List;

import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public final class SelfReminderActivity extends AppActivity implements View.OnClickListener {
    Toolbar toolbar;
    public SelfReminderViewModel selfReminderListViewModel;
    private RecyclerView selfReminderRecyclerView;
    private SelfReminderAdapter adapter;
    private MaterialButton addReminderBtn;

    private String from = null;

    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);

        setContentView((int) R.layout.activity_self_reminder);
        this.toolbar = findViewById(R.id.toolbar);

        try{
            if(getIntent().hasExtra("reminderHoursTime")
                    && getIntent().hasExtra("reminderMinutesTime")
                    && getIntent().hasExtra("notes")
                    && getIntent().hasExtra("target")) {
                int reminderHoursTime = Integer.parseInt(getIntent().getExtras().getString("reminderHoursTime", "18"));
                int reminderMinutesTime = Integer.parseInt(getIntent().getExtras().getString("reminderMinutesTime", "00"));
                String notes = getIntent().getExtras().getString("notes", "");
                int target = Integer.parseInt(getIntent().getExtras().getString("target", "1"));
                ReminderCategory reminderCategory = target == 0? ReminderCategory.UTANG : target == 1?ReminderCategory.TRANSAKSI:ReminderCategory.PAYMENT;

                SelfReminderRepository.getInstance(this).saveReminder(reminderHoursTime, reminderMinutesTime, notes, reminderCategory, SelfReminderConstants.REMINDER_SOUND_ON);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        if (getIntent().hasExtra("from")) {
            from = getIntent().getStringExtra("from");
        }

        toolbar.findViewById(R.id.backBtn).setOnClickListener(this);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(false);

        initReminderListView();
        this.addReminderBtn = findViewById(R.id.addReminderBtnBottom);
        addReminderBtn.setOnClickListener(this);

    }

    private void initReminderListView() {

        this.selfReminderRecyclerView = findViewById(R.id.selfReminderRecyclerView);
        this.selfReminderListViewModel = new ViewModelProvider(this).get(SelfReminderViewModel.class);
        List arrayList = new ArrayList();
        this.adapter = new SelfReminderAdapter(arrayList, this.selfReminderRecyclerView, this,this);
        this.selfReminderRecyclerView.setAdapter(this.adapter);
        this.selfReminderRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        this.selfReminderRecyclerView.getRecycledViewPool().setMaxRecycledViews(Tag.SELF_REMINDER_DATA_VIEW, 0);

        LifecycleOwner lifecycleOwner = this;
        this.selfReminderListViewModel.getDataHolderList().observe(lifecycleOwner, new Observer<List<? extends DataHolder>>() {
            @Override
            public void onChanged(List<? extends DataHolder> list) {
                // Different Activites are shown based on reminders are present or not
                if(list == null || list.isEmpty() || list.size() <= 1){
                    findViewById(R.id.emptyView).setVisibility(View.VISIBLE);
                    findViewById(R.id.dataView).setVisibility(View.GONE);
                }else {
                    findViewById(R.id.emptyView).setVisibility(View.GONE);
                    findViewById(R.id.dataView).setVisibility(View.VISIBLE);
                    adapter.setDataHolderList(list);
                }
            }
        });

    }

    @Override
    public void onBackPressed() {
        if (RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage()) {
            MainActivity.startActivitySingleTopToTab(this, TabName.HOME);
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.OTHERS);
        }
    }

    @Override
    public void onClick(View view) {
        if (Utility.hasInternet()) {
            Intent intent = new Intent(this, SetSelfReminderActivity.class);
            intent.putExtra("from", from);
            switch (view.getId()) {
                case R.id.backBtn:
                    onBackPressed();
                    break;
                case R.id.addReminderBtnBottom:
                    startActivity(intent);
                    AppAnalytics.trackEvent("self_reminder_create");
                    break;
            }
        } else {
            new CommonConfirmationBottomSheet().showNoInternetBottomSheet(this, getSupportFragmentManager());
        }
    }
}
