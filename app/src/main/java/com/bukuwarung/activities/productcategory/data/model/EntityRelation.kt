package com.bukuwarung.activities.productcategory.data.model

import androidx.annotation.Keep
import androidx.room.*
import com.bukuwarung.database.entity.AppEntity
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity

@Keep
@Entity(tableName = "product_category_cross_ref", primaryKeys = ["product_id", "category_id"])
data class ProductCategoryCrossRef(
    @ColumnInfo(name = "product_id")
    var productId: String,

    @ColumnInfo(name = "category_id")
    var categoryId: String,

    @ColumnInfo(name = "book_id")
    var bookId: String,

    @ColumnInfo(name = "deleted")
    var deleted: Int
) : AppEntity(){

    // empty constructor for firebase object mapping
    constructor() : this("", "", "", 0)

    fun id(): String {
        return productId + categoryId
    }
}

data class ProductWithCategories(
    @Embedded val product: ProductEntity,

    @Relation(
        parentColumn = "product_id",
        entityColumn = "category_id",
        associateBy = Junction(ProductCategoryCrossRef::class)
    )
    val categories: List<ProductCategoryEntity>
)

data class CategoryWithProducts(
    @Embedded val category: ProductCategoryEntity,

    @Relation(
        parentColumn = "category_id",
        entityColumn = "product_id",
        associateBy = Junction(ProductCategoryCrossRef::class)
    )
    val products: List<ProductEntity>
)