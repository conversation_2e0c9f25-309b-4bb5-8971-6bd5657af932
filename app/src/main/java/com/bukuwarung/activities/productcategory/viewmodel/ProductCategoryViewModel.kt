package com.bukuwarung.activities.productcategory.viewmodel

import androidx.lifecycle.*
import com.bukuwarung.activities.BaseViewModelEventState
import com.bukuwarung.activities.ViewModelEvent
import com.bukuwarung.activities.ViewModelState
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.utils.recordException
import kotlinx.coroutines.launch
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class ProductCategoryViewModel @Inject constructor(
    private val categoryUseCase: ProductCategoryUseCase,
) : BaseViewModelEventState<ProductCategoryViewModel.State, ProductCategoryViewModel.Event>() {
    private val allCategories = categoryUseCase.getObservableCategories()
    private val categoryCount = MediatorLiveData<Int>()
    val categoryCountObservable : LiveData<Int> = categoryCount

    private val categoryQuery = MutableLiveData<String>("")
    val categories = categoryQuery.switchMap { query ->
        if (query.isNullOrBlank()) {
            allCategories
        } else {
            allCategories.map { categories ->
                categories.filter { it.name.contains(query, true) }
            }
        }
    }

    init {
        categoryCount.addSource(allCategories){
            categoryCount.value = it.size
        }
    }

    private val selectedCategories = mutableListOf<ProductCategoryEntity>()
    val selectedCategoryList: List<ProductCategoryEntity> = selectedCategories

    private var categorySuggestions = listOf<String>()

    sealed class Event : ViewModelEvent {
        data class CreateNewCategory(val categoryName: String) : Event()
        data class UpdateCategory(val categoryId: String, val newCategoryName: String) : Event()
        data class DeleteCategory(val categoryId: String) : Event()
        data class AssociateProductCategories(val productId: String, val categoryIds: List<String>? = null) : Event()
        data class AssociateCategoryToProducts(val categoryId: String, val productIds: List<String>) : Event()
        data class UpdateSelection(val categoryId: String, val isSelected: Boolean) : Event()
        data class UpdateSelections(val categoryIds: List<String>) : Event()
        data class GetCategoriesOfProduct(val productId: String) : Event()
        data class RequestProductsByCategoryId(val categoryId: String) : Event()
        data class RequestCategorySuggestion(val query: String) : Event()
        data class FilterCategory(val categoryName: String) : Event()
    }

    sealed class State : ViewModelState {
        object OnSelectionCached : State()
        data class ProductsByCategoryId(val products: List<ProductEntity>) : State()
        data class SetCategorySuggestion(val suggestions: List<String>) : State()
        object OnCategoryNameAlreadyExist : State()
    }

    override fun onEventReceipt(event: Event) {
        when (event) {
            is Event.CreateNewCategory -> createNewCategory(event.categoryName)
            is Event.DeleteCategory -> deleteCategory(event.categoryId)
            is Event.UpdateCategory -> updateCategory(event.categoryId, event.newCategoryName)
            is Event.AssociateProductCategories -> associateProductToCategories(event.productId, event.categoryIds)
            is Event.AssociateCategoryToProducts -> associateCategoryToProducts(event.categoryId, event.productIds)
            is Event.UpdateSelection -> viewModelScope.launch {
                updateSelection(event.categoryId, event.isSelected)
                setState(State.OnSelectionCached)
            }
            is Event.UpdateSelections -> viewModelScope.launch {
                selectedCategories.clear()
                event.categoryIds.forEach { updateSelection(it, true) }
                setState(State.OnSelectionCached)
            }
            is Event.GetCategoriesOfProduct -> getCategoriesOfProduct(event.productId)
            is Event.RequestProductsByCategoryId -> getProductsByCategoryId(event.categoryId)
            is Event.RequestCategorySuggestion -> getCategorySuggestion(event.query)
            is Event.FilterCategory -> categoryQuery.value = event.categoryName
        }
    }

    private fun createNewCategory(categoryName: String) = viewModelScope.launch {
        try {
            categoryUseCase.createNewCategory(categoryName)
        } catch (ex: Exception) {
            setState(State.OnCategoryNameAlreadyExist)
            ex.recordException()
        }
    }

    private fun deleteCategory(categoryId: String) = viewModelScope.launch {
        updateSelection(categoryId, false)
        categoryUseCase.deleteCategory(categoryId)
    }

    private fun updateCategory(categoryId: String, newCategoryName: String) = viewModelScope.launch {
        try {
            categoryUseCase.updateCategory(categoryId, newCategoryName)
        } catch (ex: Exception) {
            setState(State.OnCategoryNameAlreadyExist)
            ex.recordException()
        }
    }

    private fun associateProductToCategories(productId: String, categoryIds: List<String>?) = viewModelScope.launch {
        categoryUseCase.createProductCategoriesAssociation(productId, categoryIds ?: selectedCategoryList.map { it.id })
    }

    private fun associateCategoryToProducts(categoryId: String, productIds: List<String>) = viewModelScope.launch {
        categoryUseCase.createCategoryProductsAssociation(categoryId, productIds)
    }

    private suspend fun updateSelection(categoryId: String, selected: Boolean) {
        val category = categoryUseCase.getCategoryById(categoryId) ?: return

        if (selected) {
            if (selectedCategories.contains(category)) return
            selectedCategories.add(category)
        } else {
            selectedCategories.removeIf { it.id == categoryId }
        }
    }

    private fun getCategoriesOfProduct(productId: String) = viewModelScope.launch {
        val currentProductCategories = categoryUseCase.getProductWithCategories(productId)?.categories
            ?.takeIf { it.isNotEmpty() } ?: return@launch

        onEventReceipt(Event.UpdateSelections(currentProductCategories.map { it.id }))
    }

    private fun getProductsByCategoryId(categoryId: String) = viewModelScope.launch {
        val categoryWithProducts = categoryUseCase.getCategoryWithProducts(categoryId)
        setState(State.ProductsByCategoryId(categoryWithProducts?.products ?: emptyList()))
    }

    private fun getCategorySuggestion(query: String) = viewModelScope.launch {
        if (query.isBlank()) return@launch

        val suggestions = categoryUseCase.getSuggestion(query)
        categorySuggestions = categorySuggestions.union(suggestions).toList()

        setState(State.SetCategorySuggestion(categorySuggestions))
    }
}