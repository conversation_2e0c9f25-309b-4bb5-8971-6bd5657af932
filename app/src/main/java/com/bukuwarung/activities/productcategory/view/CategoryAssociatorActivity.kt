package com.bukuwarung.activities.productcategory.view

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.productcategory.adapter.ProductSelectionAdapter
import com.bukuwarung.activities.productcategory.viewmodel.ProductCategoryViewModel
import com.bukuwarung.activities.productcategory.viewmodel.SimpleProductSelectionViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.databinding.ActivityCategoryAssociatorBinding
import com.bukuwarung.utils.setupForSearch
import com.bukuwarung.utils.subscribeSingleLiveEvent
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CategoryAssociatorActivity : BaseActivity() {
    private val categoryViewModel: ProductCategoryViewModel by viewModels()
    private val productViewModel: SimpleProductSelectionViewModel by viewModels()

    private val binding by lazy {
        ActivityCategoryAssociatorBinding.inflate(layoutInflater).also {
            setContentView(it.root)
        }
    }

    private var category: ProductCategoryEntity? = null

    private val categoryId by lazy {
        intent.getStringExtra(CATEGORY_ID) ?: ""
    }

    private val productAdapter by lazy {
        ProductSelectionAdapter { productId, isChecked ->
            productViewModel.onEventReceipt(SimpleProductSelectionViewModel.Event.OnSelectionUpdated(productId, isChecked))
        }
    }

    override fun setViewBinding() {}

    override fun setupView() {
        binding.apply {
            setUpToolbarWithHomeUp(tb)
            rvProduct.apply {
                adapter = productAdapter
                layoutManager = LinearLayoutManager(context)
                itemAnimator = DefaultItemAnimator()
                addItemDecoration(DividerItemDecoration(context, RecyclerView.VERTICAL))
            }

            btnConfirm.setOnClickListener {
                trackConfirmManageProductClick()
                categoryViewModel.onEventReceipt(ProductCategoryViewModel.Event.AssociateCategoryToProducts(categoryId, productViewModel.getSelectedProductIds()))
                finish()
            }

            etSearch.setupForSearch(lifecycleScope, delay = 0){ query ->
                productViewModel.onEventReceipt(SimpleProductSelectionViewModel.Event.DoSearch(query))
            }
        }

        categoryViewModel.onEventReceipt(ProductCategoryViewModel.Event.RequestProductsByCategoryId(categoryId))
    }

    private fun trackConfirmManageProductClick() {
        trackEvent(AnalyticsConst.SAVE_MANAGE_CATEGORY_CHANGES){
            addEntryPointProperty(AnalyticsConst.INVENTORY_TAB)
            addProperty(AnalyticsConst.PRODUCT_CHECKED_COUNT to productViewModel.getSelectedProductIds().size)
            addProperty(AnalyticsConst.PRODUCT_CATEGORY_NAME to category?.name)
        }
    }

    override fun subscribeState() {
        categoryViewModel.categories.observe(this) { categories ->
            category = categories.find { it.id == categoryId }

            with(category?.name ?: "") {
                binding.tb.title = this
                productAdapter.setCategoryName(this)
            }
        }

        productViewModel.productsObservable.observe(this) { products ->
            productAdapter.setData(products.sortedBy { it.name })
        }

        productViewModel.selectedProductIdsList.observe(this) { selectedProductIds ->
            productAdapter.setSelectedIds(selectedProductIds)
        }

        subscribeSingleLiveEvent(categoryViewModel.state){ state ->
            when(state){
                is ProductCategoryViewModel.State.ProductsByCategoryId -> {
                    productViewModel.onEventReceipt(SimpleProductSelectionViewModel.Event.SelectMultipleProducts(state.products.map { it.productId }))
                }
                else -> {}
            }
        }
    }

    companion object {
        private const val CATEGORY_ID = "category_id"

        fun open(context: Context, categoryId: String) {
            Intent(context, CategoryAssociatorActivity::class.java).apply {
                putExtra(CATEGORY_ID, categoryId)
            }.also {
                context.startActivity(it)
            }
        }
    }
}