package com.bukuwarung.activities.kycupgrade

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.view.HomepageNudgeActivity
import com.bukuwarung.activities.homepage.view.NoInternetAvailableDialog
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityKycUpgradeBinding
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrEmpty

class KycUpgradeActivity : BaseActivity() {

    private lateinit var binding: ActivityKycUpgradeBinding

    companion object {
        fun createIntent(origin: Context?, from: String): Intent {
            val intent = Intent(origin, KycUpgradeActivity::class.java)
            intent.putExtra("from", from)
            return intent
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()

    }

    override fun setViewBinding() {
        binding = ActivityKycUpgradeBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        binding.backBtn.setOnClickListener { onBackPressed() }

        binding.btnUpgrade.setOnClickListener {
            startActivity(
                WebviewActivity.createIntent(
                    this, RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl,
                    getString(R.string.qris_ktp_rejected)
                )
            )
            val propBuilder = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT2, "benefit_page")
                put("currently_kyc_or_kyb_step", "KYC")
                put("kyc_tier", PaymentPrefManager.getInstance()
                    .getKycTier().toString())
            }
            AppAnalytics.trackEvent("kyc_verification_start", propBuilder)
        }
        binding.tvLearnMore.setOnClickListener {
            redirectToLearnAboutAccountLevels()
            val propBuilder = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT2, "benefit_page")
                put("link", RemoteConfigUtils.getLearnAboutAccountLevelsLink())
                put("kyc_tier", PaymentPrefManager.getInstance()
                    .getKycTier().toString())
            }
            AppAnalytics.trackEvent("kyc:learn_kyc_clicked", propBuilder)
        }
    }

    private fun redirectToLearnAboutAccountLevels() {
        val deeplink = RemoteConfigUtils.getLearnAboutAccountLevelsLink()
        if (deeplink.isNotNullOrEmpty()) {
            if (Utility.hasInternet()) {
                deeplink?.let {
                    startActivity(
                        WebviewActivity.createIntent(
                            this, deeplink,
                            "BukuWarung"
                        )
                    )
                }
            }
        }
    }

    override fun subscribeState() {

    }

}