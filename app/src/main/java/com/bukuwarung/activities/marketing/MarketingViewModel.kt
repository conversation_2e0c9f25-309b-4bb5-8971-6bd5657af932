package com.bukuwarung.activities.marketing

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MarketingViewModel @Inject constructor(
        private val gsonHandler: GsonHandler,
        private val sessionManager: SessionManager,
        private val businessRepository: BusinessRepository
) : BaseViewModel() {
    private val _socialMediaStories = MutableLiveData<List<MarketingStoryTemplate>>()
    private val socialMediaMarketing = RemoteConfigUtils.SocialMediaMarketing
    val socialMediaStories: LiveData<List<MarketingStoryTemplate>> = _socialMediaStories
    private var marketingType = MarketingType.STATUS

    fun init(marketingType: MarketingType) {
        this.marketingType = marketingType
        getStatusTemplate()
    }

    private fun getStatusTemplate() = viewModelScope.launch {
        val marketingTemplate = getMarketingStatus()
        _socialMediaStories.value = marketingTemplate.list
    }

    private fun getMarketingStatus(): MarketingTemplate {
        return gsonHandler.loadJson(
                clazz = MarketingTemplate::class.java,
                json = when (marketingType) {
                    MarketingType.STATUS -> socialMediaMarketing.getStories()
                    MarketingType.POSTER -> socialMediaMarketing.getPosters()
                }
        )
    }

    fun getCurrentBook() = businessRepository.getBusinessByIdSync(sessionManager.businessId)
}