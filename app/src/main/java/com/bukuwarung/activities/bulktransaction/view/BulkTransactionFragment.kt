package com.bukuwarung.activities.bulktransaction.view

import android.app.DatePickerDialog
import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.bulktransaction.adapter.BulkTransactionAdapter
import com.bukuwarung.activities.bulktransaction.data.BulkTransactionData
import com.bukuwarung.activities.bulktransaction.helpers.IOnBackPressed
import com.bukuwarung.activities.bulktransaction.viewmodel.BulkTransactionViewModel
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.bulk.CashTransactionType
import com.bukuwarung.databinding.FragmentBulkTransactionBinding
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import com.bukuwarung.utils.subscribeSingleLiveEvent
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar

@AndroidEntryPoint
class BulkTransactionFragment : BaseFragment(), BulkTransactionAdapter.updateAggregate,
        BulkTransactionAdapter.operateOnDatabase, IOnBackPressed {

    private lateinit var binding: FragmentBulkTransactionBinding
    private lateinit var adapter: BulkTransactionAdapter
    lateinit var bulkData: MutableList<BulkTransactionData>

    private val bulkTransactionViewModel: BulkTransactionViewModel by viewModels()
    private var lastButtonSaveClicked: Long = 0
    private var saveCount = 0
    private var type = 0
    private var date = ""


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = FragmentBulkTransactionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        val bulkTransactionData = arguments?.get(BULK_DATA) as BulkTransactionData

        bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.SetInitialAggregateValues(bulkTransactionData))

        bulkData = mutableListOf()

        type = bulkTransactionData.type

        bulkData.clear()
        bulkData.add(bulkTransactionData)



        adapter = BulkTransactionAdapter(requireContext(), bulkData, this, this) { hideSoftKeyboard ->
            if (hideSoftKeyboard) {
                val imm: InputMethodManager = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            }
        }.apply {
            setKeyboard(binding.customKeyboard)
        }

        val layoutManager = LinearLayoutManager(activity)
        binding.rvBulkTransaksi.layoutManager = layoutManager
        binding.rvBulkTransaksi.adapter = adapter

        binding.rvBulkTransaksi.setItemViewCacheSize(10000);


        isKeyboardVisible = true
        checkForKeyboard()

        hideEmptyState()

        binding.lyEmptyState.tvAdditionalText.text = HtmlCompat.fromHtml(activity?.resources?.getString(R.string.empty_bulk_description)!!, HtmlCompat.FROM_HTML_MODE_COMPACT)

        binding.btnTambahTransaksi.setOnClickListener {
            adapter.addNewTransaction(BulkTransactionData(type, date, 0.0, 0.0, 0.0, ""))
            binding.btnSaveBulkTransaksi.text = "Simpan Semua (" + adapter.itemCount + ")"
            binding.rvBulkTransaksi.smoothScrollToPosition(adapter.itemCount);
            isKeyboardVisible = true
            checkForKeyboard()
            saveCount++
            hideEmptyState()
        }

        binding.lyAggregate.tvDateVal.setOnClickListener {
            showTransactionDatePicker(bulkTransactionData.date)
        }

        binding.btnSaveBulkTransaksi.setOnClickListener {
            if (bulkData.size < 1) {
                activity?.finish()
            } else {
                onSaveButtonClicked(bulkData, -2)
                MainActivity.startActivitySingleTopToTab(activity, TabName.TRANSACTION_HOME)
            }
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(bulkTransactionViewModel.state) {
            when (it) {
                is BulkTransactionViewModel.State.SetValuesFromTranaksi -> {
                    binding.lyAggregate.apply {
                        tvPenjualanVal.text = Utility.formatAmount(it.income).toString()
                        tvPengeluaranVal.text = Utility.formatAmount(it.expense).toString()
                        tvDateVal.text = Utility.getReadableDateString(it.date)
                        date = it.date
                    }
                }

                is BulkTransactionViewModel.State.SetAggregateValues -> {
                    binding.lyAggregate.apply {
                        tvPenjualanVal.text = Utility.formatAmount(it.income).toString()
                        tvPengeluaranVal.text = Utility.formatAmount(it.expense).toString()
                    }
                }

                is BulkTransactionViewModel.State.ShowCalendarDate -> {
                    binding.lyAggregate.tvDateVal.text = Utility.getReadableDateString(it.date)
                    date = it.date
                    updateValues(bulkData, -1, it.date)
                }

                is BulkTransactionViewModel.State.SetTransactionId -> {
                    bulkData[it.position].transactionId = it.transactionId
                    if (it.sendAnalytics) {
                        sendAggregateValuesToAnalytics()
                        for (element in bulkData) {
                            sendIndividualValuesToAnalytics(element)
                        }
                    }
                }
            }
        }
    }

    companion object {
        private const val BULK_DATA = "bulk_data"
        var isKeyboardVisible = false

        fun instance(bulkTransactionData: BulkTransactionData): BulkTransactionFragment {
            val fragment = BulkTransactionFragment()
            val bundle = Bundle()
            bundle.putSerializable(BULK_DATA, bulkTransactionData)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun updateAggregates(bulkData: MutableList<BulkTransactionData>) {
        this.bulkData = bulkData
        bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.SetAggregates(bulkData))

        var textToDisplay = "Simpan Semua"
        if (adapter.itemCount > 0) {
            textToDisplay += "(" + adapter.itemCount + ")"
        }
        binding.btnSaveBulkTransaksi.text = textToDisplay
    }

    private fun showTransactionDatePicker(transactionDate: String?) {
        val instance = Calendar.getInstance()
        var year = instance[Calendar.YEAR]
        var month = instance[Calendar.MONTH]
        var date = instance[Calendar.DATE]
        try {
            if (!transactionDate.isNullOrEmpty()) {
                val currentDate = DateTimeUtils.convertToDateYYYYMMDD(transactionDate)
                instance.time = currentDate
                year = instance[Calendar.YEAR]
                month = instance[Calendar.MONTH]
                date = instance[Calendar.DATE]
            }
        } catch (ex: Exception) {
            ex.recordException()
        }

        isKeyboardVisible = false
        checkForKeyboard()

        val datePickerDialog = DatePickerDialog(requireContext(), android.R.style.Theme_DeviceDefault_Light_Dialog,
                { _, selectedYear, selectedMonth, selectedDate ->
                    val cal = Calendar.getInstance()
                    cal[selectedYear, selectedMonth] = selectedDate
                    val storableDateString = Utility.getStorableDateString(cal.time)
                    bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.PickCalendarDate(storableDateString))

                }, year, month, date)
        datePickerDialog.setTitle(com.bukuwarung.R.string.date)
        datePickerDialog.show()
    }

    fun onSaveButtonClicked(bulkTransactionData: List<BulkTransactionData>, pos: Int) {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600)
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()


        if (pos == -2) {
            for (i in bulkTransactionData.indices) {
                if (bulkTransactionData[i].transactionId == null) {
                    bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.AddNewTransaction(bulkTransactionData, i, true))
                }
            }
        } else {
            bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.AddNewTransaction(bulkTransactionData, pos, false))
        }

        InputUtils.hideKeyBoardWithCheck(activity)
    }

    private fun onDeleteTransaction(transactionId: String, position: Int) {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600)
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()
        InputUtils.hideKeyBoardWithCheck(activity)
        bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.DeleteTransaction(transactionId))
        chekForEmptyState()
    }

    private fun chekForEmptyState() {
        if (bulkData.size == 0) {
            showEmptyState()
        } else {
            hideEmptyState()
        }
    }

    private fun showEmptyState() {
        binding.rvBulkTransaksi.visibility = View.GONE
        binding.lyEmptyState.root.visibility = View.VISIBLE
    }

    private fun hideEmptyState() {
        binding.rvBulkTransaksi.visibility = View.VISIBLE
        binding.lyEmptyState.root.visibility = View.GONE
    }

    override fun addValues(bulkData: MutableList<BulkTransactionData>, position: Int) {
        BulkTransactionActivity.isValueChanged = true
        this.bulkData = bulkData
        onSaveButtonClicked(bulkData, position - 1)
    }

    override fun deleteValues(bulkData: MutableList<BulkTransactionData>, position: Int) {
        BulkTransactionActivity.isValueChanged = true
        val transactionId: String? = bulkData[position].transactionId
        transactionId?.let {
            onDeleteTransaction(transactionId, position)
        }
        chekForEmptyState()
    }

    override fun updateValues(bulkData: MutableList<BulkTransactionData>, position: Int, date: String?) {
        BulkTransactionActivity.isValueChanged = true
        if (!date.isNullOrEmpty()) {
            for (i in 0 until bulkData.size) {
                bulkData[i].date = date
            }
        }

        this.bulkData = bulkData

        adapter.refreshTransactionData(bulkData, position)

        if (position == -1) {
            for (i in 0 until bulkData.size) {
                updateValue(bulkData, i)
            }
        } else {
            updateValue(bulkData, position)
        }
    }

    fun updateValue(bulkData: MutableList<BulkTransactionData>, position: Int) {
        saveCount++
        bulkTransactionViewModel.onEventReceived(BulkTransactionViewModel.Event.UpdateTransactions(bulkData, position))
    }

    fun checkForKeyboard() {


    }

    override fun onBackPressed(): MutableList<BulkTransactionData> {
        if (binding.customKeyboard.isVisible) {
            binding.customKeyboard.visibility = View.GONE
        }
        return bulkData
    }

    private fun sendAggregateValuesToAnalytics() {
        var sales_count = 0
        var expense_count = 0
        var total_sales = 0.0
        var total_expense = 0.0

        for (i in bulkData.indices) {
            sales_count += if (bulkData[i].credit > 0) 1 else 0
            expense_count += if (bulkData[i].sales > 0) 1 else 0
            total_sales = total_sales.plus(if (bulkData[i].credit > 0) {
                (bulkData[i].credit - bulkData[i].harga)
            } else 0.0)
            total_expense = total_expense.plus(if (bulkData[i].sales > 0) bulkData[i].sales else 0.0)
        }



    }

    private fun sendIndividualValuesToAnalytics(data: BulkTransactionData) {
        if (data.sales <= 0 && data.credit <= 0 && data.harga <= 0) {
            return
        }
        val salesType = if (data.sales > 0) CashTransactionType.DEBIT else CashTransactionType.CREDIT
    }

}