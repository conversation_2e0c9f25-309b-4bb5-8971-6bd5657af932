package com.bukuwarung.activities.bulktransaction.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.bulktransaction.data.BulkTransactionData
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.bulk.CashCategoryType
import com.bukuwarung.bulk.CashTransactionStatus
import com.bukuwarung.bulk.CashTransactionType
import com.bukuwarung.bulk.model.CashCategory
import com.bukuwarung.bulk.model.UserCashTransaction
import com.bukuwarung.bulk.usecase.DeleteCashTransaction
import com.bukuwarung.bulk.usecase.SaveBulkTransaction
import com.bukuwarung.bulk.usecase.UpdateCashTransaction
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.wrapper.EventWrapper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class BulkTransactionViewModel @Inject constructor(private var saveBulkTransaction: SaveBulkTransaction,
                                private var updateCashTransaction: UpdateCashTransaction,
                                private var deleteCashTransaction: DeleteCashTransaction): BaseViewModel() {

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    sealed class State {
        data class SetValuesFromTranaksi(val income: Double, val expense: Double, val date: String) : State()
        data class SetAggregateValues(val income: Double, val expense: Double) : State()
        data class ShowCalendarDate(val date: String) : State()
        data class SetTransactionId(val transactionId: String?, val position: Int, val sendAnalytics: Boolean): State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state

    var transactionEntity: CashTransactionEntity? = null

    sealed class Event {
        data class SetInitialAggregateValues(val data: BulkTransactionData): Event()
        data class SetAggregates(val data: List<BulkTransactionData>): Event()
        data class PickCalendarDate(val date:String): Event()
        data class AddNewTransaction(val data: List<BulkTransactionData>, val position: Int, val sendAnalytics: Boolean): Event()
        data class UpdateTransactions(val data: List<BulkTransactionData>, val position: Int): Event()
        data class DeleteTransaction(val transactionId: String) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.SetInitialAggregateValues -> setInitialValues(event.data)
            is Event.SetAggregates -> setAggregates(event.data)
            is Event.PickCalendarDate -> setState(State.ShowCalendarDate(event.date))
            is Event.AddNewTransaction -> addNewTransaction(event.data, event.position, event.sendAnalytics)
            is Event.UpdateTransactions -> updateTransactions(event.data, event.position)
            is Event.DeleteTransaction -> deleteSelectedTransaction(event.transactionId)
        }
    }

    private fun setInitialValues(data: BulkTransactionData) {
        var credit = 0.0
        var debit = 0.0
        if (data.type == 0) {
            credit += data.credit
        } else {
            debit += data.sales
        }

        debit += data.harga
        setState(State.SetValuesFromTranaksi(credit, debit, data.date))
    }

    private fun setAggregates(data: List<BulkTransactionData>) {
        var sales = 0.0
        var expenses = 0.0

        for (i in 0 until data.size) {
            sales += data[i].credit
            expenses += data[i].harga
            expenses += data[i].sales
        }

        setState(State.SetAggregateValues(sales, expenses))
    }

    private fun updateTransactions(bulkData: List<BulkTransactionData>, position: Int) {
        Log.i("BULK", "update transaction save pos: $position")
        val data = bulkData[position]
        if (data.transactionId == null || (data.credit <= 0 && data.harga <= 0 && data.sales <= 0))
            return
        val type = if (data.sales > 0) CashCategoryType.CASH_OUT else CashCategoryType.CASH_IN
        val transactionType = if (data.sales > 0) CashTransactionType.DEBIT else CashTransactionType.CREDIT

        val amount = if (data.sales > 0) -data.sales else data.credit

        viewModelScope.launch {
            updateTransaction(UserCashTransaction(
                    "", CashCategory(null, amount, type), Contact("", "", "", ""),
                    CashTransactionStatus.FULLY_UN_PAID, data.date, amount, data.harga, data.notes!!, "", transactionType,
                    data.transactionId, SessionManager.getInstance().userId))
        }
    }

    private fun addNewTransaction(bulkTransactionData: List<BulkTransactionData>, pos: Int, sendAnalytics: Boolean) {
        Log.i("BULK", "add new transaction save pos: $pos")
        val data = bulkTransactionData[pos]
        if ((data.credit <= 0 && data.harga <= 0 && data.sales <= 0)) {
            return
        }

        val type = if (data.sales > 0) CashCategoryType.CASH_OUT else CashCategoryType.CASH_IN
        val transactionType = if (data.sales > 0) CashTransactionType.DEBIT else CashTransactionType.CREDIT

        val amount = if (data.sales > 0) -data.sales else data.credit

        viewModelScope.launch {
            setState(State.SetTransactionId(saveTransaction(UserCashTransaction(
                    null, CashCategory(null, amount, type), Contact("", "", "", ""),
                    CashTransactionStatus.FULLY_UN_PAID, data.date, amount, data.harga, data.notes!!, "", transactionType,
                    "", SessionManager.getInstance().userId)), pos, sendAnalytics))
        }
    }

    private fun deleteSelectedTransaction(transactionId: String) {
       viewModelScope.launch {
            deleteTransaction(transactionId)
        }
    }

    private suspend fun saveTransaction(userCashTransaction: UserCashTransaction): String? {

        Utility.trackTransactionCount()
        return saveBulkTransaction.invoke(userCashTransaction)
    }

    private suspend fun deleteTransaction(transactionId: String) {
        deleteCashTransaction.invoke(transactionId)
    }

    private suspend fun updateTransaction(userCashTransaction: UserCashTransaction) {
        updateCashTransaction.invoke(userCashTransaction)
    }

}