package com.bukuwarung.activities.livelinesscheck

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.annotation.IntDef
import androidx.appcompat.content.res.AppCompatResources
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.AppConst.LIVELINESS_API_ERROR
import com.bukuwarung.constants.AppConst.LIVELINESS_DIR
import com.bukuwarung.constants.AppConst.LIVELINESS_LIMIT_EXHAUST
import com.bukuwarung.constants.AppConst.LIVELINESS_SUCCESS
import com.bukuwarung.constants.AppConst.LIVELINESS_VIDA_FAILED
import com.bukuwarung.databinding.ActivityCameraLivelinessBinding
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.invisibleView
import com.bukuwarung.utils.showToast
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import java.util.Date
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


@AndroidEntryPoint
class CameraLivelinessActivity : BaseActivity(), PaymentDownBottomSheet.PaymentDownBsListener {
    private var imageCapture: ImageCapture? = null
    private lateinit var outputDirectory: File
    private lateinit var cameraExecutor: ExecutorService
    private var lensFacing = CameraSelector.LENS_FACING_FRONT
    private var retake = false
    private lateinit var cameraProvider: ProcessCameraProvider
    private var camera: Camera? = null
    private var flashType = ImageCapture.FLASH_MODE_OFF
    val productId: String? by lazy {
        intent.getStringExtra(LivelinessLandingActivity.PRODUCT_ID)
    }


    private val viewModel: CameraLivelinessViewModel by viewModels()
    lateinit var binding: ActivityCameraLivelinessBinding


    override fun setViewBinding() {
        binding = ActivityCameraLivelinessBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {

        with(binding) {
            includeToolbar.tvTitle.setText(R.string.title_liveliness)

            includeToolbar.btnBack.setOnClickListener {
                onBackPressed()
            }
            includeToolbar.tvHelp.setOnClickListener {
                startActivity(
                    WebviewActivity.createIntent(
                        this@CameraLivelinessActivity,
                        RemoteConfigUtils.getPaymentConfigs().supportUrls.liveness,
                        getString(R.string.help_bantuan)
                    )
                )
            }

            btnCameraCapture.setOnClickListener {
                when {
                    isRetry() -> {
                        resetToCaptureImage()
                    }
                    isSuccess() -> {
                        val i = Intent().apply {
                            putExtra(RESULT, true)
                        }
                        setResult(Activity.RESULT_OK, i)
                        finish()
                    }
                    isFailed() -> {
                        val i = Intent().apply {
                            putExtra(RESULT, false)
                        }
                        setResult(Activity.RESULT_OK, i)
                        finish()
                    }
                    else -> {
                        goToTakePicture()
                    }
                }
            }
            // Request camera permissions
            if (allPermissionsGranted()) {
                startCamera()
            } else {
                ActivityCompat.requestPermissions(this@CameraLivelinessActivity, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
            }

        }

        outputDirectory = getOutputDirectory()

        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    private fun goToTakePicture() {
        if(Utility.hasInternet()) {
            takePhoto()
        }else{
            showNoInternetBottomSheet(false)
        }
    }

    override fun subscribeState() {
        viewModel.observeLivelinessDetail.observe(this) {
            when (it) {
                is CameraLivelinessViewModel.DetailEvent.ApiSuccess -> {
                    if (it.response.status == true) {
                        if (it.response.data?.data?.code == LIVELINESS_SUCCESS) {
                            showSuccessView()
                        } else {
                            showErrorView(ERROR_SELFIES)
                        }
                    } else {
                        if (it.response.code == LIVELINESS_LIMIT_EXHAUST) {
                            showErrorView(ERROR_SELFIES_RETURN)
                        } else if (it.response.code == LIVELINESS_VIDA_FAILED ||  it.response.code == LIVELINESS_API_ERROR ) {
                            showErrorView(ERROR_SELFIES)
                        } else {
                            showErrorView(ERROR_SELFIES)
                        }
                    }
                }
                is CameraLivelinessViewModel.DetailEvent.ApiError -> {
                    showErrorView(ERROR_SELFIES)
                }
            }
        }
    }

    private fun isSuccess(): Boolean {
        return binding.btnCameraCapture.text == getString(R.string.send)
    }

    private fun isRetry(): Boolean {
        return binding.btnCameraCapture.text == getString(R.string.reload)
    }

    private fun isFailed(): Boolean {
        return binding.btnCameraCapture.text == getString(R.string.txt_return)
    }


    private fun takePhoto() {
        showImageInProgress()
        // Get a stable reference of the modifiable image capture use case
        val imageCapture = imageCapture ?: return
        val photoFile = File(outputDirectory, "${LIVELINESS_DIR}_${Date().time}.jpg")
        // Create output options object which contains file + metadata
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // Set up image capture listener, which is triggered after photo has
        // been taken
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exc: ImageCaptureException) {
                    Log.e(TAG, "Photo capture failed: ${exc.message}", exc)
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    Glide.with(this@CameraLivelinessActivity).load(photoFile).centerCrop().into(binding.ivPreview)
                    binding.ivPreview.showView()
                    startFileUploader(photoFile);
                }
            })
    }

    private fun showImageInProgress() {
        with(binding) {
            tvDescription2.setText(R.string.image_processing)
            tvDescription2.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.colorPrimary))
            progressBar.showView()
            btnCameraCapture.invisibleView()
        }
    }

    private fun startFileUploader(file: File) {
        val photoFileCompress = File(outputDirectory, "${LIVELINESS_DIR}_${Date().time}_compress.jpg")
        viewModel.uploadImage(productId?: EMPTY_STRING, file, photoFileCompress)

    }

    private fun resetToCaptureImage() {
        retake = false
        with(binding) {
            ivPreview.hideView()
            tvDescriptionTitle.setText(R.string.posisikan_wajah_di_area_bawah_ini)
            tvDescriptionTitle.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.black_80))
            tvDescription.setText(R.string.description_liveliness)
            tvDescription2.setText(R.string.description2_liveliness)
            btnCameraCapture.setText(R.string.take_pic)
            cvCameraWrapper.foreground =
                AppCompatResources.getDrawable(this@CameraLivelinessActivity, R.drawable.ic_circle_capture)
            ivSuccessIcon.hideView()
            tvDescription2.setText(R.string.description2_liveliness)
            tvDescription2.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.black_80))
        }
    }

    private fun showSuccessView() {
        with(binding) {
            progressBar.hideView()
            tvDescriptionTitle.setText(R.string.image_success_tick)
            tvDescriptionTitle.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.black_80))
            tvDescription.setText(R.string.image_success_description)
            cvCameraWrapper.foreground =
                AppCompatResources.getDrawable(this@CameraLivelinessActivity, R.drawable.ic_circle_capture_green)
            btnCameraCapture.setText(R.string.send)
            btnCameraCapture.showView()
            ivSuccessIcon.setImageResource(R.drawable.ic_check_circle_green_liveliness)
            ivSuccessIcon.showView()
            cvCardSecure.showView()
            tvDescription2.hideView()
        }
    }

    private fun showErrorView(@SELFIES_ERROR_STATE state:Int) {
        retake = true
        with(binding) {
            progressBar.visibility = View.INVISIBLE
            cvCardSecure.hideView()
            tvDescriptionTitle.setText(R.string.image_error)
            tvDescriptionTitle.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.red_80))
            tvDescription.setText(R.string.image_failed_description)
            tvDescription2.setTextColor(ContextCompat.getColor(this@CameraLivelinessActivity, R.color.red_80))
            cvCameraWrapper.foreground =
                AppCompatResources.getDrawable(this@CameraLivelinessActivity, R.drawable.ic_circle_capture_red)
            ivSuccessIcon.setImageResource(R.drawable.ic_error)
            ivSuccessIcon.showView()
            btnCameraCapture.setText(R.string.reload)
            btnCameraCapture.showView()

            when (state) {
                ERROR_SELFIES -> {
                    tvDescription2.setText(R.string.image_failed_description2)
                }
                ERROR_SELFIES_TIME_TAKING -> {
                    tvDescription2.setText(R.string.image_taking_time_description2)
                }
                ERROR_SELFIES_RETURN -> {
                    tvDescription2.setText(R.string.image_exhaust_description2)
                    btnCameraCapture.setText(R.string.txt_return)
                }
            }
        }

    }

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener(Runnable {
            // Used to bind the lifecycle of cameras to the lifecycle owner
            cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

            // Preview
            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(binding.previewCamera.surfaceProvider)
                }

            imageCapture = ImageCapture.Builder().setFlashMode(flashType).build()

            // Select back camera as a default

            try {
                // Unbind use cases before rebinding
                cameraProvider.unbindAll()
                // Bind use cases to camera
                camera = cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture)

            } catch (exc: Exception) {
                Log.e(TAG, "Use case binding failed", exc)
            }

        }, ContextCompat.getMainExecutor(this))


    }


    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    private fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(
                it, LIVELINESS_DIR
            ).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else filesDir
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
    }


    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (grantResults.isNotEmpty()
                && grantResults[0] == PackageManager.PERMISSION_GRANTED
            ) {
                startCamera()
            } else {
                showToast("Grant permission for camera from settings")
                finish()
            }
        }
    }

    companion object {
        private const val REQUEST_CODE_PERMISSIONS = 10
        const val RESULT = "RESULT"
        private const val ERROR_SELFIES = 1
        private const val ERROR_SELFIES_TIME_TAKING = 2
        private const val ERROR_SELFIES_RETURN = 3

        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
        fun createIntent(origin: Context?, productId: String?): Intent {
            val intent = Intent(origin, CameraLivelinessActivity::class.java)
            intent.putExtra(LivelinessLandingActivity.PRODUCT_ID, productId)
            return intent
        }
    }

    @IntDef(value = [ERROR_SELFIES, ERROR_SELFIES_TIME_TAKING, ERROR_SELFIES_RETURN])
    @Retention(AnnotationRetention.SOURCE)
    annotation class SELFIES_ERROR_STATE


    private fun showNoInternetBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, "PaymentDownBottomSheet")
    }

    override fun onButtonClicked() {
        goToTakePicture()
    }

}
