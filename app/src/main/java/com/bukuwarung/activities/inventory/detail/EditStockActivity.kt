package com.bukuwarung.activities.inventory.detail

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.SystemClock
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.R
import com.bukuwarung.activities.bulktransaction.adapter.BulkTransactionAdapter
import com.bukuwarung.activities.bulktransaction.view.BulkTransactionFragment
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.inventory.dialog.DeleteProductDialog
import com.bukuwarung.activities.productcategory.view.ProductCategoryActivity
import com.bukuwarung.activities.productcategory.viewmodel.ProductCategoryViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dao.InventoryDao
import com.bukuwarung.database.entity.InventoryOperationType
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.databinding.ActivityEditStockBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.inventory.dialog.CustomMeasurementUnitDialog
import com.bukuwarung.inventory.ui.StockUnitViewModel
import com.bukuwarung.inventory.ui.measurement.StockUnitBottomSheet
import com.bukuwarung.inventory.ui.product.AddProductViewModel
import com.bukuwarung.keyboard.CustomKeyboardView
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import dagger.hilt.android.AndroidEntryPoint

import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.abs


@AndroidEntryPoint
class EditStockActivity : BaseActivity(),
    StockUnitBottomSheet.StockUnitSelection,
    CustomMeasurementUnitDialog.AddMeasurementInterface {

    private val scope = MainScope()

    private lateinit var binding: ActivityEditStockBinding

    private val viewModel: AddProductViewModel by viewModels()
    private val stockUnitViewModel: StockUnitViewModel by viewModels()

    @Inject
    lateinit var inventoryDao: InventoryDao

    private val inventoryHistoryViewModel: InventoryHistoryDetailViewModel by viewModels()

    private val productCategoryViewModel: ProductCategoryViewModel by viewModels()

    private var addNewMeasureUnitDialog: CustomMeasurementUnitDialog? = null
    private var units = mutableListOf<MeasurementEntity>()

    private var oldStockActiveStatus = AppConst.INVENTORY_TRACKING_ENABLED
    private var newStockActiveStatus = oldStockActiveStatus
    private var oldStockPrice = ""
    private var newStockPrice = ""
    private var oldStockQty = ""
    private var newStockQty = ""
    private var oldStockMinimumQty = ""
    private var newStockMinimumQty = ""
    private var oldStockName = ""
    private var newStockName = ""
    private var oldStockMeasurementUnit = "Pcs"
    private var newStockMeasurementUnit = ""
    private var isFav = false

    private lateinit var productEntity: ProductEntity

    // used to differentiate between add product and edit product
    private var isEdit = false
    private var isTransactionalFlow = false

    private var lastButtonSaveClicked: Long = 0
    private var bookId = ""
    private var productId = ""
    private var isTransactionFlow = false
    private var isTransactionEditFlow = false
    private var isAddTransactionFlow = false
    private var isAddPosFlow = false
    private var isFromPlusIcon = false
    private var isInventoryEditFlow = false
    private var productName: String = ""
    private lateinit var oldStockValue: String

    private lateinit var keyboard: CustomKeyboardView

    /**
     * Remote configs
     */
    private var isCogsAvailable = RemoteConfigUtils.InventoryExperiments.isCogsAvailable()

    override fun setViewBinding() {
        binding = ActivityEditStockBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        if (intent.hasExtra(BOOK_ID)) {
            bookId = intent.extras?.getString(BOOK_ID) ?: ""
        }
        if (intent.hasExtra(PRODUCT_ID)) {
            productId = intent.extras?.getString(PRODUCT_ID) ?: ""
        }
        if (intent.hasExtra(TRANSACTION_FLOW)) {
            isTransactionFlow = intent.extras?.getBoolean(TRANSACTION_FLOW) ?: false
        }
        if (intent.hasExtra(TRANSACTION_EDIT_FLOW)) {
            isTransactionEditFlow = intent.extras?.getBoolean(TRANSACTION_EDIT_FLOW) ?: false
        }
        if (intent.hasExtra(TRANSACTION_ADD_FLOW)) {
            isAddTransactionFlow = intent.extras?.getBoolean(TRANSACTION_ADD_FLOW) ?: false
        }
        if (intent.hasExtra(POS_ADD_FLOW)) {
            isAddPosFlow = intent.extras?.getBoolean(POS_ADD_FLOW) ?: false
        }
        if (intent.hasExtra(PRODUCT_NAME)) {
            productName = intent.extras?.getString(PRODUCT_NAME) ?: ""
        }
        if (intent.hasExtra(INVENTORY_EDIT_FLOW)) {
            isInventoryEditFlow = intent.extras?.getBoolean(INVENTORY_EDIT_FLOW) ?: false
        }

        isFromPlusIcon = intent.getBooleanExtra(IS_FROM_PLUS_ICON, false)
        binding.layoutInputProductBuyingPrice.visibility = (isCogsAvailable).asVisibility()
        binding.tvProfit.visibility = (isCogsAvailable).asVisibility()

        isEdit = productId.isNotEmpty()

        viewModel.init(User.getBusinessId(), isTransactionFlow)
        stockUnitViewModel.init(User.getBusinessId(), "")
        productCategoryViewModel.onEventReceipt(
            ProductCategoryViewModel.Event.GetCategoriesOfProduct(
                productId
            )
        )

        // Initialize InventoryHistoryDetailViewModel with productId if editing
        if (isEdit && productId.isNotEmpty()) {
            inventoryHistoryViewModel.initData(productId)
        }

        if (isEdit) {
            inventoryHistoryViewModel.stateData.observe(this, Observer { state ->
                showBasedOnState(state)
            })
        }

        if (isEdit) {
            binding.title.text = this.getString(R.string.change_items)
            binding.btnSave.text = this.getString(R.string.confirm_label)
        }

        binding.etProductName.requestFocus()
        InputUtils.showKeyboard(binding.etProductName.context)

        binding.ivStockToggleTooltip.setOnClickListener {
            Utilities.showTooltip(
                this, it,
                this.getString(R.string.stock_inactive_tooltip),
                Gravity.BOTTOM
            )
        }

        binding.closeBtn.setOnClickListener {
            onBackPressed()
        }

        binding.etProductMeasurementUnit.setOnClickListener {
            StockUnitBottomSheet.instance("", viewModel.currentMeasurementId)
                .show(supportFragmentManager, "")
        }

        binding.etProductMeasurementUnit.doOnTextChanged { text, _, _, _ ->
            newStockMeasurementUnit = text.toString()
        }

        binding.etProductCategory.setOnClickListener {
            val selectedCategoryIds = productCategoryViewModel.selectedCategoryList.map { it.id }
            val entryPoint = if (isTransactionEditFlow  || isAddTransactionFlow){
                ""
            }else{
                ""
            }

            val source = when {
                isFromPlusIcon -> AnalyticsConst.CASHIER_MODE
                isAddPosFlow -> AnalyticsConst.CASHIER_MODE
                else -> AnalyticsConst.INVENTORY_TAB
            }

            ProductCategoryActivity.startWithResult(this, PRODUCT_CATEGORY_CODE, selectedCategoryIds, source)
        }

        if (!isEdit) {
            binding.btnSave.setOnClickListener {
                onAddProductButtonClicked()
            }
        }

        binding.etProductName.doOnTextChanged { text, _, _, _ ->
            text?.let { productName ->
                newStockName = productName.toString()
                binding.btnSave.isEnabled = productName.isNotEmpty()
            }
            viewModel.productNameChange(text.toString())
        }

//        initKeyboard()
        
//        binding.etProductPrice.setOnFocusChangeListener { view, b ->
//            if (b) {
//                changeKeyboardFocus(0)
//            } else {
//                hideKeyBoard()
//            }
//        }

        binding.etProductPrice.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.etProductPrice.removeTextChangedListener(this)
                s?.toString()?.let {
                    newStockPrice = it
                    if (it.isEmpty()) {
                        binding.etProductPrice.typeface = Typeface.DEFAULT
                    } else {
                        binding.etProductPrice.typeface = Typeface.DEFAULT_BOLD
                        val sellingPrice = Utility.extractAmountFromText(it)
                        val buyingPrice =
                            Utility.extractAmountFromText(binding.etProductBuyingPrice.text.toString())
                        val formattedPrice = Utility.priceToString(sellingPrice, false)
                        binding.etProductPrice.setText(formattedPrice)
                        binding.etProductPrice.setSelection(formattedPrice.length)

                        if (buyingPrice > sellingPrice) {
                            binding.tvProfit.setTextColor(resources.getColor(R.color.red_80))
                            binding.tvProfit.setText(R.string.make_sure_purchase_price_is_lower_than_selling_price)
                            binding.btnSave.isEnabled = false
                        } else {
                            binding.tvProfit.setTextColor(resources.getColor(R.color.green_100))
                            val profit = sellingPrice - buyingPrice
                            val profitResourceString = getString(R.string.stock_profit)
                            val profitString =
                                String.format(profitResourceString, Utility.formatCurrency(profit))
                            binding.tvProfit.text = profitString
                            binding.btnSave.isEnabled = true
                        }
                    }
                    viewModel.productPriceChange(it)
                }
                binding.etProductPrice.addTextChangedListener(this)
            }

            override fun afterTextChanged(s: Editable?) {}
        })

//        binding.etProductBuyingPrice.onFocusChangeListener =
//            View.OnFocusChangeListener { _, hasFocus ->
//                binding.rlBuyingPriceInfo.visibility = (hasFocus).asVisibility()
//                if (hasFocus) {
//                    changeKeyboardFocus(1)
//                } else {
//                    hideKeyBoard()
//                }
//            }

        binding.etProductBuyingPrice.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.etProductBuyingPrice.removeTextChangedListener(this)
                s?.toString()?.let {
                    newStockPrice = it
                    if (it.isEmpty()) {
                        binding.etProductBuyingPrice.typeface = Typeface.DEFAULT
                    } else {
                        binding.etProductBuyingPrice.typeface = Typeface.DEFAULT_BOLD
                        val price = Utility.extractAmountFromText(it)
                        val sellingPrice =
                            Utility.extractAmountFromText(binding.etProductPrice.text.toString())
                        if (price > sellingPrice) {
                            binding.tvProfit.setTextColor(resources.getColor(R.color.red_80))
                            binding.tvProfit.setText(R.string.make_sure_purchase_price_is_lower_than_selling_price)
                            binding.btnSave.isEnabled = false
                        } else {
                            binding.tvProfit.setTextColor(resources.getColor(R.color.green_100))
                            val profit = sellingPrice - price
                            val profitResourceString = getString(R.string.stock_profit)
                            val profitString =
                                String.format(profitResourceString, Utility.formatCurrency(profit))
                            binding.tvProfit.text = profitString
                            binding.btnSave.isEnabled = true
                        }
                        val formattedPrice = Utility.priceToString(price, false)
                        binding.etProductBuyingPrice.setText(formattedPrice)
                        binding.etProductBuyingPrice.setSelection(formattedPrice.length)
                    }
                    viewModel.productBuyingPriceChange(it)
                }
                binding.etProductBuyingPrice.addTextChangedListener(this)
            }

            override fun afterTextChanged(s: Editable?) {}

        })

        binding.etProductStock.doOnTextChanged { text, _, _, _ ->
            text?.let {
                newStockQty = it.toString()
                if (it.isEmpty()) {
                    binding.etProductStock.typeface = Typeface.DEFAULT
                } else {
                    binding.etProductStock.typeface = Typeface.DEFAULT_BOLD
                }
            }
            viewModel.allStockCountChnaged(text.toString())
        }
        binding.etProductStockMinimum.doOnTextChanged { text, _, _, _ ->
            text?.let {
                newStockMinimumQty = it.toString()
                if (it.isEmpty()) {
                    binding.etProductStockMinimum.typeface = Typeface.DEFAULT
                } else {
                    binding.etProductStockMinimum.typeface = Typeface.DEFAULT_BOLD
                }
            }
            viewModel.minimumStockCountChange(text.toString())
        }

        if (productName.isNotEmpty()) {
            binding.etProductName.setText(productName)
            binding.etProductName.setSelection(productName.length)
        }

        setCogsAvailableUserProperty()
    }

    override fun subscribeState() {
        viewModel.observerProductEvents.observe(this, Observer {
            when (it) {
                is AddProductViewModel.AddProductEvents.UpdateCurrentMeasurement -> {
                    binding.etProductMeasurementUnit.setText(it.measurementName)
                }
                is AddProductViewModel.AddProductEvents.ShowAddNewMeasurementDialog -> {
                    addNewMeasureUnitDialog = CustomMeasurementUnitDialog(this, this)
                    addNewMeasureUnitDialog?.show()
                }
                AddProductViewModel.AddProductEvents.DismissAddNewMeasurementDialog -> {
                    addNewMeasureUnitDialog?.dismiss()
                    Toast.makeText(
                        this,
                        this.getString(R.string.unit_successfully_saved),
                        Toast.LENGTH_SHORT
                    ).show()
                }
                is AddProductViewModel.AddProductEvents.FinishScreen -> {
                    InputUtils.hideKeyboard(this)
                    updateProductCategoriesAssociation(it.product.productId)

                    val resultIntent = Intent().apply {
                        putExtra("PRODUCT_ID", it.product.productId)
                    }


                    ProductRepository.getInstance(this)
                        .updateStockQuantity(0.0, it.product, InventoryOperationType.PERUBAHAN_HARGA_BELI, null, 0.0, it.product.buyingPrice, it.product.measurementName)

                    ProductRepository.getInstance(this)
                        .updateStockQuantity(0.0, it.product, InventoryOperationType.PERUBAHAN_HARGA_JUAL, null, it.product.unitPrice, 0.0, it.product.measurementName)

                    this.setResult(RESULT_OK, resultIntent)


                    if (isAddTransactionFlow || isAddPosFlow) {
                        finish()
                    } else {
                        if (RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()) {
                            finish()
                        } else {
                            MainActivity.startActivitySingleTopToTab(this, TabName.STOCK)
                        }

                    }
                }
                is AddProductViewModel.AddProductEvents.FinishCurrentScreen -> {
                    InputUtils.hideKeyboard(this)
                    updateProductCategoriesAssociation(it.product.productId)
                }
                else -> {}
            }
        })

        stockUnitViewModel.observerUnits.observe(this, Observer {
            when (it) {
                is StockUnitViewModel.StockUnitEvents.ShowUnit -> {
                    units = it.units as MutableList<MeasurementEntity>
                }
                else -> {}
            }
        })

        if (isEdit) {
            inventoryHistoryViewModel.stateData.observe(this, Observer { state ->
                showBasedOnState(state)
            })
        } else {
            binding.flFavourite.setOnClickListener {
                isFav = isFav.xor(true)
                if (isFav) {
                    binding.flFavourite.background = binding.flFavourite.context?.let {
                        ContextCompat.getDrawable(
                            it, R.drawable
                                .bg_yellow_stroke_f7b500
                        )
                    }
                    binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_yellow_fav,
                        0,
                        0,
                        0
                    )
                } else {
                    binding.flFavourite.background = binding.flFavourite.context?.let {
                        ContextCompat.getDrawable(
                            it, R.drawable
                                .button_round_cornerd_edit_stroke_pop_up
                        )
                    }
                    binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_fav_grey,
                        0,
                        0,
                        0
                    )
                }
            }
            addProductActiveStatusListener()
        }

        subscribeSingleLiveEvent(productCategoryViewModel.state) { state ->
            when (state) {
                ProductCategoryViewModel.State.OnSelectionCached -> {
                    val categoriesString = productCategoryViewModel.selectedCategoryList.joinToString { it.name }
                        .takeIf { it.isNotNullOrBlank() } ?: getString(R.string.category_label)

                    binding.etProductCategory.setText(categoriesString)
                }
                else -> {}
            }
        }
    }

    /**
     * Show data based on state when editing stock
     */
    private fun showBasedOnState(state: StockDetailState) {
        when (state.currentProductState) {
            ProductDetailStateType.Loaded -> {
                getEditCurrentMeasurementId(state.productEntity?.measurementName)
                state.productEntity?.let {
                    productEntity = it
                    oldStockValue = "" + it.minimumStock
                    binding.etProductName.setText(it.name)
                    binding.etProductName.setSelection(it.name.length)
                    binding.etProductStock.setText(Utility.getRoundedOffPrice(it.stock))
                    binding.etProductStockMinimum.setText(it.minimumStock.toString())
                    val price = Utility.priceToString(it.unitPrice, false)
                    val buyingPrice = Utility.priceToString(it.buyingPrice, false)
                    binding.etProductPrice.setText(price)
                    binding.etProductBuyingPrice.setText(buyingPrice)
                    binding.etProductMeasurementUnit.setText(it.measurementName)
                }
                if (state.productEntity?.favourite == true) {
                    binding.flFavourite.background = binding.flFavourite.context?.let {
                        ContextCompat.getDrawable(
                            it, R.drawable
                                .bg_yellow_stroke_f7b500
                        )
                    }
                    binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_yellow_fav,
                        0,
                        0,
                        0
                    )
                } else {
                    binding.flFavourite.background = binding.flFavourite.context?.let {
                        ContextCompat.getDrawable(
                            it, R.drawable
                                .button_round_cornerd_edit_stroke_pop_up
                        )
                    }
                    binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_fav_grey,
                        0,
                        0,
                        0
                    )
                }

                oldStockActiveStatus = productEntity.trackInventory
                newStockActiveStatus = productEntity.trackInventory
                oldStockName = productEntity.name
                oldStockPrice = Utility.priceToString(productEntity.unitPrice, false)
                oldStockQty = Utility.getRoundedOffPrice(productEntity.stock)
                oldStockMinimumQty = productEntity.minimumStock.toString()
                oldStockMeasurementUnit = productEntity.measurementName

                if (isTransactionEditFlow) {
                    binding.llStockValues.visibility = View.GONE
                    binding.tvStatusLabel.visibility = View.GONE
                    binding.ivStockToggleTooltip.visibility = View.GONE
                    binding.rgStatus.visibility = View.GONE
                    binding.grayLayer.visibility = View.GONE
                }

                when (productEntity.trackInventory) {
                    AppConst.INVENTORY_TRACKING_ENABLED -> {
                        binding.rbActive.isChecked = true
                    }
                    AppConst.INVENTORY_TRACKING_DISABLED -> {
                        binding.rbInactive.isChecked = true
                        binding.llStockValues.visibility = View.GONE
                        binding.deleteStockBtn.visibility = View.VISIBLE
                        binding.deleteStockBtn.setOnClickListener {
                            showDeleteDialog()
                        }
                    }
                    else -> {}
                }

                addProductActiveStatusListener()

                binding.etProductMeasurementUnit.setOnClickListener {
                    val bookId = User.getBusinessId()
                    StockUnitBottomSheet.instance(bookId, viewModel.currentMeasurementId)
                        .show(supportFragmentManager, "")
                    InputUtils.hideKeyboard(this)
                }

                binding.flFavourite.setOnClickListener {
                    state.productEntity?.favourite =
                        (state.productEntity?.favourite ?: false).xor(true)
                    if (state.productEntity?.favourite == true) {
                        binding.flFavourite.background = binding.flFavourite.context?.let {
                            ContextCompat.getDrawable(
                                it, R.drawable
                                    .bg_yellow_stroke_f7b500
                            )
                        }
                        binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                            R.drawable.ic_yellow_fav,
                            0,
                            0,
                            0
                        )
                    } else {
                        binding.flFavourite.background = binding.flFavourite.context?.let {
                            ContextCompat.getDrawable(
                                it, R.drawable
                                    .button_round_cornerd_edit_stroke_pop_up
                            )
                        }
                        binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                            R.drawable.ic_fav_grey,
                            0,
                            0,
                            0
                        )
                    }
                }

                binding.btnSave.setOnClickListener {
                    trackInventoryEditProductEvent()

                    var minimumStock = 0
                    val newUnitPrice =
                        Utility.extractAmountFromText(binding.etProductPrice.text.toString())
                    val newBuyingPrice =
                        Utility.extractAmountFromText(binding.etProductBuyingPrice.text.toString())

                    if (binding.etProductStockMinimum.text.toString().isNotEmpty()) {
                        minimumStock = binding.etProductStockMinimum.text.toString().toInt()
                    }
                    state.productEntity?.minimumStock = minimumStock
                    val originalStock = state.productEntity?.stock
                    state.productEntity?.stock =
                        Utility.extractAmountFromText(binding.etProductStock.text.toString())

                    val quantityChange = state.productEntity?.stock!!.minus(originalStock!!)
                    val buyingPriceChange = state.productEntity?.unitPrice!!.minus(newUnitPrice!!)
                    val sellingPriceChange = state.productEntity?.buyingPrice!!.minus(newBuyingPrice!!)

                    state.productEntity?.name = binding.etProductName.text.toString()
                    state.productEntity?.measurementName =
                        binding.etProductMeasurementUnit.text.toString()
                    state.productEntity?.unitPrice = newUnitPrice
                    state.productEntity?.buyingPrice = newBuyingPrice
                    state.productEntity?.trackInventory = if (binding.rbActive.isChecked) {
                        AppConst.INVENTORY_TRACKING_ENABLED
                    } else {
                        AppConst.INVENTORY_TRACKING_DISABLED
                    }
                    if ((binding.rbInactive.isChecked && oldStockActiveStatus == AppConst.INVENTORY_TRACKING_ENABLED)
                        || (binding.rbActive.isChecked && oldStockActiveStatus == AppConst.INVENTORY_TRACKING_DISABLED)
                    ) {
                        inventoryDao.deleteByProductId(productId)
                    }

                    if (binding.rbInactive.isChecked && oldStockActiveStatus == AppConst.INVENTORY_TRACKING_ENABLED) {
                        state.productEntity?.stock = 0.0
                        state.productEntity?.minimumStock = 0
                        state.productEntity?.initialStock = 0.0
                    }

                    if (binding.rbActive.isChecked && oldStockActiveStatus == AppConst.INVENTORY_TRACKING_DISABLED) {
                        state.productEntity?.initialStock = state.productEntity?.stock
                    }


                    val operationType: InventoryOperationType = if (quantityChange!! < 0) InventoryOperationType.REMOVE_STOCK else InventoryOperationType.ADD_STOCK
                    ProductRepository.getInstance(this).updateProduct(state.productEntity)
                    if (quantityChange != 0.0) {
                        ProductRepository.getInstance(this)
                            .updateStockQuantity(abs(quantityChange), state.productEntity, operationType, null, 0.0, 0.0, state.productEntity.measurementName)
                    }

                    if(buyingPriceChange!=0.0){
                        ProductRepository.getInstance(this)
                            .updateStockQuantity(0.0, state.productEntity, InventoryOperationType.PERUBAHAN_HARGA_JUAL, null, newUnitPrice, 0.0, state.productEntity.measurementName)
                    }
                    if(sellingPriceChange!=0.0){
                        ProductRepository.getInstance(this)
                            .updateStockQuantity(0.0, state.productEntity, InventoryOperationType.PERUBAHAN_HARGA_BELI, null, 0.0, newBuyingPrice, state.productEntity.measurementName)
                    }
                    state.productEntity?.productId?.let {id -> updateProductCategoriesAssociation(id) }
                    if (isTransactionEditFlow) {
                        finish()
                    } else {
                        if (RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()) {
                            finish()
                        } else {
                            MainActivity.startActivitySingleTopToTab(this, TabName.STOCK)
                        }
                    }
                }
            }
            else -> {}
        }
    }

    private fun getEditCurrentMeasurementId(measurementName: String?) {
        scope.launch {
            val listOfMeasurementUnit = stockUnitViewModel.stockUnit.getAllMeasurementUnit(bookId)
            for (item in listOfMeasurementUnit) {
                if (item.measurementName == measurementName) {
                    viewModel.currentMeasurementId = item.measurementId
                    return@launch
                }
            }
        }
    }

    /**
     * Adds radio group checked change listener for product toggle
     * If isEdit is false - it's called after in setupView
     * If isEdit is true - it's called after the product details from ViewModel have been loaded
     */
    private fun addProductActiveStatusListener() {
        binding.rgStatus.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_active -> {
                    viewModel.trackInventoryStatusChanged(AppConst.INVENTORY_TRACKING_ENABLED)
                    binding.llStockValues.visibility = View.VISIBLE
                    val length = binding.etProductStock.text?.length ?: 0
                    binding.etProductStock.setSelection(length)
                    binding.etProductStock.requestFocus()
                    InputUtils.showKeyboard(this)
                    binding.etProductStock.setText("")
                    binding.etProductStockMinimum.setText("")
                    newStockActiveStatus = AppConst.INVENTORY_TRACKING_ENABLED
                }
                R.id.rb_inactive -> {
                    viewModel.trackInventoryStatusChanged(AppConst.INVENTORY_TRACKING_DISABLED)
                    newStockActiveStatus = AppConst.INVENTORY_TRACKING_DISABLED
                    Utilities.showExitDialog(
                        context = this,
                        onConfirm = {
                            binding.llStockValues.visibility = View.GONE
                        },
                        onCancel = {
                            binding.llStockValues.visibility = View.VISIBLE
                            binding.rbActive.isChecked = true
                        },
                        title = R.string.change_stock_active_status,
                        body = R.string.change_stock_active_status_body,
                        btnLeft = R.string.edit,
                        btnRight = R.string.batal
                    )
                }
                else -> {}
            }
        }
    }

    private fun onAddProductButtonClicked() {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600 ||
            TextUtils.isEmpty(binding.etProductName.text)
        ) return

        lastButtonSaveClicked = SystemClock.elapsedRealtime()

        val source = when {
            isAddTransactionFlow -> AnalyticsConst.TXN_PROD_DETAIL_SOURCE
            isFromPlusIcon -> AnalyticsConst.POS_PLUS_ICON
            isAddPosFlow -> AnalyticsConst.POS_ADD_PROD_SHORTCUT
            else -> AnalyticsConst.STOCK_MENU_SOURCE
        }

        viewModel.addProduct(source, isFav, productCategoryViewModel.selectedCategoryList.map { it.name })
    }

    private fun showDeleteDialog() {

        val dialog = DeleteProductDialog(this) {
            if (it) {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CONFIRM)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_DELETE_PRODUCT, propBuilder)
                inventoryHistoryViewModel.stateData.removeObservers(this)
                ProductRepository.getInstance(this).deleteProductByProductId(productId)
                finish()
            } else {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CANCEL)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_DELETE_PRODUCT, propBuilder)
            }
        }
        dialog.show()
    }

    override fun onBackPressed() {
        InputUtils.hideKeyboard(this)
        if (oldStockQty == "0" && newStockQty.isEmpty()) {
            newStockQty = "0"
        }
        if (oldStockMinimumQty == "0" && newStockMinimumQty.isEmpty()) {
            newStockMinimumQty = "0"
        }
        if (oldStockName != newStockName
            || oldStockPrice != newStockPrice
            || oldStockActiveStatus != newStockActiveStatus
            || oldStockQty != newStockQty
            || oldStockMinimumQty != newStockMinimumQty
            || oldStockMeasurementUnit != newStockMeasurementUnit
        ) {
            Utilities.showExitDialog(
                context = this,
                onConfirm = { finish() },
                onCancel = {},
                title = R.string.exit_change_item,
                body = R.string.changed_data_will_not_be_save,
                btnLeft = R.string.utang_transaksi_exit_dialog_yes_btn,
                btnRight = R.string.continue_change
            )
        } else {
            super.onBackPressed()
        }
    }

    override fun onMeasurementSelected(unitId: String, unitName: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, unitName)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.onMeasurementChanged(unitId, unitName)
    }

    override fun addNewMeasurement() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CREATE_NEW)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.addNewMeasurementRequest()
    }

    override fun addMeasurement(measurement: String) {
        units.forEach {
            if (it.measurementName == measurement) {
                InputUtils.hideKeyboard(this)
                Toast.makeText(
                    this,
                    this.getString(R.string.unit_already_exists_please_use_another_name),
                    Toast.LENGTH_SHORT
                ).show()
                return
            }
        }
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CREATE_NEW)
        propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, measurement)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
        viewModel.addNewMeasurement(measurement)
    }

    private fun trackInventoryEditProductEvent() {
        val newPrice = Utility.extractAmountFromText(newStockPrice)
        val sellingPriceUpdateType =
            if (productEntity.unitPrice == 0.0) AnalyticsConst.ADDED_NEW else AnalyticsConst.CHANGE_EXISTING
        val buyingPrice =
            Utility.extractAmountFromText(binding.etProductBuyingPrice.text.toString())
        var entryPoint = ""
        if (isTransactionEditFlow) {
            entryPoint = AnalyticsConst.VIA_TRANSAKSI_FORM
        } else if (isInventoryEditFlow) {
            entryPoint = AnalyticsConst.VIA_STOCK_MENU_VIEW_PRODUCT
        }

        var useStock = ""
        when (newStockActiveStatus) {
            AppConst.INVENTORY_TRACKING_ENABLED -> {
                useStock = AnalyticsConst.YES
            }
            AppConst.INVENTORY_TRACKING_DISABLED -> {
                useStock = AnalyticsConst.NO
            }
        }

        if (newStockMinimumQty.isEmpty())
            newStockMinimumQty = "0"

        AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.USE_STOCK, useStock)
            put(AnalyticsConst.UPDATE_SELLING_PRICE, newPrice != productEntity.unitPrice)
            put(AnalyticsConst.SELLING_PRICE_UPDATE_TYPE, sellingPriceUpdateType)
            put(
                AnalyticsConst.UPDATED_MINIMUM_STOCK,
                newStockMinimumQty != productEntity.minimumStock.toString()
            )
            put(AnalyticsConst.LAST_MINIMUM_STOCK_VALUE, productEntity.minimumStock)
            put(AnalyticsConst.UPDATED_MINIMUM_STOCK_VALUE, newStockMinimumQty)
            put(
                AnalyticsConst.IS_UPDATED_STOCK_VALUE_GREATER,
                newStockMinimumQty.toInt() > productEntity.minimumStock
            )
            put(AnalyticsConst.EDIT_PRODUCT_SOURCE, entryPoint)
            put(
                AnalyticsConst.QUANTITY_TYPE,
                Utility.getQuantityTypeFromTotalStock(productEntity.stock)
            )
            put(AnalyticsConst.BUYING_PRICE, (buyingPrice != 0.0).toString())
        }.also {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_EDIT_PRODUCT, it)
        }
    }

    private fun setCogsAvailableUserProperty() {
        AppAnalytics.setUserProperty(
            AnalyticsConst.BUYING_PRICE_ENABLED,
            isCogsAvailable.toString()
        )
    }

    override fun dismiss() {}

    private fun updateProductCategoriesAssociation(productId: String){
        productCategoryViewModel.onEventReceipt(ProductCategoryViewModel.Event.AssociateProductCategories(productId))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK) return

        when (requestCode) {
            PRODUCT_CATEGORY_CODE -> parseProductCategoryResult(data)
        }
    }

    private fun parseProductCategoryResult(data: Intent?) {
        data ?: return
        val categoryIds =
            data.getStringArrayExtra(ProductCategoryActivity.SELECTED_CATEGORY_IDS)?.toList() ?: emptyList()
        productCategoryViewModel.onEventReceipt(ProductCategoryViewModel.Event.UpdateSelections(categoryIds))
    }

    companion object {
        const val PRODUCT_ID = "product_id"
        const val BOOK_ID = "book_id"
        const val TRANSACTION_FLOW = "transaction_flow"
        const val TRANSACTION_ADD_FLOW = "transaction_add_flow"
        const val POS_ADD_FLOW = "pos_add_flow"
        const val IS_FROM_PLUS_ICON = "is_from_plus_icon"
        const val TRANSACTION_EDIT_FLOW = "transaction_edit_flow"
        const val INVENTORY_EDIT_FLOW = "inventory_edit_flow"
        const val PRODUCT_ADDED_SUCCESS = 100
        const val PRODUCT_NAME = "product_name"
        const val EDIT_SOURCE = "edit_source"
        const val PRODUCT_CATEGORY_CODE = 1133

        fun getNewIntent(
            context: Context, productId: String, bookId: String,
            isTransactionalEditFlow: Boolean = false,
            isTransactionalAddFlow: Boolean = false,
            isPosAddFlow: Boolean = false,
            isFromPlusIcon: Boolean = false,
            isInventoryEditFlow: Boolean = false,
            productName: String = "",
            editSource: String = AnalyticsConst.NON_0_PRODUCT
        ): Intent {
            val intent = Intent(context, EditStockActivity::class.java)
            intent.putExtra(PRODUCT_ID, productId)
            intent.putExtra(BOOK_ID, bookId)
            intent.putExtra(TRANSACTION_ADD_FLOW, isTransactionalAddFlow)
            intent.putExtra(POS_ADD_FLOW, isFromPlusIcon)
            intent.putExtra(IS_FROM_PLUS_ICON, isPosAddFlow)
            intent.putExtra(TRANSACTION_EDIT_FLOW, isTransactionalEditFlow)
            intent.putExtra(INVENTORY_EDIT_FLOW, isInventoryEditFlow)
            intent.putExtra(PRODUCT_NAME, productName)
            intent.putExtra(EDIT_SOURCE, editSource)
            return intent
        }
    }

    fun hideKeyBoard() {
        keyboard?.apply {
            if (this.isAttachedToWindow) {
                visibility = View.GONE
                submit()
            }
        }
    }

    private fun changeKeyboardFocus(v: Int) {
        keyboard.apply {
            binding.etProductName.clearFocus()
            InputUtils.hideKeyboard(this@EditStockActivity)
            if (v == 1) {
                setCurrency(binding.tvCurrency)
                binding.tvExpr.text = ""
                setResultLayout(binding.llResult)
                keyboard.setResultTv(binding.etProductBuyingPrice)
                binding.etProductBuyingPrice.clearFocus()
            } else {
                setCurrency(binding.tvCurrency)
                binding.tvExpr.text = ""
                setResultLayout(binding.llResult)
                keyboard.setResultTv(binding.etProductPrice)
                binding.etProductPrice.clearFocus()
            }
            val moveup = AnimationUtils.loadAnimation(context, R.anim.move_up)
            if (context.isAnimEnabled()) startAnimation(moveup)
            visibility = View.VISIBLE
        }
    }

    private fun initKeyboard() {
        keyboard = binding.keyboardView
        keyboard?.apply {
            cursor = binding.cursor
            setCurrency(binding.tvCurrency)
            setExprTv(binding.tvExpr)
            setResultLayout(binding.llResult)
            binding.llResult.hideView()
        }
    }
}