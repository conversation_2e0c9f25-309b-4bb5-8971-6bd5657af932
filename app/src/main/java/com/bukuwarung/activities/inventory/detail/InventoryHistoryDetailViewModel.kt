package com.bukuwarung.activities.inventory.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.bukuwarung.activities.inventory.model.InventoryHistoryData
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.superclasses.DataHolder.NoResultRowHolder
import com.bukuwarung.database.entity.InventoryHistoryEntity
import com.bukuwarung.database.entity.InventoryOperationType
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.inventory.removePrecisionIfZero
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.*
import javax.inject.Inject

@HiltViewModel
class InventoryHistoryDetailViewModel @Inject constructor(
    private val inventoryRepository: InventoryRepository,
    private val productRepository: ProductRepository
) : ViewModel() {
    private var productId: String = ""
    private val removePrecision: (Double) -> String = { removePrecisionIfZero(it) }
    private var productSellingPrice: Double = 0.0
    private var isForTransactionFlow = false

    fun initData(productId: String) {
        this.productId = productId
        getData()
    }

    fun init(isForTransactionFlow: Boolean) {
        this.isForTransactionFlow =  isForTransactionFlow
        editProductState.value = EditProductState.ShowInputUI(this.isForTransactionFlow)
    }

    lateinit var stateData: MediatorLiveData<StockDetailState>
    private val editProductState = MutableLiveData<EditProductState>()
    val observerEditProductState: LiveData<EditProductState> = editProductState


    // get product and inventory data.
    private fun getData(productId: String = this.productId) {
        stateData = MediatorLiveData()

        var inventHistoryEntityData: LiveData<List<InventoryHistoryEntity>> =
                inventoryRepository
                        .getInventoryHistory(productId)

        stateData.addSource(
                inventHistoryEntityData
        ) {
            addInventoryDataToState(it)
        }

        var productEntityData: LiveData<ProductEntity> =
                productRepository
                        .getProductsLiveDataById(productId)

        stateData.addSource(
                productEntityData
        ) {
            addProductDataToState(it)
        }

    }

    // add inventory data state
    private fun addInventoryDataToState(it: List<InventoryHistoryEntity>?) {
        val currentState = stateData.value ?: StockDetailState()
        stateData.value = currentState.copy(
                inventoryHistoryData = transformToLiveData(it),
                inventoryDetailStateType = if (it == null) {
                    InventoryDetailStateType.NotFound
                } else {
                    InventoryDetailStateType.Loaded
                }
        )
    }

    // add product entity state
    private fun addProductDataToState(it: ProductEntity?) {
        val currentProductState = stateData.value ?: StockDetailState()
        productSellingPrice = it?.unitPrice ?: 0.0
        stateData.value = currentProductState.copy(
                productEntity = it,
                currentProductState = if (it == null) {
                    ProductDetailStateType.NotFound
                } else {
                    ProductDetailStateType.Loaded
                }
        )
    }

    fun productPriceChange(price: String?) {
        try {
            if (!price.isNullOrBlank()) {
                val cleanBalance = Utility.cleanBalance(price.trim())
                this.productSellingPrice = cleanBalance.toDouble()
            } else {
                productSellingPrice = 0.0
            }
        } catch (e: Exception) {
            e.recordException()
            productSellingPrice = 0.0
        }
    }


    // transform to model class data
    private fun transformToLiveData(inventoryHistoryEntity: List<InventoryHistoryEntity>?) : ArrayList<DataHolder>?
    {
        var inventHistoryDataList: ArrayList<DataHolder> = ArrayList()
        var runningStock = Double.MIN_VALUE;
        if (inventoryHistoryEntity != null) {
            var productEntity: ProductEntity? = null
            for (it in inventoryHistoryEntity) {

                try {
                    if (runningStock.equals(Double.MIN_VALUE)) {
                        productEntity = productRepository.getProductsById(it.productId)
                        runningStock = productEntity.initialStock;
                    }
                }catch (e: Exception) {
                    e.recordException()
                    runningStock = 0.0
                }
                val transactionId = inventoryRepository.getInventoryHistoryTransactionId(it.historyId)
                val inventoryHistoryData = InventoryHistoryData()
                if (it.operationType == InventoryOperationType.ADD_STOCK || it.operationType == InventoryOperationType.EXPENSE_TRANSACTION) {
                    runningStock=runningStock+it.quantityChange;
                } else if (it.operationType == InventoryOperationType.REMOVE_STOCK || it.operationType == InventoryOperationType.SALE_TRANSACTION) {
                    runningStock=runningStock-it.quantityChange;
                }
                inventoryHistoryData.currentStock = runningStock
                inventoryHistoryData.dateTime=it.createdAt
                inventoryHistoryData.minStock= it.minimumStock
                inventoryHistoryData.updateStock=it.quantityChange
                inventoryHistoryData.transactionId=transactionId
                inventoryHistoryData.operationType=it.operationType.name
                if (it.buyingPrice != 0.0) {
                    inventoryHistoryData.hargaValue = Utility.formatAmount(it.buyingPrice) +"/" + it.measurementName
                }
                if (it.sellingPrice != 0.0) {
                    inventoryHistoryData.hargaValue = Utility.formatAmount(it.sellingPrice) +"/" + it.measurementName
                }

                inventHistoryDataList.add(inventoryHistoryData)
            }
            try {
                if (productEntity!=null && !runningStock.equals(Double.MIN_VALUE) && productEntity.stock!=runningStock) {
                    productEntity.stock = runningStock
                    productRepository.updateProduct(productEntity)
                }
            }catch (e: Exception) {
                e.recordException()
            }
            Collections.reverse(inventHistoryDataList);
//            try{
//                var productEntity = productRepository.getProductsById(productId)
//                val inventoryHistoryDataBeli = InventoryHistoryData()
//                inventoryHistoryDataBeli.operationType= InventoryOperationType.PERUBAHAN_HARGA_BELI.name;
//                inventoryHistoryDataBeli.updateStock = 0.0
//                inventoryHistoryDataBeli.hargaValue = ""+Utility.formatAmount(productEntity.buyingPrice) +"/"+productEntity.measurementName
//                inventoryHistoryDataBeli.dateTime=productEntity.updatedAt
//
//
//                val inventoryHistoryDataJual = InventoryHistoryData()
//                inventoryHistoryDataJual.operationType= InventoryOperationType.PERUBAHAN_HARGA_JUAL.name;
//                inventoryHistoryDataJual.hargaValue = ""+Utility.formatAmount(productEntity.unitPrice) +"/"+productEntity.measurementName
//                inventoryHistoryDataJual.dateTime=productEntity.updatedAt
//                inventoryHistoryDataJual.currentStock = 0.0
//                inventoryHistoryDataJual.updateStock = 0.0
//                inventHistoryDataList.add(inventoryHistoryDataJual)
//                inventHistoryDataList.add(inventoryHistoryDataBeli)
//
//            }catch (e:Exception)
//            {
//                e.recordException()
//            }

            inventHistoryDataList.add(NoResultRowHolder())
        }
        return inventHistoryDataList

    }

    sealed class EditProductState {
        data class ShowInputUI(val hideMinMax: Boolean = false) : EditProductState()
    }



}

data class StockDetailState(
        val inventoryHistoryData: ArrayList<out DataHolder>? = null,
        val inventoryDetailStateType: InventoryDetailStateType = InventoryDetailStateType.Loading,
        val productEntity: ProductEntity? = null,
        val currentProductState: ProductDetailStateType = ProductDetailStateType.Loading
)

enum class ProductDetailStateType {
    Loading,
    Loaded,
    NotFound
}

enum class InventoryDetailStateType {
    Loading,
    Loaded,
    NotFound
}