package com.bukuwarung.activities.payment

import android.os.Handler
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.*
import com.bukuwarung.los.LosUseCase
import com.bukuwarung.payments.constants.*
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.ReminderEligibilityResponse
import com.bukuwarung.payments.history.OrderHistoryAdapter
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.wrapper.EventWrapper
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import kotlinx.coroutines.*
import java.lang.Runnable
import java.util.*
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class PaymentTabViewModel @Inject constructor(private val LosUseCase: LosUseCase,
                                              private val businessUseCase: BusinessUseCase,
                                              private val finproUseCase: FinproUseCase,
                                              private val onboardingPrefManager: OnboardingPrefManager,
                                              private val sessionManager: SessionManager,
                                              private val paymentUseCase: PaymentUseCase,
                                              private val bankingUseCase: BankingUseCase,
                                              private val janusUseCase: JanusUseCase,
                                              private val remindersUseCase: ReminderUseCase,
                                              private val riskUseCase: RiskUseCase,
                                              private val ordersUseCase: OrdersUseCase,
                                              private val customerUseCase: CustomerUseCase) : BaseViewModel() {
    sealed class State {
        data class SetBookData(val bookId: String?, val businessName: String?) : State()

        data class SetSummaryData(val summaryResponse: PaymentSummaryResponse?, val bookId: String?) : State()
        data class SetPaymentList(val list: List<PaymentHistory>) : State()
        data class IsPpobAvailable(val fragmentBodyBlock: BodyBlock?) : State()
        data class IsCRMReminderAvailable(val available: Boolean): State()
        object showEmptyStateInfo : State()
        data class GoToCreatePayment(val paymentType: Int, val bookId: String?): State()
        data class BannerList(val bannerList: List<PaymentBannerInfoResponse>) : State()
        data class SetPaymentInfoSaving(val paymentMetadata: PaymentMetadata): State()
        object ShowPpobCoachmark : State()
        object ShowSaldoCoachmark : State()
        object ShowKybPendingBottomSheet : State()
        data class SaldoState(
            val fetching: Boolean, val saldoData: SaldoResponse?,
            val error: Boolean, val errorMessage: String?,
            val isSaldoFreeze: Boolean
        ) : State()
        data class ShowPpobPulsaBottomsheet(val merchantCategory: String?): State()
        data class ShowPpobListrikBottomsheet(val merchantCategory: String?): State()
        data class BnplInfo(val isRegistered: Boolean, val bnplCurrentLimit: Double) : State()
        object BnplError : State()
    }

    sealed class Event {
        object OnBnplDetailsRequested: Event()
        data class LinkedItemsFetched(var adapterPos: Int) : Event()
        data class ApiError(var errorMessage: String?, var adapterPos: Int) : Event()
    }

    fun onEventReceived(event: PaymentTabViewModel.Event) {

    }

    private var handler: Handler? =null
    private var paymentPrefManager = PaymentPrefManager.getInstance()
    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private val _saldoState = MutableLiveData<EventWrapper<State>>()
    val saldoState: LiveData<EventWrapper<State>> = _saldoState
    private val _bnplState = MutableLiveData<EventWrapper<State>>()
    val bnplState: LiveData<EventWrapper<State>> = _bnplState
    private var hasLoaded = false
    private var bookId: String? = null
    var paymentMetadata: PaymentMetadata? = null
    var featureFlags: BankingFeatureFlags? = BankingFeatureFlags()
    var kycAccount: KycStatusResponse? = null
    private var paymentOutCount: Int? = null
    private var paymentCountAll: Int = 0
    private var _selectedBankAccount = MutableLiveData<List<BankAccount>>()
    val selectedBankAccount: LiveData<List<BankAccount>> = _selectedBankAccount
    private var haveMadeActivationRequest = false
    private var observeProfile: LiveData<UserProfileEntity>? = null
    var ppobCount = 0.0
    var paymentCount = 0.0

    data class ViewState(
            val showLoading: Boolean = true,
            val showError: Boolean = false,
            val showEmptyState: Boolean = false,
            val saldoBalance: Double? = null,
            val cashbackBalance: Double? = null,
            val saldoEnabled: Boolean? = false
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    private var list: List<PaymentHistory> = emptyList()
    private var hasShownPpobCoachmark = false
    private var hasShownSaldoCoachmark = false

    private val _qrisDeactivatedGroup = MutableLiveData<WhitelistGroup>()
    val qrisDeactivatedGroup: LiveData<WhitelistGroup> = _qrisDeactivatedGroup

    var qrisData: QrisResponse? = null
    private val _qrisData = MutableLiveData<QrisResponse>()
    val qrisDataLive: LiveData<QrisResponse> = _qrisData

    var merchantBankAccounts: ApiResponse<List<BankAccount>>?? = null

    val linkedOrdersMap = hashMapOf<String, LinkedOrdersData>()
    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event

    fun finishedCoachmark(id: String?) {
        id ?: return
        onboardingPrefManager.setHasFinishedForId(id)
    }

    fun handleOnResume() = viewModelScope.launch {
        getPaymentSummary()
        if (paymentPrefManager.getHasActivatedSaldo()) checkSaldoBalance()
        getFeatureFlags()
        getPaymentLimits()
    }

    /**
     * Fetch payment limits for ADVANCED tier users
     */
    private fun getPaymentLimits() = viewModelScope.launch(Dispatchers.IO) {
        if (paymentPrefManager.getKycTier() == KycTier.ADVANCED) {
            val response = paymentUseCase.getPaymentLimits()
            if (response is ApiSuccessResponse) {
                paymentPrefManager.setPaymentLimits(response.body)
            } else {
                paymentPrefManager.setPaymentLimits(null)
            }
        } else {
            paymentPrefManager.setPaymentLimits(null)
        }
    }

    private fun checkPpobConfig(fragmentBodyBlock: BodyBlock?) {
        setState(
            State.IsPpobAvailable(fragmentBodyBlock)
        )
    }

    private fun checkReminderAvailability(isAvailable: Boolean) { //to show reminders, it should be both enabled from remote config and backend.
        setState(State.IsCRMReminderAvailable(paymentPrefManager.shouldShowRemindersOptions() && isAvailable))
    }

    fun checkPpobAndSaldoCoachmark() = viewModelScope.launch {
        if (!onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTORIAL_PPOB_INTRODUCTION) && !hasShownPpobCoachmark) {
            hasShownPpobCoachmark = true
            setState(State.ShowPpobCoachmark)
        } else {
            checkSaldoCoachmark()
        }
    }

    private fun checkSaldoCoachmark() = viewModelScope.launch {
        // NOTE: Adding delay because coachmark check is happening onResume and we don't receive the
        // api response from backend yet, so coachmark is never visible when screen appears first time.
        delay(2000)
        if (!onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTORIAL_SALDO_CTA) && !hasShownSaldoCoachmark) {
            hasShownSaldoCoachmark = true
            setState(State.ShowSaldoCoachmark)
        }
    }

    fun handleOnCreateView() = viewModelScope.launch {
        publishStateBusinessId()
        getFeatureFlags()
        getPaymentLimits()
        if (!paymentPrefManager.getHasActivatedSaldo()) checkSaldoActivation()
        // Fix issue SUP-4642, where customer is entered with "null" customerId
        if (!paymentPrefManager.cleanedCustomerEntries()) cleanCustomerEntries()
    }

    /**
     * To fix SUP-4642,
     * In 3.59, a bug was introduced which added customer in DB with "null" as the customer_id,
     * We have to delete such entries.
     */
    private fun cleanCustomerEntries() = viewModelScope.launch(Dispatchers.IO) {
        customerUseCase.deleteCustomerById("null")
        paymentPrefManager.setCleanedCustomerEntries()
    }

    private fun getFeatureFlags() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                handleFeatureFlags(bankingUseCase.getFeatureFlags())
            }
        }
    }

    private fun showKybPendingSheetIfRequired() {
        viewModelScope.launch {
            if (!RemoteConfigUtils.getPaymentConfigs().enableKybAlerts.isTrue) {
                return@launch
            }
            val date = PaymentPrefManager.getInstance().getKybBottomSheetShownDate()
            // If user is approved or pending
            if (qrisData?.isApprovedQrisUser().isTrue || qrisData?.finalStatus == QrisAndKycStatus.PENDING.name) {
                // Check for KYB status -> UNVERIFIED/null
                if (qrisData?.kybStatus == VerificationStatus.UNVERIFIED.name || qrisData?.kybStatus == null) {
                    if (RemoteConfigUtils.getPaymentConfigs().kybMandatoryFromDate?.parseToZeroHourDate()
                            ?.after(Calendar.getInstance()).isTrue
                    ) {
                        if (date == null || date.parseToZeroHourDate().beforeToday()) {
                            setState(State.ShowKybPendingBottomSheet)
                        }
                    }
                }
            }
        }
    }

    fun getReminderFlag() = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            handleReminderFlag(remindersUseCase.checkEligibility(SessionManager.getInstance().businessId))
        }
    }

    fun getPpobAndPaymentCount() = launch {
        when (val result = finproUseCase.getPaymentSummary(SessionManager.getInstance().businessId)) {
            is ApiSuccessResponse -> {
                ppobCount = result.body.count?.ppob.orNil
                paymentCount = result.body.count?.paymentIn.orNil + result.body.count?.paymentOut.orNil
            }
            else -> {}
        }
    }

    fun getFirstTransaction() = launch {
        var paymentOutCreatedDate: String? = null
        var ppobCreatedDate: String? = null
        var listrikCreatedDate: String? = null
        when (val result = paymentUseCase.getFirstTransaction(SessionManager.getInstance().businessId)) {
            is ApiSuccessResponse -> {
                val firstTransaction = result.body
                firstTransaction.apply {
                    paymentOutCreatedDate = first_payment_out?.created_at
                    listrikCreatedDate = first_ppob_listrik?.created_at
                    ppobCreatedDate = first_ppob_pulsa?.created_at

                    if (merchant_category != "PPOB" && merchant_category != "PAYMENT_AGENT" && merchant_category != "FMCG") {
                        return@apply
                    }

                    if (merchant_category == "PAYMENT_AGENT") {
                        if (paymentOutCreatedDate != null && listrikCreatedDate == null &&
                                ppobCreatedDate == null && !sessionManager.hasPpobPulsaSeen()) {
                            val diff = getTimeOfFirstPaymentDifference(paymentOutCreatedDate)
                            if (diff in 3..7 && sessionManager.hasPpobPulsaSeenCount() < 2) {
                                setState(State.ShowPpobPulsaBottomsheet(merchant_category))
                            }
                        }
                    }

                    if (merchant_category == "FMCG") {
                        if (paymentOutCreatedDate != null && listrikCreatedDate == null &&
                                ppobCreatedDate == null && !sessionManager.hasPpobPulsaSeen()) {
                            val diff = getTimeOfFirstPaymentDifference(paymentOutCreatedDate)
                            if (diff in 2..7 && sessionManager.hasPpobPulsaSeenCount() < 2) {
                                setState(State.ShowPpobPulsaBottomsheet(merchant_category))
                            }
                        }
                    }

                    if (merchant_category == "PPOB") {
                        if (paymentOutCreatedDate == null && listrikCreatedDate == null &&
                                ppobCreatedDate != null && !sessionManager.hasPpobListrikSeen()) {
                            val diff = getTimeOfFirstPaymentDifference(ppobCreatedDate)
                            if (diff in 1..7 && sessionManager.hasPpobPulsaSeenCount() < 2) {
                                setState(State.ShowPpobListrikBottomsheet(merchant_category))
                            }
                        }
                    }
                }
            }
            else -> {}
        }
    }

    private fun getTimeOfFirstPaymentDifference(firstPaymentTime: String?) : Long {
        return DateTimeUtilsKt.getDaysDifference(firstPaymentTime)
    }

    private fun handleReminderFlag(result: ApiResponse<ReminderEligibilityResponse>) = viewModelScope.launch {
        when (result) {
            is ApiSuccessResponse -> {
                paymentPrefManager.setShowRemindersOption(result.body.eligibleForReminder ?: false)
            }
            else -> {
                paymentPrefManager.setShowRemindersOption(false)
            }
        }
    }

    /**
     * Fetches payment metadata and qris status synchronously.
     * It was required because before setting the banners, we need both kyc and qris status
     */
    fun getQrisAndPaymentMetaData(isQrisEnabled: Boolean = false) {
        viewModelScope.launch {
            withContext(Dispatchers.Main) {
                var metadata: ApiResponse<PaymentMetadata>? = null
                var qrisResponse: ApiResponse<QrisResponse>? = null
                var kycAccount: ApiResponse<KycStatusResponse>? = null
                var bankAccounts: ApiResponse<List<BankAccount>>? = null

                coroutineScope {
                    launch {
                        metadata = paymentUseCase.getMetadata()
                    }
                    if (isQrisEnabled) {
                        bookId?.let {
                            launch {
                                qrisResponse = bankingUseCase.getQrisStatus(it)
                                bankAccounts = paymentUseCase.getMerchantBankAccounts(it)
                            }
                        }
                    }
                    val kybStatus = paymentPrefManager.getKybStatus()
                    if (paymentPrefManager.getKycTier().isNotVerified() || kybStatus?.isNotVerified().isTrue) {
                        sessionManager.kycAccountId?.let {
                            launch {
                                kycAccount = janusUseCase.getKycAccount(it)
                            }
                        } ?: launch {
                            kycAccount = janusUseCase.createKycAccount()
                        }
                    }
                }
                qrisResponse?.let { handleQrisResponse(it, bankAccounts) }
                kycAccount?.let { handleKycResponse(it) }
                metadata?.let { handlePaymentMetaData(it) }
            }
        }
    }

    private fun handleFeatureFlags(result: ApiResponse<BankingFeatureFlags>) = viewModelScope.launch {
        when (result) {
            is ApiSuccessResponse -> {
                featureFlags = result.body
                paymentPrefManager.setQrisFeatureFlag(featureFlags?.qris.isTrue)
                getQrisAndPaymentMetaData(featureFlags?.qris.isTrue)
            }
            else -> {
                getQrisAndPaymentMetaData(false)
            }
        }
    }

    private fun handlePaymentMetaData(result: ApiResponse<PaymentMetadata>) = viewModelScope.launch {
        when (result) {
            is ApiSuccessResponse -> {
                paymentPrefManager.setPaymentMetadata(result.body)
                paymentMetadata = result.body
                handleKycData()
                getBannerInfo()
                showSmallBannerMessage(result.body)
            }
            else -> {
                paymentMetadata = paymentPrefManager.getPaymentMetadata()
            }
        }
    }

    val observer = Observer<UserProfileEntity?> {
        if (haveMadeActivationRequest || it == null) return@Observer
        val bookEntity = businessUseCase.getBusinessById(SessionManager.getInstance().businessId)
        val bookName = bookEntity?.bookName ?: bookEntity?.businessName
        // We first check for profile name, it can be null so we fallback to
        // book name or business name from BookEntity
        val saldoVaName = it.userName ?: bookName
        val request = ActivateSaldoRequest(saldoVaName, sessionManager.userId)
        launch {
            haveMadeActivationRequest = true
            when (val result = finproUseCase.activateSaldo(request)) {
                is ApiSuccessResponse -> {
                    paymentPrefManager.setHasActivatedSaldo(true)
                    paymentPrefManager.setSaldoEnabledState(result.body.visible.isTrue)
                    paymentPrefManager.setSaldoInfo(result.body)
                    val balance = result.body.subBalance?.saldo.ifNull { result.body.amount }
                    setViewState(
                        currentViewState().copy(
                            saldoBalance = balance,
                            saldoEnabled = result.body.visible,
                            cashbackBalance = result.body.subBalance?.cashback
                        )
                    )
                }
                else -> {}
            }
        }
    }

    private fun checkSaldoActivation() {
        observeProfile = businessUseCase.getUserProfile(User.getUserId())
        // When we first login, user profile will be empty
        observeProfile?.observeForever(observer)
    }

    override fun onCleared() {
        super.onCleared()
        observeProfile?.removeObserver(observer)
    }

    fun checkSaldoBalance() = launch {
        setSaldoState(
            State.SaldoState(
                fetching = true, saldoData = null, error = false, errorMessage = null, isSaldoFreeze = false
            )
        )
        when (val result = finproUseCase.getSaldo()) {
            is ApiSuccessResponse -> {
                paymentPrefManager.setSaldoEnabledState(result.body.visible.isTrue)
                paymentPrefManager.setSaldoInfo(result.body)
                val balance = result.body.subBalance?.saldo.ifNull { result.body.amount }
                setViewState(
                    currentViewState().copy(
                        saldoBalance = balance,
                        saldoEnabled = result.body.visible,
                        cashbackBalance = result.body.subBalance?.cashback
                    )
                )
                setSaldoState(
                    State.SaldoState(
                        fetching = false, saldoData = result.body,
                        error = false, errorMessage = null,
                        isSaldoFreeze = result.body.isSaldoFreezed.isTrue
                    )
                )
            }
            is ApiErrorResponse -> {
                setSaldoState(
                    State.SaldoState(
                        fetching = false, saldoData = null,
                        error = true, errorMessage = result.errorMessage,
                        isSaldoFreeze = false
                    )
                )
            }
            else -> {
                setSaldoState(
                    State.SaldoState(
                        fetching = false, saldoData = null,
                        error = true, errorMessage = null,
                        isSaldoFreeze = false
                    )
                )
            }
        }
    }

    private fun handleQrisResponse(
        qrisRes: ApiResponse<QrisResponse>, bankAccounts: ApiResponse<List<BankAccount>>?
    ) = launch {
        if (qrisRes is ApiSuccessResponse) {
            qrisData = qrisRes.body
            _qrisData.postValue(qrisData)
            merchantBankAccounts = bankAccounts
            val qrisBank = if (bankAccounts is ApiSuccessResponse) {
                bankAccounts.body.firstOrNull {
                    it.isQrisBank == true
                }
            } else null
            qrisRes.body.let {
                val qrisInfo = QrisInfoSubset(
                    virtualAccountId = it.virtualAccountId, kycStatus = it.kycStatus,
                    kybStatus = it.kybStatus,
                    qrisStatus = it.qrisStatus, qrisCode = it.qrisCode,
                    matchingStatus = it.matchingStatus, finalStatus = it.finalStatus,
                    qrisBookId = it.metadata?.qris?.paymentBookId,
                    qrisBookName = it.metadata?.qris?.businessName,
                    qrisBankId = qrisBank?.bankAccountId
                )
                paymentPrefManager.setQrisInfo(qrisInfo)
                if (it.finalStatus == QrisAndKycStatus.REJECTED.name) {
                    paymentPrefManager.setQrisRejectedTs(System.currentTimeMillis(), false)
                }
            }
            qrisRes.body.kybStatus?.let {
                AppAnalytics.setUserProperty(
                    AnalyticsConst.KYB_STATUS,
                    PaymentUtils.getKybStatusForEvents(qrisRes.body.kybStatus)
                )
            }
            showKybPendingSheetIfRequired()
            isQrisDeactivated()
        }
    }

    /**
     * Checks if user's QRIS is deactivated. All the users with deactivated QRIS will be added
     * to the user group with remote configurable groupCode on risk service.
     *
     * When we fetch the list of groups for this user, if that list contains a group and
     * it's status is ENABLED, it is considered that user is part of the deactivated QRIS User Group.
     */
    private fun isQrisDeactivated() = viewModelScope.launch {
        RemoteConfigUtils.getPaymentConfigs().deactivatedQrisGroupCode?.let {
            withContext(Dispatchers.IO) {
                val response = riskUseCase.getWhitelistGroups(it)
                if (response is ApiSuccessResponse) {
                    withContext(Dispatchers.Main) {
                        val group = response.body.firstOrNull()
                        group?.let {
                            _qrisDeactivatedGroup.value = it
                            PaymentPrefManager.getInstance().setQrisGroupData(it)
                        }
                    }
                }
            }
        }
    }

    fun isQrisAccountBlocked(): Boolean {
        return merchantBankAccounts?.let { res ->
            if (res is ApiSuccessResponse) {
                res.body.firstOrNull {
                    it.isQrisBank.isTrue && (it.flag.equals(
                        PpobConst.IN_ACTIVE,
                        ignoreCase = true
                    ) || it.flag.equals(PpobConst.BLOCKED, ignoreCase = true))
                } != null
            } else false
        } ?: run { false }
    }

    private fun handleKycResponse(result: ApiResponse<KycStatusResponse>) {
        if (result is ApiSuccessResponse) {
            kycAccount = result.body
            paymentPrefManager.setKycStatus(kycAccount?.basicKyc?.status)
            paymentPrefManager.setKybStatus(kycAccount?.kybData?.status)
            kycAccount?.basicKyc?.status?.let {
                AppAnalytics.setUserProperty(AnalyticsConst.KYC_STATUS, it.toString())
            }
            kycAccount?.kybData?.status?.let {
                AppAnalytics.setUserProperty(AnalyticsConst.KYB_STATUS, it.toString())
            }
        }
    }

    private fun handleKycData() = viewModelScope.launch {
        var response: ApiResponse<KycStatusResponse>? = null
        if (sessionManager.kycAccountId == null) response = janusUseCase.createKycAccount()
        else {
            return@launch
        }
        when (response) {
            is ApiSuccessResponse -> {
                sessionManager.kycAccountId = response.body.accountId
                handleKycResponse(response)
            }
            else -> {}
        }
    }

    private fun showSmallBannerMessage(paymentMetadata: PaymentMetadata) {
        setState(State.SetPaymentInfoSaving(paymentMetadata))
    }

    fun onCreatePaymentClicked() {
        setState(State.GoToCreatePayment(paymentPrefManager.getLastPaymentType(), bookId))
    }

    fun getBook() = businessUseCase.getBusinessById(SessionManager.getInstance().businessId)

    private fun publishStateBusinessId() {
        val bookEntity = businessUseCase.getBusinessById(SessionManager.getInstance().businessId)
        bookId = bookEntity?.bookId
        setState(State.SetBookData(bookId, bookEntity?.businessName))
    }

    private fun getPaymentSummary() = launch {
        setViewState(currentViewState().copy(showLoading = list.isEmpty(), showEmptyState = false, showError = false))
        publishStateBusinessId()
        when (val result = finproUseCase.getPaymentSummary(SessionManager.getInstance().businessId)) {
            is ApiSuccessResponse -> {
                paymentCountAll = result.body.countAll.orNil
                paymentOutCount = (result.body.count?.paymentOut ?: 0.0).toInt()
                setState(State.SetSummaryData(result.body, bookId))
                getPaymentList()
            }
            else -> {
                setState(State.SetSummaryData(null, bookId))
                getPaymentList()
            }
        }
    }

    private suspend fun getPaymentList() {
        val ttlOrders = Firebase.performance.newTrace(AnalyticsConst.TTL_ORDERS)
        ttlOrders.start()
        val result = if (PaymentPrefManager.getInstance().getTransactionHistoryFeatureFlag()) {
            val limit = 10
            ttlOrders.apply {
                putAttribute(AnalyticsConst.API_SERVICE, AnalyticsConst.TRANSACTION_SERVICE)
                putAttribute(AnalyticsConst.LIMIT, limit.toString())
            }
            ordersUseCase.getOrders(
                accountId = SessionManager.getInstance().businessId, page = 0, limit = limit,
                type = listOf(PaymentConst.TYPE_DEFAULT)
            )
        } else {
            ttlOrders.apply {
                putAttribute(AnalyticsConst.API_SERVICE, AnalyticsConst.FINPRO_SERVICE)
            }
            finproUseCase.getOrders(bookId = SessionManager.getInstance().businessId, page = 1, limit = 10)
        }
        when (result) {
            is ApiSuccessResponse -> {
                hasLoaded = true
                setViewState(currentViewState().copy(showLoading = false))
                FeaturePrefManager.getInstance().setPosNotificationShown()
                list = result.body
                if (list.isNotEmpty()) {
                    setState(State.SetPaymentList(result.body))
                } else {
                    setViewState(currentViewState().copy(showLoading = false, showEmptyState = true))
                    // Note: Setting timer for finpro service and not for transaction service
                    if (!PaymentPrefManager.getInstance().getTransactionHistoryFeatureFlag()) {
                        emptyStateTimer()
                    }
                }
                ttlOrders.putAttribute(
                    AnalyticsConst.API_RESPONSE,
                    AnalyticsConst.STATUS_SUCCESS
                )
                ttlOrders.stop()
            }
            else -> {
                setViewState(currentViewState().copy(showLoading = false, showError = !hasLoaded))
                if (hasLoaded) {
                    setState(State.SetPaymentList(list))
                }
                ttlOrders.putAttribute(
                    AnalyticsConst.API_RESPONSE,
                    if (result is ApiEmptyResponse) AnalyticsConst.EMPTY else AnalyticsConst.STATUS_FAILURE
                )
                ttlOrders.stop()
            }
        }
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    private fun setSaldoState(state: State) {
        _saldoState.value = EventWrapper(state)
    }

    private fun setBnplState(state: State) {
        _bnplState.value = EventWrapper(state)
    }

    private suspend fun setViewState(vs: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = vs
    }

    //getting list of bank accounts to show the first bank on the tutorial
    fun checkBankAccounts() {
        getBankAccounts()
    }

    private fun getBankAccounts() = viewModelScope.launch {
        val bookId = SessionManager.getInstance().businessId
        if (!PaymentPrefManager.getInstance().getShowSelectedBankFromLocal()) {
            when (val result = paymentUseCase.getMerchantBankAccounts(bookId)) {
                is ApiSuccessResponse -> {
                    val size = result.body?.size ?: 0
                    FeaturePrefManager.getInstance().setHasBankAccount(size > 0, bookId)
                    PaymentPrefManager.getInstance().setShowSelectedBankFromLocal(true)
                    _selectedBankAccount.value = result.body
                }
                else -> {}
            }
        } else {
            _selectedBankAccount.value = paymentUseCase.getLocalMerchantBankAccounts(bookId)
        }
    }

    private fun emptyStateTimer() {
        setState(State.showEmptyStateInfo)
        handler = Handler()
        handler?.postDelayed(Runnable { kotlin.run {
            emptyStateTimer()
        } }, 5000)
    }

    fun stopHandlerForEmptyState() {
        handler?.removeCallbacksAndMessages(null)
    }

    private fun getBannerInfo() = viewModelScope.launch {
        when (val result = finproUseCase.getPaymentBannerInfo(sessionManager.businessId)) {
            is ApiSuccessResponse -> {
                setState(State.BannerList(result.body))
            }
            else -> {
                setState(State.BannerList(listOf()))
            }
        }
    }

    fun checkPpobAvailability(fragmentBodyBlock: BodyBlock?= null) {
        when(fragmentBodyBlock?.ppobCategoryName) {
            PpobConst.CATEGORY_REMINDER -> {
                checkReminderAvailability( fragmentBodyBlock.is_available.isTrue)
            }
            else -> {
                checkPpobConfig(fragmentBodyBlock)
            }

        }
    }

    fun getTransactionFeatureFlag() = viewModelScope.launch {
        val result = ordersUseCase.getFeatureFlag()
        when (result) {
            is ApiSuccessResponse -> {
                paymentPrefManager.setTransactionHistoryFeatureFlag(result.body.transactionHistoryService.isTrue)
            }
            else -> {
                paymentPrefManager.setTransactionHistoryFeatureFlag(false)
            }
        }
    }

    fun hideKybPendingBanner(): Boolean {
        val firstShown = paymentPrefManager.kybPendingBannerFirstShown()
        return if (firstShown == -1L) {
            false
        } else {
            (firstShown + DateTimeUtils.ONE_WEEK_DURATION_IN_MILLIS) < System.currentTimeMillis()
        }
    }

    fun hideAccountVerifiedBanner(): Boolean {
        val firstShown = paymentPrefManager.accountVerifiedBannerFirstShown()
        return if (firstShown == -1L) {
            false
        } else {
            (firstShown + DateTimeUtils.ONE_WEEK_DURATION_IN_MILLIS) < System.currentTimeMillis()
        }
    }

    fun fetchBnplInfo() = launch {
        if (SessionManager.getInstance().hasRefreshedBnplData() && SessionManager.getInstance().isBnplRegistered) {
            setBnplState(
                State.BnplInfo(
                    SessionManager.getInstance().isBnplRegistered,
                    SessionManager.getInstance().bnplLimit
                )
            )
            return@launch
        }
        when (val response = LosUseCase.getPpobBnplUserDetails()) {
            is ApiSuccessResponse -> {
                SessionManager.getInstance().setBnplInfo(response.body.data?.isRegisteredUser.isTrue, response.body.data?.currentLimit.orNil)
                SessionManager.getInstance().hasRefreshedBnplData(true)
                setBnplState(
                    State.BnplInfo(
                        response.body.data?.isRegisteredUser.isTrue,
                        response.body.data?.currentLimit.orNil
                    )
                )
            }
            else -> {
                setBnplState(State.BnplError)
            }
        }
    }

    fun fetchLinkedOrders(orderId: String, adapterPos: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val response = ordersUseCase.getLinkedOrders(orderId)) {
                is ApiSuccessResponse -> {
                    val linkedOrders = arrayListOf<OrderHistoryData>()
                    response.body.map {
                        linkedOrders.add(
                            OrderHistoryData(
                                orderData = it.apply { isLinkedOrder = true },
                                formattedDate = null,
                                timestamp = null,
                                viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                            )
                        )
                    }
                    linkedOrdersMap[orderId] = LinkedOrdersData(
                        linkedOrders = linkedOrders,
                        linkedOrdersVisibility = true,
                        linkedOrdersLoading = false
                    )
                    withContext(Dispatchers.Main) {
                        _event.value = Event.LinkedItemsFetched(adapterPos)
                    }
                }
                is ApiErrorResponse -> {
                    withContext(Dispatchers.Main) {
                        _event.value = Event.ApiError(response.errorMessage, adapterPos)
                    }
                }
                else -> {}
            }
        }
    }
}
