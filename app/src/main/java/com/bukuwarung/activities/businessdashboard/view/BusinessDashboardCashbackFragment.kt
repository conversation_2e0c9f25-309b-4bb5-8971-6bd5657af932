package com.bukuwarung.activities.businessdashboard.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessDashboardCashbackAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.LayoutBusinessDashboardCashbackBinding
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.session.User
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessDashboardCashbackFragment : BaseFragment(),
    BusinessDashboardCashbackAdapter.BusinessDashboardCashbackListener {

    private lateinit var binding: LayoutBusinessDashboardCashbackBinding
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels()
    private lateinit var businessDashboardCashbackAdapter: BusinessDashboardCashbackAdapter
    private var disbursableType: String? = null
    private var totalCashbackAmount: Double? = null

    companion object {
        const val TOP_CARD = 0
        const val DATA_ITEM = 1
        fun createIntent(
            disbursableType: String,
            totalCashbackAmount: Double
        ): BusinessDashboardCashbackFragment {
            val fragment = BusinessDashboardCashbackFragment()
            fragment.arguments = Bundle().apply {
                putString("disbursableType", disbursableType)
                putDouble("totalCashbackAmount", totalCashbackAmount)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = LayoutBusinessDashboardCashbackBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        disbursableType = arguments?.getString("disbursableType")
        totalCashbackAmount = arguments?.getDouble("totalCashbackAmount")
        viewModel.getCashbackList(
            User.getUserId(),
            User.getBusinessId(),
            (activity as BusinessDashboardMainActivity).getMonthStartDate(),
            (activity as BusinessDashboardMainActivity).getMonthEndDate(),
            disbursableType
        )
        businessDashboardCashbackAdapter =
            BusinessDashboardCashbackAdapter(this, totalCashbackAmount)
        binding.rvCashback.apply {
            User.getBusinessId()
            layoutManager = LinearLayoutManager(context)
            adapter = businessDashboardCashbackAdapter
        }
        binding.backBtn.setOnClickListener {
            activity?.onBackPressed()
        }
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            if (it is BusinessDashboardMainViewModel.State.UpdateCashbackList) {
                businessDashboardCashbackAdapter.setData(viewModel.cashbackList)
                with(binding) {
                    rvCashback.showView()
                    ivNoCashback.hideView()
                    tvNoCashbackTitle.hideView()
                    tvNoCashbackSubtitle.hideView()
                }
                if (viewModel.pagecount > 0 && it.loadMoreData) {
                    viewModel.getCashbackList(
                        User.getUserId(),
                        User.getBusinessId(),
                        (activity as BusinessDashboardMainActivity).getMonthStartDate(),
                        (activity as BusinessDashboardMainActivity).getMonthEndDate(),
                        disbursableType
                    )
                }
            }
            if (it is BusinessDashboardMainViewModel.State.ShowNoCashbackPage) {
                with(binding) {
                    rvCashback.hideView()
                    ivNoCashback.showView()
                    tvNoCashbackTitle.showView()
                    tvNoCashbackSubtitle.showView()
                }
            }
        }
    }

    override fun goToPaymentHistoryDetailActivity(
        paymentType: String?,
        orderId: String?,
        context: Context
    ) {
        triggerTransactionClickedAnalyticsEvent()
        startActivity(
            PaymentHistoryDetailsActivity.createIntent(
                context = requireContext(),
                orderId = orderId,
                paymentType = paymentType
            )
        )
    }

    private fun triggerTransactionClickedAnalyticsEvent() {
        val prop = AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.BD_PAYMENT)

        AppAnalytics.trackEvent(AnalyticsConst.PAYMENT_DETAIL_VISIT, prop)
    }

}