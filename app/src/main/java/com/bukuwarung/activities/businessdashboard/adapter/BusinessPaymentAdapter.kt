package com.bukuwarung.activities.businessdashboard.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.CategoriesItem
import com.bukuwarung.databinding.ItemBusinessDashboardPaymentHistoryBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView

class BusinessPaymentAdapter(val list: List<CategoriesItem>, val isFromBottomSheet: <PERSON>olean, val tagString: String): RecyclerView.Adapter<BusinessPaymentAdapter.BusinessPaymentViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BusinessPaymentAdapter.BusinessPaymentViewHolder {
        val binding = ItemBusinessDashboardPaymentHistoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return BusinessPaymentViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return if (isFromBottomSheet) {
            list.size
        } else {
            return if (list.size >= 3) 3 else list.size
        }
    }

    inner class BusinessPaymentViewHolder constructor(private val binding: ItemBusinessDashboardPaymentHistoryBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: CategoriesItem, position: Int) {
            with(binding) {
                tvItemTitle.text = item.name

                item.count?.let {
                    tvCount.text =
                        itemView.context.getString(R.string.total_transactions, it.toString())
                } ?: kotlin.run {
                    tvCount.text = itemView.context.getString(R.string.total_transactions, "0")
                }
                item.amount?.let {
                    tvAmount.text = Utility.formatAmount(it)
                } ?: kotlin.run {
                    tvAmount.text = itemView.context.getString(R.string.currency).plus("0")
                }
                if (item.mainCategory.equals("Keperluan Usaha")) {
                    labelBusinessPersonal.background = ContextCompat.getDrawable(
                        root.context, R.drawable.label_bisnis
                    )
                    labelBusinessPersonal.text = "Bisnis"
                    labelBusinessPersonal.setTextColor(
                        ContextCompat.getColor(
                            root.context,
                            R.color.blue_60
                        )
                    )
                } else {
                    labelBusinessPersonal.background = ContextCompat.getDrawable(
                        root.context, R.drawable.label_pribadi
                    )
                    labelBusinessPersonal.setTextColor(
                        ContextCompat.getColor(
                            root.context,
                            R.color.green_80
                        )
                    )
                    labelBusinessPersonal.text = "Pribadi"
                }
            }
            if (position == list.size -1 && !isFromBottomSheet) {
                binding.vwDivider.hideView()
            } else {
                binding.vwDivider.showView()
            }
        }
    }

    override fun onBindViewHolder(holder: BusinessPaymentViewHolder, position: Int) {
        holder.bind(list[position], position)
    }
}