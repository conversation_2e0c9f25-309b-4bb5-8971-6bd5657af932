package com.bukuwarung.activities.businessdashboard.view

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.text.TextUtils
import androidx.activity.viewModels
import androidx.fragment.app.FragmentTransaction
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardItems
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardAutoRecordSummary.BusinessDashboardTransactionsCardFragment
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AnalyticsConst.INVENTORY
import com.bukuwarung.constants.AnalyticsConst.PAYMENT
import com.bukuwarung.constants.AnalyticsConst.PPOB
import com.bukuwarung.constants.AnalyticsConst.TRANSACTION
import com.bukuwarung.constants.AnalyticsConst.TRANSACTION_CARD
import com.bukuwarung.constants.AnalyticsConst.UTANG
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityProductDashboardMainBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getClassTag
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.roundTo
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.github.dewinjm.monthyearpicker.MonthYearPickerDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import java.util.Calendar
import kotlin.math.abs

@AndroidEntryPoint
class BusinessDashboardMainActivity : BaseActivity() {

    private val viewModel: BusinessDashboardMainViewModel by viewModels()

    private lateinit var binding: ActivityProductDashboardMainBinding
    private lateinit var businessDashboardTransaksiFragment: BusinessDashboardTransaksiFragment
    private lateinit var businessDashboardStockFragment: BusinessDashboardStockFragment
    private lateinit var businessDashboardUtangFragment: BusinessDashboardUtangFragment
    private lateinit var businessDashboardInfoFragment: BusinessDashboardInfoFragment
    private lateinit var businessDashboardCategoryFragment: BusinessDashboardCategoryFragment
    private lateinit var businessDashboardTipsEmptyFragment: BusinessDashboardTipsEmptyFragment
    private lateinit var businessDashboardTransactionsCardFragment: BusinessDashboardTransactionsCardFragment
    private lateinit var businessDashboardPpobProducts: BusinessDashboardPpobProducts
    private lateinit var businessDashboardPaymentSectionFragment: BusinessDashboardPaymentSectionFragment
    private var month: Int = 0
    private var year: Int = 2022
    private var monthEndDate: Int = 31
    private var listener: OnSharedPreferenceChangeListener? = null
    private var firstCard: String = ""
    private val delay: Long = 1000



    companion object {
        fun createIntent(origin: Context?, ppobCount: Double?, paymentCount: Double?): Intent {
            val intent = Intent(origin, BusinessDashboardMainActivity::class.java)
            intent.putExtra(BusinessDashboardWelcomeActivity.PPOB_COUNT, ppobCount)
            intent.putExtra(BusinessDashboardWelcomeActivity.PAYMENT_COUNT, paymentCount)
            return intent
        }
        const val IS_FIRST_CARD = "is_first_card"
    }


    override fun setViewBinding() {
        binding = ActivityProductDashboardMainBinding.inflate(layoutInflater)
    }

    @SuppressLint("StringFormatMatches")
    override fun setupView() {
        viewModel.ppobCount = intent.getDoubleExtra(BusinessDashboardWelcomeActivity.PPOB_COUNT, 0.0)
        viewModel.paymentCount = intent.getDoubleExtra(BusinessDashboardWelcomeActivity.PAYMENT_COUNT, 0.0)
        setContentView(binding.root)
        businessDashboardInfoFragment = BusinessDashboardInfoFragment.createIntent()
        businessDashboardTipsEmptyFragment = BusinessDashboardTipsEmptyFragment.createIntent()
        businessDashboardCategoryFragment = BusinessDashboardCategoryFragment.createIntent()
        month = Calendar.getInstance().get(Calendar.MONTH)
        year = Calendar.getInstance().get(Calendar.YEAR)
        monthEndDate = DateTimeUtils.getCurrentMonthLastDay()
        binding.chipTransactionGroupByDate.text =
            DateTimeUtils.getShortMonthFromNumber(month) + " " + year
        DateTimeUtils.getShortMonthFromNumber(month) + " " + year

        binding.backBtn.setOnClickListener { onBackPressed() }
        val monthValue = DateTimeUtils.getShortMonthFromNumber(
            Calendar.getInstance().get(Calendar.MONTH)
        ) + " " + Calendar.getInstance().get(Calendar.YEAR)


        setErsProps()
        val yearMonthString = getString(
            R.string.mantap_kamu_sudah_melihat_rekap_bisnis_kamu_untuk_bulan_meiember_2021, monthValue,
            ""
        )

        binding.tvBottomDate.text = yearMonthString

        val cashInAmount = CashRepository.getInstance(this)
            .getCashInByDateRange(User.getBusinessId(), getMonthStartDate(), getMonthEndDate())
        val cashOutAmount = CashRepository.getInstance(this)
            .getCashOutByDateRange(User.getBusinessId(), getMonthStartDate(), getMonthEndDate())
        val lastMonthCashInAmount = CashRepository.getInstance(this).getCashInByDateRange(
            User.getBusinessId(),
            getLastMonthStartDate(),
            getLastMonthEndDate()
        )
        val lastMonthCashOutAmount = CashRepository.getInstance(this).getCashOutByDateRange(
            User.getBusinessId(),
            getLastMonthStartDate(),
            getLastMonthEndDate()
        )
        val balanceAmount = abs(cashInAmount) - abs(cashOutAmount)
        val lastMonthBalanceAmount =
            abs(lastMonthCashInAmount) - abs(lastMonthCashOutAmount)
        binding.totalAmtTxt.text = Utility.formatAmount(balanceAmount)
        if (balanceAmount < 0) {
            binding.totalAmtTxt.text = "-" + Utility.formatAmount(balanceAmount)
        } else {
            binding.totalAmtTxt.text = Utility.formatAmount(balanceAmount)
        }
        if (lastMonthBalanceAmount.equals(0.0)) {
            binding.subHeadingTxt.hideView()
        } else {
            binding.subHeadingTxt.hideView()
            val changeAmount = (balanceAmount - lastMonthBalanceAmount)
            val percentageIncrease = ((changeAmount / lastMonthBalanceAmount) * 100).roundTo()
            if (changeAmount <= 0) {
//                binding.subHeadingTxt.text =
//                    "Lebih banyak -" + Utility.formatAmount(changeAmount) + "(" + percentageIncrease + "%) dari bulan lalu"
                binding.subHeadingTxt.hideView()
            } else {
                binding.subHeadingTxt.hideView()
                binding.subHeadingTxt.text =
                    "Lebih banyak " + Utility.formatAmount(changeAmount) + "(" + percentageIncrease + "%) dari bulan lalu"
            }
        }
        binding.chipTransactionGroupByDate.setSingleClickListener { datePicker() }
        val fragmentManager = supportFragmentManager
        var transaction: FragmentTransaction?

        val businessDashboardHeader = binding.llBusinessDashboardHead

        if (businessDashboardHeader.childCount > 0) {
            businessDashboardHeader.removeAllViews()
        }

        if (RemoteConfigUtils.BusinessDashBoard.downloadBusinessDashBoard()) {
            binding.btnDownload.showView()
        } else {
            binding.btnDownload.hideView()
        }

        binding.btnDownload.setSingleClickListener {

            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
                requestPermissions(
                    PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                    PermissionConst.WRITE_EXTERNAL_STORAGE
                )
            } else {
                downloadBusinessDashboard()
            }
        }
        val businessDashboardItems = arrayListOf<BusinessDashboardItems>()
        if ((viewModel.ppobCount>= 0 && viewModel.paymentCount > 0)) {
            businessDashboardItems.add(BusinessDashboardItems.PAYMENT)
            businessDashboardItems.add(BusinessDashboardItems.PPOB)
            businessDashboardItems.add(BusinessDashboardItems.TRANSACTION_CARD)
            businessDashboardItems.add(BusinessDashboardItems.INVENTORY)
            businessDashboardItems.add(BusinessDashboardItems.BRICK_AUTORECORD)
            businessDashboardItems.add(BusinessDashboardItems.UTANG)
        } else if (viewModel.ppobCount> 0 && viewModel.paymentCount == 0.0) {
            businessDashboardItems.add(BusinessDashboardItems.PPOB)
            businessDashboardItems.add(BusinessDashboardItems.PAYMENT)
            businessDashboardItems.add(BusinessDashboardItems.TRANSACTION_CARD)
            businessDashboardItems.add(BusinessDashboardItems.INVENTORY)
            businessDashboardItems.add(BusinessDashboardItems.BRICK_AUTORECORD)
            businessDashboardItems.add(BusinessDashboardItems.UTANG)
        } else {
            businessDashboardItems.add(BusinessDashboardItems.TRANSACTION_CARD)
            businessDashboardItems.add(BusinessDashboardItems.INVENTORY)
            businessDashboardItems.add(BusinessDashboardItems.UTANG)
            businessDashboardItems.add(BusinessDashboardItems.PAYMENT)
            businessDashboardItems.add(BusinessDashboardItems.PPOB)
            businessDashboardItems.add(BusinessDashboardItems.BRICK_AUTORECORD)
        }
        binding.apply {
            fragmentManager.also {

                val hideTips = showTipsSection()
                val newUserTips = showTipsNewUser()
                var position = 0;
                for (businessDashboardItem in businessDashboardItems) {
                    when (businessDashboardItem) {
                        BusinessDashboardItems.ACCOUNTING_TRANSACTION -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isTransactionCardEnabled()) {
                                businessDashboardTransaksiFragment = if (position == 0) {
                                    firstCard = TRANSACTION
                                    BusinessDashboardTransaksiFragment.createIntent(true)
                                } else {
                                    BusinessDashboardTransaksiFragment.createIntent(false)
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardTransaksiFragment
                                )?.commitAllowingStateLoss()
                            }
                        }
                        BusinessDashboardItems.INVENTORY -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isProductCardEnabled() && newUserTips) {
                                businessDashboardStockFragment = if (position == 0) {
                                    firstCard = INVENTORY
                                    BusinessDashboardStockFragment.createIntent(true)
                                } else {
                                    BusinessDashboardStockFragment.createIntent(false)
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardStockFragment
                                )?.commitAllowingStateLoss()
                            }
                        }
                        BusinessDashboardItems.UTANG -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isUtangCardEnabled() && newUserTips) {
                                businessDashboardUtangFragment = if (position == 0) {
                                    firstCard = UTANG
                                    BusinessDashboardUtangFragment.createIntent(true)
                                } else {
                                    BusinessDashboardUtangFragment.createIntent(false)
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardUtangFragment
                                )?.commitAllowingStateLoss()
                            }
                        }
                        BusinessDashboardItems.PAYMENT -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isPaymentSectionEnabled() && newUserTips) {
                                businessDashboardPaymentSectionFragment = if (position == 0) {
                                    firstCard = PAYMENT
                                    BusinessDashboardPaymentSectionFragment.createIntent(true)
                                } else {
                                    BusinessDashboardPaymentSectionFragment.createIntent(false)
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardPaymentSectionFragment
                                )?.commitAllowingStateLoss()
                            }
                        }
                        BusinessDashboardItems.PPOB -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isPpobCardEnabled() && newUserTips) {
                                businessDashboardPpobProducts = if (position == 0) {
                                    firstCard = PPOB
                                    BusinessDashboardPpobProducts.createIntent(true)
                                } else {
                                    BusinessDashboardPpobProducts.createIntent(false)
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardPpobProducts
                                )?.commitAllowingStateLoss()
                            }
                        }
                        BusinessDashboardItems.TRANSACTION_CARD -> {
                            if (RemoteConfigUtils.BusinessDashBoard.isTransactionCategoryCardEnabled() && newUserTips) {
                                businessDashboardTransactionsCardFragment = if (position == 0) {
                                    firstCard = TRANSACTION_CARD
                                    BusinessDashboardTransactionsCardFragment.instance(
                                        position = 0,
                                        isFirstCard = true
                                    ) {
                                        super.onBackPressed()
                                    }
                                } else {
                                    BusinessDashboardTransactionsCardFragment.instance(
                                        position = 0,
                                        isFirstCard = false
                                    ) {
                                        super.onBackPressed()
                                    }
                                }
                                transaction = it.beginTransaction()
                                transaction?.add(
                                    businessDashboardHeader.id,
                                    businessDashboardTransactionsCardFragment,
                                    BusinessDashboardTransactionsCardFragment.getClassTag()
                                )?.commitAllowingStateLoss()
                            }
                        }
                        else -> {}
                    }
                    position++
                }


                if (hideTips) {
                    transaction = it.beginTransaction()
                    transaction?.add(businessDashboardHeader.id, businessDashboardTipsEmptyFragment)
                        ?.commitAllowingStateLoss()
                }


                val hideCategory =
                    TransactionRepository.getInstance(this@BusinessDashboardMainActivity).cashTransactionCountWithDeletedRecords == 0
                if (hideCategory) {
                    transaction = it.beginTransaction()
                    transaction?.add(businessDashboardHeader.id, businessDashboardCategoryFragment)
                        ?.commitAllowingStateLoss()
                }
                transaction = it.beginTransaction()
                transaction?.add(businessDashboardHeader.id, businessDashboardInfoFragment)
                    ?.commitAllowingStateLoss()
            }
        }

        listener =
            OnSharedPreferenceChangeListener { prefs, key -> // your stuff
                if (!TextUtils.isEmpty(key) && key == FeaturePrefManager.BRICK_INTEGRATED_FIRST_TIME) {
                    setupView()
                }
            }

        FeaturePrefManager.getInstance().addListener(listener)

    }

    override fun onDestroy() {
        super.onDestroy()
        FeaturePrefManager.getInstance().removeListener(listener)
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    setErsProps(it.startDate, it.endDate)
                }
                else -> {}
            }
        }
    }

    fun getMonthStartDate(): String {
        val monthValue = month + 1
        return if (monthValue < 10) {
            "$year-0$monthValue-01"
        } else {
            "$year-$monthValue-01"
        }
    }

    fun getMonthEndDate(): String {
        val monthValue = month + 1
        monthEndDate = DateTimeUtils.getLastDayOfMonth(getMonthStartDate())
        return if (monthValue < 10) {
            "$year-0$monthValue-$monthEndDate"
        } else {
            "$year-$monthValue-$monthEndDate"
        }
    }

    private fun getLastMonthStartDate(): String {
        val monthValue = month
        return if (monthValue == 0) {
            (year - 1).toString() + "-12" + "-01"
        } else {
            if (monthValue < 10) {
                "$year-0$monthValue-01"
            } else {
                "$year-$monthValue-01"
            }
        }


    }

    private fun getLastMonthEndDate(): String {
        val monthValue = month
        return if (monthValue == 0) {
            (year - 1).toString() + "-12" + "-" + monthEndDate
        } else {
            if (monthValue < 10) {
                "$year-0$monthValue-$monthEndDate"
            } else {
                "$year-$monthValue-$monthEndDate"
            }
        }
    }

    fun datePicker() {
        var yearSelected: Int
        var monthSelected: Int

        val calendar = Calendar.getInstance()
        yearSelected = calendar[Calendar.YEAR]
        monthSelected = calendar[Calendar.MONTH]

        val dialogFragment = MonthYearPickerDialogFragment
            .getInstance(monthSelected, yearSelected, "Pilih bulan")

        dialogFragment.setOnDateSetListener { yearSelected, monthSelected ->
            month = monthSelected
            year = yearSelected

            val calendarString = DateTimeUtils.getShortMonthFromNumber(monthSelected) + " $yearSelected"

            binding.chipTransactionGroupByDate.text =
                calendarString

            binding.tvBottomDate.text = getString(
                R.string.mantap_kamu_sudah_melihat_rekap_bisnis_kamu_untuk_bulan_meiember_2021, calendarString
            )


            val cashInAmount = CashRepository.getInstance(this)
                .getCashInByDateRange(User.getBusinessId(), getMonthStartDate(), getMonthEndDate())
            val cashOutAmount = CashRepository.getInstance(this)
                .getCashOutByDateRange(User.getBusinessId(), getMonthStartDate(), getMonthEndDate())
            val lastMonthCashInAmount = CashRepository.getInstance(this).getCashInByDateRange(
                User.getBusinessId(),
                getLastMonthStartDate(),
                getLastMonthEndDate()
            )
            val lastMonthCashOutAmount = CashRepository.getInstance(this).getCashOutByDateRange(
                User.getBusinessId(),
                getLastMonthStartDate(),
                getLastMonthEndDate()
            )
            val balanceAmount = Math.abs(cashInAmount) - Math.abs(cashOutAmount)
            val lastMonthBalanceAmount =
                Math.abs(lastMonthCashInAmount) - Math.abs(lastMonthCashOutAmount)
            binding.totalAmtTxt.text = Utility.formatAmount(balanceAmount);
            if (balanceAmount < 0) {
                binding.totalAmtTxt.text = "-" + Utility.formatAmount(balanceAmount);
            } else {
                binding.totalAmtTxt.text = Utility.formatAmount(balanceAmount);
            }
            if (lastMonthBalanceAmount.equals(0.0)) {
                binding.subHeadingTxt.hideView()
            } else {
                val changeAmount = (balanceAmount - lastMonthBalanceAmount);
                val percentageIncrease = ((changeAmount / lastMonthBalanceAmount) * 100).roundTo()
                if (changeAmount == 0.0) {
                    binding.subHeadingTxt.hideView()
                } else {
                    binding.subHeadingTxt.hideView()
                }
                if (changeAmount <= 0) {
//                    binding.subHeadingTxt.text =
//                        "Lebih banyak -" + Utility.formatAmount(changeAmount) + "(" + percentageIncrease + "%) dari bulan lalu"
                    binding.subHeadingTxt.hideView()
                } else {
                    binding.subHeadingTxt.hideView()
                    binding.subHeadingTxt.text =
                        "Lebih banyak " + Utility.formatAmount(changeAmount) + "(" + percentageIncrease + "%) dari bulan lalu"
                }
            }


            viewModel.onEventReceived(
                BusinessDashboardMainViewModel.Event.OnMonthChange(
                    getMonthStartDate(),
                    getMonthEndDate()
                )
            )
        }

        dialogFragment.show(supportFragmentManager, null)

    }

    private fun downloadBusinessDashboard() {
        ImageUtils.saveLayoutConvertedImageLongView(
            binding.svHomeDashboard,
            binding.svHomeDashboard.getChildAt(0).height,
            binding.svHomeDashboard.getChildAt(0).width, true
        )

        val downloadDirectory = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            "business_dashboard.png"
        )

        val path = Uri.fromFile(downloadDirectory)
        val imageOpenIntent = Intent(Intent.ACTION_VIEW)
        imageOpenIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        imageOpenIntent.setDataAndType(path, "image/*")
        try {
            this.startActivity(imageOpenIntent)
        } catch (e: ActivityNotFoundException) {
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    downloadBusinessDashboard()
                }
            }
        }
    }

    private fun showTipsNewUser(): Boolean {
        val categoryId = CashRepository.getInstance(this).getCategoryIdByName(User.getBusinessId(), "Penambahan Modal");

        val hideModal =
            TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords == 0

        val hideUtang =
            TransactionRepository.getInstance(this).utangTransactionCountWithDeletedRecords == 0
        val hideBrick = !FeaturePrefManager.getInstance().isBrickIntegratedAtleastOnce && RemoteConfigUtils.shouldShowAutoRecordFeature()


        val hidePpobCard = FeaturePrefManager.getInstance().businessDashboardPpobProductShown
        val hidePaymentCard = FeaturePrefManager.getInstance().businessDashboardPayment

        if (hideBrick && hideModal && hideUtang && hidePpobCard && hidePaymentCard) {
            return false
        }
        return true
    }

    private fun showTipsSection(): Boolean {
        val categoryId = CashRepository.getInstance(this).getCategoryIdByName(User.getBusinessId(), "Penambahan Modal");

        val hideModal =
            TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords == 0

        val hideUtang =
            TransactionRepository.getInstance(this).utangTransactionCountWithDeletedRecords == 0
        val hideBrick = !FeaturePrefManager.getInstance().isBrickIntegratedAtleastOnce && RemoteConfigUtils.shouldShowAutoRecordFeature()


        val hidePpobCard = FeaturePrefManager.getInstance().businessDashboardPpobProductShown
        val hidePaymentCard = FeaturePrefManager.getInstance().businessDashboardPayment
        if (hideBrick || hideModal || hideUtang || hidePpobCard || hidePaymentCard) {
            return false
        }
        return false
    }

    private fun setErsProps(startDate: String = "", endDate: String = "") {

    }


}