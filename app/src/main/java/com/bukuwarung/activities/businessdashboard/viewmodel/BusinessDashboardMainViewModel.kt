package com.bukuwarung.activities.businessdashboard.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCashbackResponseItem
import com.bukuwarung.activities.businessdashboard.model.CategoriesItem
import com.bukuwarung.activities.businessdashboard.model.PaymentCategoriesHistoryResponse
import com.bukuwarung.activities.businessdashboard.model.PersonalPpobProduct
import com.bukuwarung.activities.businessdashboard.model.PpobProducts
import com.bukuwarung.activities.businessdashboard.model.SalesPpobProduct
import com.bukuwarung.activities.businessdashboard.model.TotalPayment
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.filter.CashTabFilter
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.transactionreport.DateRange
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.dto.FrequentProductDto
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.los.LosUseCase
import com.bukuwarung.payments.data.model.PaymentCategory
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.wrapper.EventWrapper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject


/*
    This would be common viewmodel, which would be used to communicate between different fragments.
    In this case, change of date need to reflect data changes in all the fragments below
 */
@HiltViewModel
class BusinessDashboardMainViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val losUseCase: LosUseCase,
    private val bankingUseCase: BankingUseCase,
    private val finproUseCase: FinproUseCase
) : BaseViewModel() {

    var pagecount = 0
    var cashbackList = arrayListOf<BusinessDashboardCashbackResponseItem>()
    var ppobCount = 0.0
    var paymentCount = 0.0

    private val _state = MutableLiveData<EventWrapper<State>>()
    private val _monthState = MutableLiveData<EventWrapper<State>>()
    private val _autoRecordState = MutableLiveData<EventWrapper<State>>()
    private val _state_transaksi = MutableLiveData<EventWrapper<State>>()
    private val _state_no_cash_transaksi = MutableLiveData<EventWrapper<State>>()
    private val _state_bottomsheet_credit = MutableLiveData<EventWrapper<State>>()
    private val _state_bottomsheet_debit = MutableLiveData<EventWrapper<State>>()
    private val _state_ppob_products = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> get() = _state
    val monthState: LiveData<EventWrapper<State>> get() = _monthState
    val autoRecordstate: LiveData<EventWrapper<State>> get() = _autoRecordState
    val state_transaksi: LiveData<EventWrapper<State>> get() = _state_transaksi
    val state_no_cash_transaksi: LiveData<EventWrapper<State>> get() = _state_no_cash_transaksi
    val state_bottomsheet_credit: LiveData<EventWrapper<State>> get() = _state_bottomsheet_credit
    val state_bottomsheet_debit: LiveData<EventWrapper<State>> get() = _state_bottomsheet_debit
    val state_ppob_products: LiveData<EventWrapper<State>> get() = _state_ppob_products


    var transactionCount: Int = 0

    private var cashListViewModel: CashListViewModel? = null

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    private fun setMonthState(state: State) {
        _monthState.value = EventWrapper(state)
    }

    private fun setAutoRecordState(state: State) {
        _autoRecordState.value = EventWrapper(state)
    }

    private fun setTransaksiState(state: State) {
        _state_transaksi.value = EventWrapper(state)
    }

    private fun setNoCashTransaksiState(state: State) {
        _state_no_cash_transaksi.value = EventWrapper(state)
    }

    private fun setBottomSheetCreditState(state: State) {
        _state_bottomsheet_credit.value = EventWrapper(state)
    }

    private fun setBottomSheetDebitState(state: State) {
        _state_bottomsheet_debit.value = EventWrapper(state)
    }

    private fun setPPOBProduct(state: State) {
        _state_ppob_products.value = EventWrapper(state)
    }

    sealed class State {
        data class IsProductDashboardWelcomeSeen(val isShown: Boolean) : State()
        object ProceedToDashBoard : State()
        object OnBackPressed : State()
        data class GetPpobAndPaymentCount(val paymentCount: Double, val ppobCount: Double) : State()
        data class OnMonthChange(val startDate: String, val endDate: String) : State()
        data class PopulateTransaksiRecords(val transksiRecords: List<DataHolder>) : State()
        data class PopulateAutoRecordTransaksiRecords(val transksiRecords: List<DataHolder>) : State()
        data class PopulateNoCashTransaksiRecords(val transksiRecords: List<DataHolder>) : State()
        data class PopulateNoAutoRecordTransaksiRecords(val transksiRecords: List<DataHolder>) : State()
        data class PopulateBottomSheetPemasukanTransaksiRecords(val transaksiRecords: List<DataHolder>) : State()
        data class PopulateBottomSheetAutoRecordCreditTransaksiRecords(val transaksiRecords: List<DataHolder>) : State()
        data class PopulateBottomSheetPengeluaranTransaksiRecords(val transaksiRecords: List<DataHolder>) : State()
        data class PopulateBottomSheetAutoRecordDebitTransaksiRecords(val transaksiRecords: List<DataHolder>) : State()
        data class ShowNoOfTransactions(val transactionCount: Int) : State()
        data class ShowNoOfAutoRecordTransactions(val transactionCount: Int) : State()
        data class SetPpobSalesProducts(val products: SalesPpobProduct?) : State()
        data class SetPpobPersonalProducts(val products: PersonalPpobProduct?) : State()
        data class SetPpobProducts(val products: PpobProducts?) : State()
        data class SetInventoryData(
            val products: List<FrequentProductDto?>?,
            val bestSellingCategory: ProductCategoryEntity?,
            val totalTransactionsWithProducts: Int,
            val totalPenjualan: Double,
            val totalModal: Double,
            val runningOutProducts: List<ProductEntity>,
            val productsInStock: List<ProductEntity>
        ) : State()
        object HidePpobProducts : State()
        data class Error(val errorMessage: String, val status: Int) : State()
        data class GetPaymentCategoriesHistory(val paymentCategoriesHistoryResponse: PaymentCategoriesHistoryResponse) : State()
        data class HidePaymentCard(val paymentCategoriesHistoryResponse: ApiResponse<PaymentCategoriesHistoryResponse>) : State()
        data class ShowCategoryList(val list: List<PaymentCategory>) : State()
        data class UpdateCashbackList(val loadMoreData: Boolean): State()
        object ShowNoCashbackPage: State()
        data class ShowTotalPayment(val totalPayment: TotalPayment, val isPaymentReq: Boolean) : State()
        class SetBrickAccountsInProgress : State()
    }

    sealed class Event {
        object CheckFirstTimeEntry : Event()
        object OnProceedClicked : Event()
        object OnBackPressed : Event()
        object GetPPOBSalesPrefetchedRecord : Event()
        object GetPPOBPersonalPrefetchedRecord : Event()
        data class OnMonthChange(val startDate: String, val endDate: String) : Event()
        data class FetchTransaksiRecords(
            val cashListViewModel: CashListViewModel,
            val startDate: String, val endDate: String
        ) : Event()

        data class FetchPpobProducts(val account_id: String, val startDate: String, val endDate: String, val isPersonal: Boolean = false) : Event()

        data class FetchInventoryData( val cashListViewModel: CashListViewModel, val startDate: String, val endDate: String) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.CheckFirstTimeEntry -> checkIfFirstTimeUser()
            is Event.OnProceedClicked -> proceedToDashboard()
            is Event.OnBackPressed -> onBackPressed()
            is Event.GetPPOBPersonalPrefetchedRecord -> getPPOBPersonalPrefetchedRecord()
            is Event.GetPPOBSalesPrefetchedRecord -> getPPOBSalesPrefetchedRecord()
            is Event.OnMonthChange -> onMonthChange(event.startDate, event.endDate)
            is Event.FetchTransaksiRecords -> fetchTransaksiRecords(event.cashListViewModel, event.startDate, event.endDate)
            is Event.FetchPpobProducts -> fetchPpobProducts(event.account_id, event.startDate, event.endDate, event.isPersonal)
            is Event.FetchInventoryData -> fetchInventoryData(event.cashListViewModel, event.startDate, event.endDate)
        }
    }

    private fun fetchInventoryData(cashListViewModel: CashListViewModel, startDate: String, endDate: String) {
        this.cashListViewModel = cashListViewModel

        val bestSellingProduct = cashListViewModel.productInventory.getBestsellingProduct(startDate,endDate)
        val bestSellingCategory = if(cashListViewModel.productInventory.getBestSellingCategory(startDate,endDate)!=null) cashListViewModel.productInventory.getBestSellingCategory(startDate,endDate) else null
        val totalTransactionsWithProducts = cashListViewModel.productInventory.getTotalTransactionsWithProducts(startDate,endDate)
        val totalPenjualan = cashListViewModel.productInventory.getTotalSellingPriceForAllCategoriesWithProduct(startDate,endDate)
        val totalModal = cashListViewModel.productInventory.getTotalBuyingPriceForAllCategoriesWithProduct(startDate,endDate)
        val runningOutProducts : List<ProductEntity> =
            cashListViewModel.productInventory.getAllRunningOutProductsList()
        val productsInStock : List<ProductEntity> =
            cashListViewModel.productInventory.getProductsInStock()

        setState(State.SetInventoryData(bestSellingProduct,bestSellingCategory,totalTransactionsWithProducts,totalPenjualan,totalModal,runningOutProducts,productsInStock))
    }

    private fun getPPOBSalesPrefetchedRecord() {
        val data = state_ppob_products.value?.peekContent() as State.SetPpobProducts
        setState(State.SetPpobSalesProducts(data.products?.sales))
    }

    private fun getPPOBPersonalPrefetchedRecord() {
        val data = state_ppob_products.value?.peekContent() as State.SetPpobProducts
        setState(State.SetPpobPersonalProducts(data.products?.personal))
    }

    private fun checkIfFirstTimeUser() {
        setState(
            State.IsProductDashboardWelcomeSeen(
                FeaturePrefManager.getInstance().isProductDashboardWelcomeScreenSeen
            )
        )
        FeaturePrefManager.getInstance().setProductDashboardWelcomeScreenSeen()
    }

    private fun fetchPpobProducts(accountId: String, startDate: String, endDate: String, isPersonal: Boolean) = launch {
        when (val response = paymentUseCase.fetchPpobProducts(accountId, startDate, endDate)) {
            is ApiSuccessResponse -> handlePpobResult(response.body, isPersonal)
            is ApiErrorResponse -> {
                setState(State.HidePpobProducts)
            }
            else -> {}
        }
    }

    private fun handlePpobResult(result: PpobProducts?, isPersonal: Boolean) {
        result?.let {
            setPPOBProduct(State.SetPpobProducts(result))
            if (isPersonal) {
                setState(State.SetPpobPersonalProducts(result.personal))
            } else {
                setState(State.SetPpobSalesProducts(result.sales))
            }

        }
    }

    private fun proceedToDashboard() {
        setState(State.ProceedToDashBoard)
    }

    private fun fetchTransaksiRecords(cashListViewModel: CashListViewModel, startDate: String, endDate: String) {
        this.cashListViewModel = cashListViewModel
        cashListViewModel.setTypeFilter(CashTabFilter.BY_CATEGORY)
        val data = cashListViewModel.dataForBusinessDashBoard(DateRange(startDate, endDate))
        setState(State.PopulateTransaksiRecords(data.first))
        setTransaksiState(State.ShowNoOfTransactions(data.second))
        setNoCashTransaksiState(State.PopulateNoCashTransaksiRecords(data.first))
        setBottomSheetCreditState(State.PopulateBottomSheetPemasukanTransaksiRecords(data.first))
        setBottomSheetDebitState(State.PopulateBottomSheetPengeluaranTransaksiRecords(data.first))
    }

    private fun onBackPressed() {
        setState(State.OnBackPressed)
    }

    private fun onMonthChange(startDate: String, endDate: String) {
        setMonthState(State.OnMonthChange(startDate, endDate))
        setAutoRecordState(State.OnMonthChange(startDate, endDate))
    }

    private val eventStatus = MutableLiveData<State>()
    val observeEvent: LiveData<State> = eventStatus

    val paymentRequestList = MutableLiveData<MutableList<CategoriesItem>>()

    val disbursementRequestList = MutableLiveData<MutableList<CategoriesItem>>()

    fun getPaymentCategoriesHistory(accountId: String, startDate: String, endDate: String) = viewModelScope.launch {
        when (val result =
            paymentUseCase.getPaymentCategoriesHistory(accountId, startDate, endDate)) {
            is ApiSuccessResponse -> {
                eventStatus.value = State.GetPaymentCategoriesHistory(result.body)
            }
            else -> {
                eventStatus.value = result?.let { State.HidePaymentCard(it) }
            }
        }
    }

    fun getPaymentCategoryList(disbursableType: String) = viewModelScope.launch {
        when (val result = paymentUseCase.getPaymentCategoryList(disbursableType)) {
            is ApiSuccessResponse -> {
                eventStatus.value = State.ShowCategoryList(result.body)
            }
            is ApiErrorResponse -> {
                // eventStatus.value = PaymentCategoryViewModel.Event.showServerDown
            }
            else -> {}
        }
    }
    fun getCashbackList(
        userId: String,
        bookId: String? = null,
        start_date: String?,
        end_date: String?,
        disbursableType: String?
    ) = viewModelScope.launch {
        if (pagecount == 0) {
            cashbackList.clear()
        }
        when (val result = bankingUseCase.getCashbackList(
            userId,
            pagecount,
            bookId,
            start_date = start_date,
            end_date = end_date,
            disbursableType
        )) {
            is ApiSuccessResponse -> {
                val list = result.body
                if (list.isEmpty() && pagecount == 0) {
                    eventStatus.value = State.ShowNoCashbackPage
                } else if (list.size == 20) {
                    cashbackList.addAll(list)
                    pagecount++
                    eventStatus.value = State.UpdateCashbackList(true)
                } else {
                    cashbackList.addAll(list)
                    eventStatus.value = State.UpdateCashbackList(false)
                    pagecount = 0
                }
            }
            is ApiErrorResponse -> {
                eventStatus.value = State.ShowNoCashbackPage
            }
            else -> {}
        }
    }

    fun getTotalPayment(startDate: String, endDate: String, disbursableType: String) = viewModelScope.launch {
        when (val result =
            paymentUseCase.getTotalPayment(User.getUserId(), startDate, endDate, disbursableType)) {
            is ApiSuccessResponse -> {
                eventStatus.value = State.ShowTotalPayment(result.body, disbursableType == "PaymentRequest")
            }
            is ApiErrorResponse -> {
                eventStatus.value = State.ShowTotalPayment(TotalPayment(0.0, 0.0), disbursableType == "PaymentRequest")
            }
            else -> {}
        }
    }
}