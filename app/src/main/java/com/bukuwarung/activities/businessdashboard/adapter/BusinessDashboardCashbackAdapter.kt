package com.bukuwarung.activities.businessdashboard.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCashbackResponseItem
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardCashbackFragment.Companion.DATA_ITEM
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardCashbackFragment.Companion.TOP_CARD
import com.bukuwarung.databinding.ItemCashbackBindingBinding
import com.bukuwarung.databinding.LayoutCashbackTopCardBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.showView

class BusinessDashboardCashbackAdapter(
    val listener: BusinessDashboardCashbackListener? = null,
    private val totalCashbackAmount: Double? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var cashbackList = arrayListOf<BusinessDashboardCashbackResponseItem>()

    class BusinessDashboardCashbackViewHolder constructor(
        private val binding: ItemCashbackBindingBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            businessDashboardCashbackResponseItem: BusinessDashboardCashbackResponseItem,
            position: Int,
            listener: BusinessDashboardCashbackListener?
        ) {
            with(binding) {
                tvAmountCashback.text =
                    Utility.formatAmount(businessDashboardCashbackResponseItem.amount?.toDouble())
                tvStatus.text = businessDashboardCashbackResponseItem.status
                tvDate.text =
                    Utility.formatDateWithTime(businessDashboardCashbackResponseItem.createdDate)
                root.setOnClickListener {
                    listener?.goToPaymentHistoryDetailActivity(
                        businessDashboardCashbackResponseItem.metadata?.disbursableType,
                        businessDashboardCashbackResponseItem.metadata?.disbursableId,
                        root.context
                    )
                }
                if (businessDashboardCashbackResponseItem.status.equals("PENDING")) {
                    tvInprocessInfo.showView()
                    tvInprocessInfo.text =
                        getProcessSaldoText(businessDashboardCashbackResponseItem)
                } else {
                    tvInprocessInfo.hideView()
                }
            }
        }

        private fun getProcessSaldoText(cashbackResponseItem: BusinessDashboardCashbackResponseItem): String {
            return binding.root.context.getString(
                R.string.cashback_saldo,
                Utility.formatAmount(cashbackResponseItem.amount?.toDouble()),
                Utility.formatReceiptDate(cashbackResponseItem.createdDate)
            )
        }
    }

    class TotalCashbackViewHolder constructor(private val binding: LayoutCashbackTopCardBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(totalCashback: Double) {
            binding.tvAmountTotal.text = Utility.formatAmount(totalCashback)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TOP_CARD) {
            val topBinding = LayoutCashbackTopCardBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
            TotalCashbackViewHolder(topBinding)
        } else {
            val itemBinding = ItemCashbackBindingBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
            BusinessDashboardCashbackViewHolder(itemBinding)
        }
    }

    override fun getItemCount(): Int {
        return cashbackList.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TOP_CARD
        else DATA_ITEM
    }

    fun setData(list: List<BusinessDashboardCashbackResponseItem>) {
        this.cashbackList = list as ArrayList<BusinessDashboardCashbackResponseItem>
        notifyDataSetChanged()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder.itemViewType == TOP_CARD) {
            (holder as TotalCashbackViewHolder).bind(totalCashbackAmount.orNil)
        } else {
            (holder as BusinessDashboardCashbackViewHolder).bind(
                cashbackList[position - 1],
                position - 1,
                listener
            )
        }
    }

    interface BusinessDashboardCashbackListener {
        fun goToPaymentHistoryDetailActivity(
            paymentType: String?,
            orderId: String?,
            context: Context
        )
    }
}