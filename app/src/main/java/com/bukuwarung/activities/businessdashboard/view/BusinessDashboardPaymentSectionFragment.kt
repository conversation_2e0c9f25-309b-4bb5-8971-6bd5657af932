package com.bukuwarung.activities.businessdashboard.view

import android.content.res.Resources
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.adapter.BusinessPaymentBottomSheetViewPager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessPaymentViewPagerAdapter
import com.bukuwarung.activities.businessdashboard.model.CategoriesItem
import com.bukuwarung.activities.businessdashboard.model.PaymentCategoriesHistoryResponse
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.FragmentProductDashboardPaymentBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessDashboardPaymentSectionFragment : BaseFragment() {

    private lateinit var businessPaymentViewPagerAdapter: BusinessPaymentViewPagerAdapter
    private lateinit var businessPaymentBottomSheetViewPager: BusinessPaymentBottomSheetViewPager
    private lateinit var binding: FragmentProductDashboardPaymentBinding
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels()
    private var totalCashbackAmount: Double? = null
    private var isFirstCard = false
    private var tabPosition = 0

    companion object {
        fun createIntent(isFirstCard: Boolean): BusinessDashboardPaymentSectionFragment {
            val fragment = BusinessDashboardPaymentSectionFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD, isFirstCard)
            }
            return fragment
        }
    }

    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        viewModel.getPaymentCategoriesHistory(User.getBusinessId(), selectedMonthStartDate(), selectedMonthEndDate())
        viewModel.getTotalPayment(selectedMonthStartDate(), selectedMonthEndDate(), "PaymentRequest")
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                clCard.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                clCard.hideView()
            }
            clHeading.setOnClickListener {
                if (clCard.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    clCard.hideView()
                } else {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    clCard.showView()
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentProductDashboardPaymentBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            if (it is BusinessDashboardMainViewModel.State.GetPaymentCategoriesHistory) {

                val response = it.paymentCategoriesHistoryResponse

                response.let {
                    if (it.categories.isNullOrEmpty()) {
                        FeaturePrefManager.getInstance().businessDashboardPayment = true
                        viewModel.getPaymentCategoryList("PaymentRequest")
                        viewModel.getPaymentCategoryList("DisbursementRequest")

                    } else {
                        FeaturePrefManager.getInstance().businessDashboardPayment = false
                        binding.root.showView()
                        initCard(response)
                    }
                }
                binding.btnDetail.setSingleClickListener {
                    val bottomSheetdialog = BottomSheetDialog(requireContext())
                    bottomSheetdialog.setContentView(R.layout.bottomsheet_transaksi_business_dashboard)
                    with(bottomSheetdialog) {
                        behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 200
                        behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                    val tabLayout = bottomSheetdialog.findViewById<TabLayout>(R.id.tl_transaction)!!
                    val viewPager = bottomSheetdialog.findViewById<ViewPager2>(R.id.vp_transaction)!!
                    val btnDismiss = bottomSheetdialog.findViewById<Button>(R.id.btn_dismiss)
                    val viewSlider = bottomSheetdialog.findViewById<View>(R.id.vw_slider)
                    viewSlider?.setOnClickListener {
                        bottomSheetdialog.dismiss()
                    }
                    btnDismiss?.setSingleClickListener {
                        bottomSheetdialog.dismiss()
                    }
                    businessPaymentBottomSheetViewPager = BusinessPaymentBottomSheetViewPager(this)
                    viewPager.adapter = businessPaymentBottomSheetViewPager
                    viewPager.isNestedScrollingEnabled = false
                    viewPager.currentItem = tabPosition
                    TabLayoutMediator(tabLayout, viewPager) { tab, position ->
                        when (position) {
                            0 -> {
                                tab.text = context?.getString(R.string.payment_label_payment_in)
                            }
                            else -> {
                                tab.text = context?.getString(R.string.payment_label_payment_out)
                            }
                        }
                    }.attach()
                    bottomSheetdialog.show()
                }
                binding.tvCashback.setOnClickListener {
                    if (binding.tlPayment.selectedTabPosition == 0) {
                        triggerCashBackAnalyticsEvent(true)
                        activity?.supportFragmentManager?.beginTransaction()?.add(
                            R.id.fragment_container_business_dashboard,
                            BusinessDashboardCashbackFragment.createIntent(
                                "PaymentRequest",
                                totalCashbackAmount.orNil
                            ),
                            "BusinessDashboardCashbackFragment"
                        )?.addToBackStack("BusinessDashboardCashbackFragment")?.commit()
                    } else {
                        triggerCashBackAnalyticsEvent(false)
                        activity?.supportFragmentManager?.beginTransaction()?.add(
                            R.id.fragment_container_business_dashboard,
                            BusinessDashboardCashbackFragment.createIntent(
                                "DisbursementRequest",
                                totalCashbackAmount.orNil
                            ),
                            "BusinessDashboardCashbackFragment"
                        )?.addToBackStack("BusinessDashboardCashbackFragment")?.commit()
                    }
                }
            }
            if (it is BusinessDashboardMainViewModel.State.HidePaymentCard) {
                viewModel.getPaymentCategoryList("PaymentRequest")
                viewModel.getPaymentCategoryList("DisbursementRequest")
            }
            if (it is BusinessDashboardMainViewModel.State.ShowCategoryList) {
                val paymentRequestList = mutableListOf<CategoriesItem>()
                val disbursementRequestList = mutableListOf<CategoriesItem>()
                val list = it.list
                for (item in list) {
                    for (listItem in item.categoryList) {
                        if (listItem.disbursableType.equals("DisbursementRequest")) {
                            disbursementRequestList.add(CategoriesItem(name = listItem.name, mainCategory = listItem.title))
                        } else {
                            paymentRequestList.add(CategoriesItem(name = listItem.name, mainCategory = listItem.title))
                        }
                    }
                }
                if (paymentRequestList.isNotEmpty()) {
                    viewModel.paymentRequestList.value = paymentRequestList
                } else {
                    viewModel.disbursementRequestList.value = disbursementRequestList
                }
                businessPaymentViewPagerAdapter = BusinessPaymentViewPagerAdapter(this)

                binding.vpPayment.adapter = businessPaymentViewPagerAdapter
                binding.vpPayment.isNestedScrollingEnabled = false

                TabLayoutMediator(binding.tlPayment, binding.vpPayment) { tab, position ->
                    when (position) {
                        0 -> {
                            tab.text = context?.getString(R.string.payment_label_payment_in)
                        }
                        else -> {
                            tab.text = context?.getString(R.string.payment_label_payment_out)
                        }
                    }
                }.attach()

                binding.tlPayment.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                    override fun onTabSelected(tab: TabLayout.Tab?) {
                        if (tab != null) {
                            if (tab.position == 0) {
                                tabPosition = 0
                                viewModel.getTotalPayment(selectedMonthStartDate(), selectedMonthEndDate(), "PaymentRequest")
                                binding.viewInfo.hideView()
                                binding.ivInfo.hideView()
                                binding.tvMenghemat.hideView()
                            } else if (tab.position == 1) {
                                tabPosition = 1
                                viewModel.getTotalPayment(selectedMonthStartDate(), selectedMonthEndDate(), "DisbursementRequest")
                                binding.viewInfo.showView()
                                binding.ivInfo.showView()
                                binding.tvMenghemat.showView()
                            }
                        }
                    }

                    override fun onTabUnselected(tab: TabLayout.Tab?) {}
                    override fun onTabReselected(tab: TabLayout.Tab?) {}
                })
            }

            if (it is BusinessDashboardMainViewModel.State.ShowTotalPayment) {
                totalCashbackAmount = it.totalPayment.totalCashbackAmount
                binding.tvBiayaAdminAmount.text = Utility.formatAmount(it.totalPayment.totalAdminFeeAmount)
                binding.tvCashbackAmount.text = Utility.formatAmount(it.totalPayment.totalCashbackAmount)
                if(!it.isPaymentReq) {
                    val remainingAmount = AppConst.PAYMENT_FEE - (it.totalPayment.totalAdminFeeAmount ?: 0.0)
                    val menghematAmount = remainingAmount + (it.totalPayment.totalCashbackAmount ?: 0.0)
                    val stringBuilder = SpannableStringBuilder(
                        context?.getString(
                            R.string.menghemat_amount,
                            Utility.formatAmount(menghematAmount)
                        )
                    )
                    stringBuilder.setSpan(
                        StyleSpan(
                            Typeface.BOLD
                        ), 64, stringBuilder.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE
                    )
                    binding.tvMenghemat.text = stringBuilder
                }
            }
        }
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data =
                        (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    viewModel.getPaymentCategoriesHistory(
                        User.getBusinessId(),
                        data.startDate,
                        data.endDate
                    )
                }
                else -> {}
            }
        })

        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data =
                        (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    viewModel.getPaymentCategoriesHistory(
                        User.getBusinessId(),
                        data.startDate,
                        data.endDate
                    )
                }
                else -> {}
            }
        })
    }

    private fun initCard(response: PaymentCategoriesHistoryResponse) {
        val paymentRequestList = mutableListOf<CategoriesItem>()
        val disbursementRequestList = mutableListOf<CategoriesItem>()
        for (categoryItem in response.categories.orEmpty()) {
            if (categoryItem.type.equals("PaymentRequest")) {
                paymentRequestList.add(categoryItem)
            } else {
                disbursementRequestList.add(categoryItem)
            }
        }
        if (paymentRequestList.isEmpty()) {
            viewModel.getPaymentCategoryList("PaymentRequest")
        }
        if (disbursementRequestList.isEmpty()) {
            viewModel.getPaymentCategoryList("DisbursementRequest")
        }
        viewModel.paymentRequestList.value = paymentRequestList
        viewModel.disbursementRequestList.value = disbursementRequestList
        businessPaymentViewPagerAdapter = BusinessPaymentViewPagerAdapter(this)
        binding.vpPayment.adapter = businessPaymentViewPagerAdapter
        binding.vpPayment.isNestedScrollingEnabled = false

        TabLayoutMediator(binding.tlPayment, binding.vpPayment) { tab, position ->
            when (position) {
                0 -> {
                    tab.text = context?.getString(R.string.payment_label_payment_in)
                }
                else -> {
                    tab.text = context?.getString(R.string.payment_label_payment_out)
                }
            }
        }.attach()

        binding.tlPayment.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    if (tab.position == 0) {
                        viewModel.getTotalPayment(selectedMonthStartDate(), selectedMonthEndDate(), "PaymentRequest")
                        binding.viewInfo.hideView()
                        binding.ivInfo.hideView()
                        binding.tvMenghemat.hideView()
                    } else if (tab.position == 1) {
                        viewModel.getTotalPayment(selectedMonthStartDate(), selectedMonthEndDate(), "DisbursementRequest")
                        binding.viewInfo.showView()
                        binding.ivInfo.showView()
                        binding.tvMenghemat.showView()
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun selectedMonthStartDate(): String {
        return (activity as BusinessDashboardMainActivity).getMonthStartDate()
    }

    private fun selectedMonthEndDate(): String {
        return (activity as BusinessDashboardMainActivity).getMonthEndDate()
    }

    private fun triggerCashBackAnalyticsEvent(isPaymentIn: Boolean) {
        val prop = AppAnalytics.PropBuilder()
        val entryPointValue = if (isPaymentIn) AnalyticsConst.BD_PAYMENT_IN else AnalyticsConst.BD_PAYMENT_OUT
        prop.put(AnalyticsConst.ENTRY_POINT2, entryPointValue)

        AppAnalytics.trackEvent(AnalyticsConst.BD_CASHBACK_DETAIL_CLICK, prop)
    }

}