package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessNoCashTransaksiAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.CashListViewModel
import androidx.fragment.app.viewModels
import com.bukuwarung.activities.expense.adapter.model.DailySummary
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity.Companion.getNewIntent
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst.BD
import com.bukuwarung.constants.AnalyticsConst.BD_CATEGORY_CLICK
import com.bukuwarung.constants.AnalyticsConst.CATEGORY_NAME
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT
import com.bukuwarung.constants.AnalyticsConst.NO_TRX
import com.bukuwarung.constants.AnalyticsConst.TRANSAKSI
import com.bukuwarung.constants.AnalyticsConst.TYPE
import com.bukuwarung.databinding.FragmentCashProductTransaksiBinding
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NoCashBusinessTransaksiFragment : BaseFragment(), BusinessNoCashTransaksiAdapter.BusinessTransaksiListener {
    private lateinit var binding: FragmentCashProductTransaksiBinding
    private val viewModel: BusinessDashboardMainViewModel by viewModels({ requireActivity() })
    private lateinit var cashAdapter: BusinessNoCashTransaksiAdapter

    private val cashListViewModel: CashListViewModel by viewModels()

    private var sdate:String = "2022-01-01"
    private var edate:String = "2022-01-31"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCashProductTransaksiBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {

        sdate = DateTimeUtils.getCurrentMonthStartDate()
        edate = DateTimeUtils.getCurrentMonthEndDate()
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(cashListViewModel, sdate,edate))

        cashAdapter = BusinessNoCashTransaksiAdapter(this)
        binding.productTransaksiUnitRecyclerView.apply {
            adapter = cashAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state_no_cash_transaksi) { it ->
            when (it) {
                is BusinessDashboardMainViewModel.State.PopulateNoCashTransaksiRecords -> updateTransaksiAdapter(it.transksiRecords)
                else -> {}
            }
        }
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.PopulateNoCashTransaksiRecords -> updateTransaksiAdapter(
                    (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.PopulateNoCashTransaksiRecords).transksiRecords)
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                else -> {}
            }
        })

        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                else -> {}
            }
        })
    }

    fun onMonthChange(startDate:String,endDate:String) {
        sdate = startDate
        edate = endDate
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(cashListViewModel, sdate,edate))
    }

    fun updateTransaksiAdapter(records: List<DataHolder>) {
        // Data of transaksi here

        val categories = RemoteConfigUtils.SelectCategory.getDebitCategoriesNew()
        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<com.bukuwarung.activities.expense.data.Category?>?>() {}.type
        val categoriesToDisplay: List<com.bukuwarung.activities.expense.data.Category> = gson.fromJson(categories, jsonType)

        val filteredCategoryList: ArrayList<DataHolder> = arrayListOf()

        val cat = HashSet<String>()

        for (category in categoriesToDisplay) {
            cat.add(category.categoryName)
        }

        for (data in records) {
            if (cat.contains(data.name) || data.name!!.contentEquals("Pengeluaran")) {
                filteredCategoryList.add(data)
                cat.remove(data.name)
            }
        }

        for (category in cat) {
            if (filteredCategoryList.size > 2) {
                break
            } else {
                filteredCategoryList.add(
                    DataHolder.CategoryRowHolder(
                        DailySummary(
                            category,
                            "",
                            "",
                            "",
                            0
                        )
                    )
                )
            }
        }
        cashAdapter.refreshView(filteredCategoryList, categoriesToDisplay)
    }

    companion object {
        fun getInstance() = NoCashBusinessTransaksiFragment()
    }

    override fun goToDetail(
        id: String,
        isExpense: Boolean,
        status: Int,
        isDetailTransaksi: Boolean,
        isAutoRecordTxn: Boolean
    ) {
        if (context != null && id.isNotNullOrEmpty()) {
            val intent = getNewIntent(requireContext(), id, false)
            intent.putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, isExpense)
            intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status)
            intent.putExtra(CashTransactionDetailActivity.IS_DETAIL_TRANSACTION, isDetailTransaksi)
            intent.putExtra(
                CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION,
                isAutoRecordTxn
            )
            startActivity(intent)
        }

    }

    override fun goToCategory(id: String, name: String,trxCount: Int) {
        if (id.isNotNullOrEmpty()) {
            try {
                val intent = Intent(context, CategoryTransactionsActivity::class.java)
                intent.putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, id)
                intent.putExtra(CategoryTransactionsActivity.START_DATE, (activity as BusinessDashboardMainActivity ).getMonthStartDate())
                intent.putExtra(CategoryTransactionsActivity.END_DATE, (activity as BusinessDashboardMainActivity ).getMonthEndDate())
                AppAnalytics.trackEvent("open_cash_transaction_list", "", "")
                startActivity(intent)
                val propBuilder = PropBuilder()
                propBuilder.put(CATEGORY_NAME, name)
                propBuilder.put(NO_TRX, trxCount)
                propBuilder.put(TYPE, TRANSAKSI)
                propBuilder.put(ENTRY_POINT, BD)

                AppAnalytics.trackEvent(BD_CATEGORY_CLICK, propBuilder)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}