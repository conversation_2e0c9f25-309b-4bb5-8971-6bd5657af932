package com.bukuwarung.activities.profile.summary.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.FormType
import com.bukuwarung.activities.onboarding.form.OperationHourSelectorFragment
import com.bukuwarung.activities.profile.businessprofile.AdditionalInformationFragment
import com.bukuwarung.activities.profile.businessprofile.BackConfirmationDialog
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileFragment
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ACTION
import com.bukuwarung.constants.AnalyticsConst.EDIT
import com.bukuwarung.constants.AnalyticsConst.HomePage.SECTION
import com.bukuwarung.constants.AnalyticsConst.NEW
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.LayoutBusinessOperationalInfoFragmentBinding
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.AppIdGenerator
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isEditingAddressForFirstTime
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.isTrue

class BusinessOperationalInformationFragment: BaseFragment() {

    private lateinit var binding: LayoutBusinessOperationalInfoFragmentBinding
    private val businessFormViewModel: DetailedBusinessFormViewModel by activityViewModels()
    private var bookEntity: BookEntity? = null
    private var bookId:String = ""
    private var isFromProfileTab = false

    companion object {
        const val BOOK_ID = "BOOK_ID"
        const val IS_FROM_PROFILE_TAB = "IS_FROM_PROFILE_TAB"
        fun instance(targetBookId:String, isFromProfileTab: Boolean = false): BusinessOperationalInformationFragment  {
            val fragment = BusinessOperationalInformationFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, targetBookId)
            bundle.putBoolean(IS_FROM_PROFILE_TAB, isFromProfileTab)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        arguments?.let {
            if (it.containsKey(BOOK_ID)) {
                bookId = requireArguments().getString(BOOK_ID).toString()
            }
            if (it.containsKey(IS_FROM_PROFILE_TAB)) {
                isFromProfileTab = requireArguments().getBoolean(IS_FROM_PROFILE_TAB)
            }
        }
        if(bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(context)?.getBusinessByIdSync(bookId)
        } else {
            bookEntity = BookEntity()
        }
        binding = LayoutBusinessOperationalInfoFragmentBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        businessFormViewModel.setBusinessData(bookEntity?: BookEntity())
    }

    override fun subscribeState() {
      //NO IMP
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            etBookingHours.setOnClickListener {
                val categoryForm = OperationHourSelectorFragment.getInstance(
                    data = RemoteConfigUtils.OnBoarding.getBusinessCategories(),
                    showHeader = true,
                    showActionButton = true,
                    formType = FormType.BusinessTiming,
                    targetBookId = bookId
                )
                activity?.supportFragmentManager?.beginTransaction()
                    ?.add(R.id.fragment_container, categoryForm)?.addToBackStack("OperationHourSelectorFragment")
                    ?.commit()
                AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.BUSINESS_PROFILE)
                    put(
                        ACTION, if (Utility.isSectionEmpty(
                                bookEntity,
                                Utility.profileAdditionalInfoFields
                            )
                        ) NEW else EDIT
                    )
                    AppAnalytics.trackEvent("edit_business_operations_time", this)
                }
            }
            if(bookEntity?.operatingDays.isNotNullOrEmpty() && !bookEntity?.operatingDays.equals("0")) {
                val businessTiming = bookEntity?.operatingDays+ ", " + bookEntity?.operatingHourStart+" - "+bookEntity?.operatingHourEnd
                etBookingHours.setText(businessTiming)
            } else {
                etBookingHours.setText(getString(R.string.choose_opening_hours))
            }
            if (isFromProfileTab) {
                btnSave.text = getString(R.string.next)
            } else {
                btnSave.text = getString(R.string.save)
            }
        }

        businessFormViewModel.bookDataLive.observe(viewLifecycleOwner, Observer { data ->
            if (data?.operatingDays.isNotNullOrEmpty() && !data?.operatingDays.equals("0")) {
                val businessTiming =
                    data?.operatingDays + ", " + data?.operatingHourStart + " - " + data?.operatingHourEnd
                binding.etBookingHours.setText(businessTiming)
            }

            val addressText = Utility.getCompleteAddress(data).takeIf { it.isNotEmpty() } ?: getString(R.string.choose_business_address)
            binding.etBusinessAddress.setText(addressText)
        })

        binding.etBusinessAddress.setOnClickListener {
            AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.BUSINESS_PROFILE)
                put(AnalyticsConst.ACTION, if (bookEntity?.isEditingAddressForFirstTime().isTrue) AnalyticsConst.NEW else AnalyticsConst.EDIT)
                AppAnalytics.trackEvent(AnalyticsConst.EDIT_BUSINESS_ADDRESS, this)
            }

            businessFormViewModel.onEventReceipt(DetailedBusinessFormViewModel.Event.RequestToOpenAddressFlow)
        }


        binding.btnSave.setOnClickListener {
            val prop = AppAnalytics.PropBuilder()
            prop.put(SECTION, "BUSINESS_OPERATION")
            prop.put(
                "section_status",
                Utility.sectionCompletionStatus(bookEntity, Utility.profileInfoFields)
            )
            prop.put(
                ACTION,
                if (Utility.isSectionEmpty(bookEntity, Utility.profileInfoFields)) NEW else EDIT
            )
            prop.put("business_province", bookEntity?.province.toString())
            prop.put("business_city", bookEntity?.city.toString())
            prop.put("business_postal_code", bookEntity?.postalCode?.toString())
            prop.put("business_street_address", bookEntity?.businessAddress?.toString())
            prop.put("business_operating_days", bookEntity?.operatingDays)
          if (isFromProfileTab) {
              // update livedata here to pass in addional info fragment for saving it eventually
              prop.put(AnalyticsConst.ENTRY_POINT2, "progress_bar_lainnya")
              val additionalInfoFragment = AdditionalInformationFragment.instance(bookId, true)
              activity?.supportFragmentManager?.beginTransaction()
                  ?.add(R.id.fragment_container, additionalInfoFragment)?.addToBackStack("AdditionalInformationFragment")
                  ?.commit()
          } else {
              prop.put(AnalyticsConst.ENTRY_POINT2, "user_profile")
              if (bookEntity?.bookId.isNullOrEmpty()) {
                  bookEntity?.bookId = AppIdGenerator.resourceUUID()
                  bookEntity?.ownerId = User.getUserId()
                  EntityHelper.fillBusinessMetadata(bookEntity)
                  SessionManager.getInstance().selectedBookName = bookEntity!!.bookName
                  SessionManager.getInstance().setBusinessId(bookEntity!!.bookId)
                  SessionManager.getInstance().setSelectedBookName(bookEntity!!.bookName)
                  SessionManager.getInstance().setAppState(1)
                  MainActivity.startActivityAndClearTop(activity)
              } else {
                  bookEntity?.updatedAt = Utility.getCurrentTime()
                  bookEntity?.dirty = 1
              }
              BusinessRepository.getInstance(Application.getAppContext())
                  .createBusinessWithDetails(bookEntity)
              requireActivity().supportFragmentManager.beginTransaction().replace(
                  R.id.main_container,
                  NgBusinessProfileFragment.instance(bookId)
              ).commit()
              requireActivity().supportFragmentManager.popBackStack()
          }
            AppAnalytics.trackEvent("business_profile_section_saved", prop)
            Utilities.sendEventsToBackendWithBureau("save_business_profile_section", "operation_info")
        }

        binding.backBtn.setOnClickListener {
            showBackPromptDialog()
        }
    }

    private fun showBackPromptDialog() {
        val dialog = context?.let { context ->
            BackConfirmationDialog(
                context,
                "Keluar dari isi profil usaha?",
                "Tenang, data akan tersimpan saat keluar dan kamu bisa lanjutkan kembali nanti.",
                "Keluar",
                "Lanjut Mengisi"
            ) {
                val prop = AppAnalytics.PropBuilder()
                prop.put(
                    AnalyticsConst.ENTRY_POINT2,
                    if (isFromProfileTab) "progress_bar_lainnya" else "user_profile"
                )
                prop.put(AnalyticsConst.HomePage.SECTION, "business_operation")
                if (it) {
                    prop.put(AnalyticsConst.HomePage.BUTTON_CLICK_EVENT, "yes")
                    activity?.onBackPressed()
                } else {
                    prop.put(AnalyticsConst.HomePage.BUTTON_CLICK_EVENT, "no")
                }
                AppAnalytics.trackEvent("click_back_button_business_profile", prop)
            }
        }
        dialog?.show()
    }
}