package com.bukuwarung.activities.profile

import android.graphics.Color
import android.graphics.PorterDuff
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.databinding.ProfilePinItemBinding
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.TooltipBuilder
import com.bumptech.glide.Glide
import com.facebook.FacebookSdk
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip

class ProfilePinAdapter(val pins:List<ProfilePins>, var transactionCount: Int, val getSubMenuItem: (Int) -> Unit) : RecyclerView.Adapter<ProfilePinAdapter.PinViewHolder>() {

    private var tooltip: SimpleTooltip? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PinViewHolder {
        val binding = ProfilePinItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PinViewHolder(binding).listen { pos, type ->
            if (transactionCount > 0 || pos == 0) {
                getSubMenuItem(pos)
            } else {
                val tooltipBuilder = parent.context?.let {
                    TooltipBuilder.builder(it)
                            .setAnchor(parent.getChildAt(pos))
                            .setText("Buat 1 catatan transaksi, utang, atau pembayaran untuk membuka fitur ini.")
                            .setGravity(Gravity.END)
                }
                tooltip = tooltipBuilder?.build()
                tooltip?.show()
            }
        }
    }


    override fun onBindViewHolder(holder: PinViewHolder, position: Int) {
        holder.bind(pins[position], transactionCount)
    }

    override fun getItemCount(): Int = pins.size

    fun setLockstateForPins(count: Int) {
        transactionCount = count
        notifyDataSetChanged()
    }

    class PinViewHolder constructor(private val binding: ProfilePinItemBinding) : RecyclerView.ViewHolder(binding.root) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
                .setDuration(1000)
                .setBaseAlpha(0.3f)
                .setHighlightAlpha(0.7f)
                .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
                .setAutoStart(true)
                .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        fun bind(pin: ProfilePins, transactionCount: Int) {
            with(binding) {
                Glide.with(root).load(pin.icon).placeholder(shimmerDrawable).centerCrop().into(ivProfilePinItem)
                tvProfilePinItem.text = pin.name
                if (transactionCount > 0 || pin.name.equals("Undang", true)) {
                    root.alpha = 1f
                    root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.white))
                } else {
                    root.alpha = 0.5f
                    root.setBackgroundResource(R.drawable.bg_black5)
                }
            }
        }
    }
}