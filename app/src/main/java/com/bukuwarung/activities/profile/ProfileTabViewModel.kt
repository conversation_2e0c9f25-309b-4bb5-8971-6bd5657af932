package com.bukuwarung.activities.profile

import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.activities.BaseAndroidViewModel
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTierBenefit
import com.bukuwarung.activities.selfreminder.adapter.dataholder.EmptyReminderDataHolder
import com.bukuwarung.activities.selfreminder.adapter.dataholder.SelfReminderDataHolder
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.superclasses.DataHolder.LastRowHolder
import com.bukuwarung.bulk.usecase.GetDefaultCashCategory
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.restclient.CompletionRequest
import com.bukuwarung.database.entity.*
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.SelfReminderRepository
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.config.AutoRecordUseCase
import com.bukuwarung.domain.config.StockConfigUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.domain.profile.ProfileUseCase
import com.bukuwarung.domain.transaction.TransactionUseCase
import com.bukuwarung.los.LosUseCase
import com.bukuwarung.payments.data.model.PaymentSummaryResponse
import com.bukuwarung.payments.data.model.ReferralDataResponse
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.wrapper.EventWrapper
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import dagger.hilt.android.lifecycle.HiltViewModel


@HiltViewModel
class ProfileTabViewModel : BaseAndroidViewModel {
    private var losUseCase: LosUseCase
    private var businessUseCase: BusinessUseCase
    private var paymentUseCase: PaymentUseCase
    private var transactionUseCase: TransactionUseCase
    private var stockConfigUseCase: StockConfigUseCase
    private var autoRecordUseCase: AutoRecordUseCase
    private var defaultCashCategory: GetDefaultCashCategory
    private val finproUseCase: FinproUseCase
    private val profileUseCase: ProfileUseCase
    private val appConfigManager: AppConfigManager
    private val featurePrefManager: FeaturePrefManager
    private val liveDataMerger: MediatorLiveData<List<DataHolder>>

    @Inject
    constructor(
        losUseCase: LosUseCase,
        businessUseCase: BusinessUseCase,
        paymentUseCase: PaymentUseCase,
        transactionUseCase: TransactionUseCase,
        stockConfigUseCase: StockConfigUseCase,
        autoRecordUseCase: AutoRecordUseCase,
        finproUseCase: FinproUseCase,
        profileUseCase: ProfileUseCase,
        appConfigManager: AppConfigManager,
        defaultCashCategory: GetDefaultCashCategory,
        featurePrefManager: FeaturePrefManager,
        application: Application
    ) : super(application) {
        this.losUseCase = losUseCase
        this.businessUseCase = businessUseCase
        this.paymentUseCase = paymentUseCase
        this.transactionUseCase = transactionUseCase
        this.stockConfigUseCase = stockConfigUseCase
        this.autoRecordUseCase = autoRecordUseCase
        this.defaultCashCategory = defaultCashCategory
        this.finproUseCase = finproUseCase
        this.profileUseCase = profileUseCase
        this.appConfigManager = appConfigManager
        this.featurePrefManager = featurePrefManager
        this.liveDataMerger = MediatorLiveData<List<DataHolder>>()
        this._state = MutableLiveData<EventWrapper<State>>()
        this.state = _state
        this.countryCode = SessionManager.getInstance().countryCode

        liveDataMerger.addSource(
            SelfReminderRepository.getInstance(application).getObservableReminderByUser(
                User.getUserId()
            )
        ) { list ->
            selfReminderList = list
            convertToViewObject()
        }
    }

    private var selfReminderList: List<SelfReminderEntity?>? = null

    fun getDataHolderList(): LiveData<List<DataHolder>> {
        return liveDataMerger
    }

    fun convertToViewObject() {
        val convertedList: ArrayList<DataHolder> = ArrayList()
        if (selfReminderList != null) {
            for (selfReminderEntity in selfReminderList!!) {
                convertedList.add(SelfReminderDataHolder(selfReminderEntity))
            }
            convertedList.add(LastRowHolder())
        } else {
            convertedList.add(EmptyReminderDataHolder())
        }

        liveDataMerger.setValue(convertedList)
    }

    sealed class State {
        data class ShowBanner(val showLendingBanner:Boolean): State()
        data class ShowIncentiveLayout(val showLayout: Boolean) : State()
        data class ShowTokokoDownloadLayout(val show: Boolean) : State()
        data class ShowPaymentReferral(val show: Boolean) : State()
        data class ShowBukuPay(val show: Boolean) : State()
        data class ContinueUploadImage(val uri: Uri, val bookEntity: BookEntity) : State()
        data class setProfileUrlToUserEntity(val url: String) : State()
        data class ReturnCardBackground(val res: Int) : State()
        data class HandlePaymentVisibility(
            val hasBankAccount: Boolean,
            val paymentTabEnabled: Boolean,
            val hasTransaction: Boolean,
            val hasShownListBankCoachmark: Boolean = OnboardingPrefManager.getInstance()
                .getHasFinishedForId(OnboardingPrefManager.TUTOR_BANK_LIST_LAINNYA)
        ) : State()

        data class SetProfileData(
            val trxCount: Int, val bookEntity: BookEntity?,
            val hasPendingPayment: Boolean, val phoneNumber: String,
            val hasEditCard: Boolean, val showPpobBanner: Boolean
        ) : State()

        data class ContinueOnUpdateClicked(val useFlexibleUpdate: Boolean) : State()
        data class ShouldShowBankListCoachmark(val hasShownListBankCoachmark: Boolean) : State()
        data class SetSummaryData(val summaryResponse: PaymentSummaryResponse) : State()
        object GoToRegisterBankAccount : State()
        data class StockTabInitialVisibility(val visibility: Boolean) : State()
        data class AutoRecordTabInitialVisibility(val visibility: Boolean) : State()
        object ShowStockTabWarningPopup : State()
        data class ChangeStockSwitchStatus(val status: Boolean) : State()
        data class SetProfilePins(val profilePins: List<ProfilePins>) : State()
        data class SetPinUnlocked(val count: Int) : State()
        data class SetFloatingButtonHide(val isVisible: Boolean): State()
        data class SetFloatingButtonVisibility(val count: Int): State()
        data class Error(val errorMessage: String, val status: Int) : State()
        object RefreshState : State()
        data class OnProfileTierLoaded(val loyaltyAccount: LoyaltyAccount, val tier: LoyaltyTier, val isWhitelister:Boolean): State()
        data class OnSaldoBonusLoaded(val saldoBonusResponse: SaldoResponse, val loyaltyAccount: LoyaltyAccount, val tier: LoyaltyTier, val isWhitelister:Boolean, val tierBenefits: LoyaltyTierBenefit): State()
        data class OnLoyaltySectionVisibility(val isVisible: Boolean): State()
        object HideBnplEntryPoint: State()
        object ShowBnplEntryPoint: State()
        data class OnReferralDataLoaded(val referralDataResponse: ReferralDataResponse): State()
    }

    private val _state: MutableLiveData<EventWrapper<State>>
    val state: LiveData<EventWrapper<State>>
    private var countryCode: String
    private var bookEntity: BookEntity? = null
    private var stockTabState = false
    private var autoRecordTabState = false

    sealed class Event {
        object OnCreateView : Event()
        object OnSetProfileDetails : Event()
        object OnUpdateClicked : Event()
        object GetCardBackground : Event()
        object SetShownListBankCoachmark : Event()
        object SetShownAutoRecordCoachmark : Event()
        object CheckForCoachmark : Event()
        object OnRegisterBankAccount : Event()
        object OnStockTabClicked : Event()
        object OnAutoRecordTabClicked : Event()
        object GetPinsForProfile : Event()
        object GetUserProfileOptions : Event()
        object GetBrickAccounts : Event()
        object DisconnectBrickAccount : Event()
        object OnBnplDetailsRequested: Event()

        data class OnCountryPicked(val countryCode: String) : Event()
        data class OnUploadImage(val image: Bitmap, val uri: Uri) : Event()
        data class OnUploadUserProfileImage(val image: Bitmap, val uri: Uri) : Event()
        data class OnStockTabWarningPopUpSelected(val proceed: Boolean) : Event()
        data class OnAutoRecordTabWarningPopUpSelected(val proceed: Boolean) : Event()
        data class OnTransactionCount(val transactionCount: Int) : Event()
        data class OnFloatingButtonHide(val isVisible: Boolean): Event()
        data class OnFloatingButtonThreshold(val count: Int): Event()
        data class ProfileCompletionEvent(val request: CompletionRequest): Event()

    }

    fun onEventReceived(event: Event) {
        when (event) {
            Event.OnCreateView -> handleOnCreateView()
            Event.OnSetProfileDetails -> handleOnSetProfileDetails()
            Event.OnUpdateClicked -> handleUpdateClicked()
            Event.GetCardBackground -> setState(State.ReturnCardBackground(FeaturePrefManager.getInstance().cardColor))
            Event.SetShownListBankCoachmark -> OnboardingPrefManager.getInstance()
                .setHasFinishedForId(OnboardingPrefManager.TUTOR_BANK_LIST_LAINNYA)
            Event.SetShownAutoRecordCoachmark -> OnboardingPrefManager.getInstance()
                .setHasShownAutoRecordCoachmark()
            Event.CheckForCoachmark -> checkCoachmark()
            Event.OnRegisterBankAccount -> setState(State.GoToRegisterBankAccount)
            Event.OnStockTabClicked -> {
                if (!stockTabState) {
                    stockConfigUseCase.setStockTabEnabledFromSettings(stockConfigUseCase.STOCK_SELECTED)
                    stockTabState = true
                    setState(State.ChangeStockSwitchStatus(stockTabState))
                } else {
                    setState(State.ShowStockTabWarningPopup)
                }
            }
            Event.OnBnplDetailsRequested -> fetchBnplDetails()
            Event.GetPinsForProfile -> fetchProfilePinsFromRemoteConfig()
            Event.GetUserProfileOptions -> fetchUserProfileOptionsFromRemoteConfig()

            is Event.OnStockTabWarningPopUpSelected -> {
                if (!event.proceed) return
                stockTabState = !event.proceed
                stockConfigUseCase.setStockTabEnabledFromSettings(stockConfigUseCase.STOCK_UN_SELECTED)
                setState(State.ChangeStockSwitchStatus(stockTabState))
            }
            is Event.OnCountryPicked -> countryCode = event.countryCode
            is Event.OnUploadImage -> uploadImage(event.image, event.uri)
            is Event.OnUploadUserProfileImage -> uploadUserProfileImage(event.image, event.uri)
            is Event.OnTransactionCount -> checkTransactionCountForPinsVisibility(event.transactionCount)
            is Event.OnFloatingButtonHide -> hideFloatingButton(event.isVisible)
            is Event.OnFloatingButtonThreshold -> showFloatingButton(event.count)
            is Event.ProfileCompletionEvent -> sendCompletionRequest(event.request)
            else -> {}
        }
    }

    private fun showFloatingButton(count: Int) {
        setState(State.SetFloatingButtonVisibility(count))
    }

    private fun hideFloatingButton(isVisible: Boolean) {
        setState(State.SetFloatingButtonHide(isVisible))
    }

    private fun fetchProfilePinsFromRemoteConfig() {
        setState(State.SetProfilePins(profileUseCase.getProfilePins().sortedBy { it.rank }))
    }

    private fun fetchUserProfileOptionsFromRemoteConfig() {
        setState(State.SetProfilePins(profileUseCase.getUserProfileOptions().sortedBy { it.rank }))
    }

    private fun checkTransactionCountForPinsVisibility(transactionCount: Int) {
        if (transactionCount > 0) {
            setState(State.SetPinUnlocked(transactionCount))
        }
    }

    private fun checkCoachmark() = launch {
        val hasShownListBankCoachmark: Boolean = OnboardingPrefManager.getInstance()
            .getHasFinishedForId(OnboardingPrefManager.TUTOR_BANK_LIST_LAINNYA)
        delay(200)
        setState(State.ShouldShowBankListCoachmark(hasShownListBankCoachmark))
    }

    private fun uploadImage(image: Bitmap, uri: Uri) = launch {
        if (bookEntity == null) bookEntity = businessUseCase.getCurrentBusiness()
        bookEntity?.let {
            setState(State.ContinueUploadImage(uri, it))
        }

        val bookId = bookEntity!!.bookId
        val userId = User.getUserId()

        businessUseCase.uploadProfilePicture(image, userId, bookId)

    }

    private fun uploadUserProfileImage(image: Bitmap, uri: Uri) = launch {
        if (bookEntity == null) bookEntity = businessUseCase.getCurrentBusiness()
        bookEntity?.let {
            setState(State.ContinueUploadImage(uri, it))
        }

        val bookId = bookEntity!!.bookId
        val userId = User.getUserId()

        val userProfileUrl =  businessUseCase.uploadUserProfilePicture(image, userId, bookId)

        userProfileUrl?.let { State.setProfileUrlToUserEntity(it) }?.let { setState(it) }

    }

    private fun handleUpdateClicked() {
        val docRef: DocumentReference = FirebaseFirestore.getInstance().collection("app_config")
            .document(BuildConfig.APP_CONFIG_VERSION)
        docRef.get().addOnSuccessListener { documentSnapshot ->
            try {
                BusinessRepository.appConfig = documentSnapshot.toObject(AppConfig::class.java)
                AppConfigManager.getInstance().latestVersion =
                    BusinessRepository.appConfig.latestVersion
                AppConfigManager.getInstance().enableFlexibleUpdate =
                    BusinessRepository.appConfig.useFlexibleUpdate
                val useFlexibleUpdate: Boolean =
                    AppConfigManager.getInstance().enableFlexibleUpdate == AppConst.IN_APP_UPDATE
                setState(State.ContinueOnUpdateClicked(useFlexibleUpdate))
            } catch (e: Exception) {
                e.recordException()
            }
        }
    }

    private fun handleOnCreateView() = launch {
        if(Utility.hasInternet() && RemoteConfigUtils.shouldShowLoyaltyLayout() && !RemoteConfigUtils.shouldShowSaldoBonus()) {
            getProfileTierInfo()
        }
        if(Utility.hasInternet() && RemoteConfigUtils.shouldShowLoyaltyLayout() && RemoteConfigUtils.shouldShowSaldoBonus()) {
            getSaldoBonusInfo()
        }
        if (Utility.hasInternet() && RemoteConfigUtils.getLoyaltyWidgetType() == 2) {
            getReferralData()
        }
        val transactionCount = transactionUseCase.getTranscationCount()
        if (RemoteConfigUtils.shouldShowIncentiveLayout()) {
            setState(State.ShowIncentiveLayout(RemoteConfigUtils.shouldShowIncentiveLayout()))
        }
        if (AppConfigManager.getInstance().enableTokokoDownload()) {
            setState(State.ShowTokokoDownloadLayout(transactionCount > 0))
        }
        if (ReferralPrefManager.getInstance().enablePaymentReferral()) {
            setState(State.ShowPaymentReferral(transactionCount > 0))
        }
        setState(State.ShowBukuPay(AppConfigManager.getInstance().bukuPayLogoActive()))
        bookEntity = businessUseCase.getCurrentBusiness()
        bookEntity?.let {
            if (it.enabledPayment == 0) {
                enablePayment(it.bookId, it.businessName)
            }
            //TODO change the fn accordingly
        }
        val showPpobBanner = appConfigManager.enablePpobBanner && transactionCount >= 0
        val phoneWithCountryCode =
            "${SessionManager.getInstance().countryCode}-${SessionManager.getInstance().userId}"
        setState(
            State.SetProfileData(
                transactionCount,
                bookEntity,
                AppConfigManager.getInstance().hasPendingPayment(),
                phoneWithCountryCode.replace("++", "+"),
                SessionManager.getInstance().hasEditCard(),
                showPpobBanner
            )
        )
        if (FeaturePrefManager.getInstance()
                .hasBankAccount(SessionManager.getInstance().businessId)
        ) {
            setState(
                State.HandlePaymentVisibility(
                    true,
                    FeaturePrefManager.getInstance().paymentTabEnabled(),
                    transactionCount > 0
                )
            )
        } else if (!SessionManager.getInstance().isGuestUser) {
            getMerchantBankAccounts(bookEntity?.bookId, transactionCount > 0)
        } else {
            setState(State.HandlePaymentVisibility(false, false, transactionCount > 0))
        }
        if (!FeaturePrefManager.getInstance()
                .paymentTabEnabled() && !Utility.isBlank(SessionManager.getInstance().businessId)
        ) getPaymentSummary()
        stockTabState =
            stockConfigUseCase.getStockTabEnabledFromSettings() == stockConfigUseCase.STOCK_SELECTED
        setState(State.StockTabInitialVisibility(stockTabState))

        autoRecordTabState =
            autoRecordUseCase.getAutoRecordTabEnabledFromSettings() == autoRecordUseCase.AUTO_RECORD_SELECTED
        setState(State.AutoRecordTabInitialVisibility(autoRecordTabState))
    }

    private fun getReferralData() = viewModelScope.launch {
        try {
            val referralData = profileUseCase.getReferralData()
            referralData?.let {
                setState(
                    State.OnReferralDataLoaded(it)
                )
            } ?: kotlin.run {
                State.OnReferralDataLoaded(ReferralDataResponse(null,null))
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    private fun handleOnSetProfileDetails() = launch {

        //TODO get user profile
//        businessUseCase.getUserProfile()

    }

    private fun getMerchantBankAccounts(bookId: String?, hasTransaction: Boolean) = launch {
        bookId ?: return@launch
        when (val result = paymentUseCase.getMerchantBankAccounts(bookId)) {
            is ApiSuccessResponse -> {
                val size = result.body?.size ?: 0
                FeaturePrefManager.getInstance()
                    .setHasBankAccount(size > 0, SessionManager.getInstance().businessId)
                setState(
                    State.HandlePaymentVisibility(
                        size > 0,
                        FeaturePrefManager.getInstance().paymentTabEnabled(),
                        hasTransaction
                    )
                )
            }
            else -> setState(
                State.HandlePaymentVisibility(
                    false,
                    FeaturePrefManager.getInstance().paymentTabEnabled(),
                    hasTransaction
                )
            )
        }
    }

    private fun enablePayment(bookId: String, bookName: String) = launch {
        when (paymentUseCase.enableMerchantPayments(bookId, bookName)) {
            is ApiSuccessResponse -> businessUseCase.enablePayments(bookId)
            else -> {
            }
        }
    }

    private fun getPaymentSummary() = launch {
        when (val result =
            finproUseCase.getPaymentSummary(SessionManager.getInstance().businessId)) {
            is ApiSuccessResponse -> setState(State.SetSummaryData(result.body))
            else -> {}
        }
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    fun isLendingEligible() = launch {
        val showLendingBanner = AppConfigManager.getInstance().showLendingBanner()
        if (showLendingBanner != AppConst.STALE_DATA) {
            setState(State.ShowBanner(showLendingBanner == 1))
            return@launch
        }
        when(losUseCase.isLendingEligible()) {
            is ApiSuccessResponse,
            is ApiEmptyResponse  -> {
                AppConfigManager.getInstance().setShowLendingBanner(1)
                setState(State.ShowBanner(true))
            }
            else -> {
                AppConfigManager.getInstance().setShowLendingBanner(0)
                setState(State.ShowBanner(false))
            }
        }
    }

    fun getUserProfile(userId: String) : LiveData<UserProfileEntity>  {
        return businessUseCase.getUserProfile(userId)
    }


    fun setUserProfile(userProfileEntity: UserProfileEntity) : Long  {
        EntityHelper.fillMetadata(userProfileEntity)
        return businessUseCase.setUserProfile(userProfileEntity)
    }

    fun getBusinessByIdSync(businessId: String): BookEntity {
        return businessUseCase.getBusinessByIdSync(businessId)
    }

    fun getCurrentBusiness(): BookEntity? {
        return businessUseCase.getCurrentBusiness()
    }

    fun saveUserProfileToRemote(userProfileEntity: UserProfileEntity) = launch {
        userProfileEntity.clientId = BukuWarungKeys.clientId.orEmpty()
        userProfileEntity.clientSecret =  BukuWarungKeys.clientSecret.orEmpty()
        if(userProfileEntity.dateOfBirth.isNotNullOrEmpty()
            && userProfileEntity.userName.isNotNullOrEmpty()
            && userProfileEntity.userEmail.isNotNullOrEmpty()
            && userProfileEntity.userPhone.isNotNullOrEmpty()){
            FeaturePrefManager.getInstance().hasCompleteUserProfile(true);
            setState(State.RefreshState)
        }else{
            FeaturePrefManager.getInstance().hasCompleteUserProfile(false);
            setState(State.RefreshState)
        }
        when (val result = profileUseCase.setUserProfile(userProfileEntity)) {
            is ApiSuccessResponse -> {
                userProfileEntity.dirty = 0
                setUserProfile(userProfileEntity)
            }
            is ApiErrorResponse -> {
                    userProfileEntity.dirty = 1
                    setUserProfile(userProfileEntity)
            }
            else -> {}
        }

    }

    fun getUserProfileFromRemote() = launch {

        when (val result = profileUseCase.getUserProfile()) {
            is ApiSuccessResponse -> {
                setUserProfile(result.body)
            }
            is ApiErrorResponse -> {
            }
            else -> {}
        }

    }

    private fun getProfileTierInfo() = viewModelScope.launch{
        Log.d("TIER", "fetching the tier info...")

        try {
            val tier = profileUseCase.getProfileTier()
            if (tier != null) {
                tier.let {
                    setState(
                        State.OnProfileTierLoaded(
                            it.loyaltyAccount,
                            it.loyaltyAccountTier.loyaltyTier,
                            it.isWhitelisted
                        )
                    )
                }
            }
        }catch(ex: Exception){
            ex.recordException()
        }
    }

    private fun getSaldoBonusInfo() = viewModelScope.launch {
        Log.d("SALDO", "fetching the Komisi Agen info...")

        try {
            val tier = profileUseCase.getProfileTier()
            val saldoBonus = profileUseCase.getSaldoBonus()
            if (tier != null) {
                if (saldoBonus != null) {
                    saldoBonus.let {
                        setState(
                            State.OnSaldoBonusLoaded(
                                it,
                                tier.loyaltyAccount,
                                tier.loyaltyAccountTier.loyaltyTier,
                                tier.isWhitelisted,
                                tier.loyaltyAccountTier.loyaltyTierBenefits[0]
                            )
                        )
                    }
                }
            }

        } catch(ex: Exception){
            ex.recordException()
        }
    }


    private fun sendCompletionRequest(request: CompletionRequest) = viewModelScope.launch {
        try {
            profileUseCase.sendCompletionRequest(request)
        } catch (e: java.lang.Exception) {
            e.recordException()
        }
    }

    fun updateExistingBooktype(bookId: String, bookType: Int, bookTypeName: String) = launch {
        businessUseCase.updateExistingBook(bookId, bookType, bookTypeName)
    }

    private fun fetchBnplDetails() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val isUserBnplWhitelisted = SessionManager.getInstance().isBnplUserWhiteListed
                if (isUserBnplWhitelisted != AppConst.STALE_DATA) {
                    withContext(Dispatchers.Main) {
                        if (isUserBnplWhitelisted == 1) {
                            setState(State.ShowBnplEntryPoint)
                        } else {
                            setState(State.HideBnplEntryPoint)
                        }
                    }
                    return@withContext
                }
                when(val result = losUseCase.getBnplUserDetails()) {
                    is ApiSuccessResponse -> {
                        try {
                            val isWhitelisted: Boolean = result.body.data[0].whitelisted
                            SessionManager.getInstance().isBnplUserWhiteListed = if (isWhitelisted) 1 else 0
                            if (isWhitelisted) {
                                withContext(Dispatchers.Main) {
                                    setState(State.ShowBnplEntryPoint)
                                }
                            }
                        } catch (ex: java.lang.Exception) {
                            withContext(Dispatchers.Main) {
                                setState(State.HideBnplEntryPoint)
                            }
                            FirebaseCrashlytics.getInstance().recordException(ex)
                        }
                    }
                    else -> {
                        withContext(Dispatchers.Main) {
                            setState(State.HideBnplEntryPoint)
                        }
                    }
                }
            }
        }
    }

}
