package com.bukuwarung.activities.profile.update

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.profile.ProfilePins
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.listen
import com.bukuwarung.databinding.UserProfileOptionItemBinding

class UserProfileOptionsAdapter(val pins:List<ProfilePins>, val getSubMenuItem: (Int) -> Unit) : RecyclerView.Adapter<UserProfileOptionsAdapter.PinViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PinViewHolder {
        val binding =
            UserProfileOptionItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PinViewHolder(binding).listen { pos, type ->
            getSubMenuItem(pos)
        }
    }


    override fun onBindViewHolder(holder: PinViewHolder, position: Int) {
        holder.bind(pins[position])
    }

    override fun getItemCount(): Int = pins.size

    class PinViewHolder constructor(private val binding: UserProfileOptionItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(pin: ProfilePins) {
            with(binding) {
                if(pin.readabaleName.equals("business_profile")){
                    if(FeaturePrefManager.getInstance().hasCompleteBusinessProfile()){
                        ivCompletionCheck.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("business_profile_status","complete")
                    }else{
                        ivCompletionCheck.visibility = View.GONE
                        AppAnalytics.setUserProperty("business_profile_status","partial")
                    }
                }else if(pin.readabaleName.equals("user_profile")){
                    if(FeaturePrefManager.getInstance().hasCompleteUserProfile()){
                        ivCompletionCheck.visibility = View.VISIBLE
                    }else{
                        ivCompletionCheck.visibility = View.GONE
                    }

                }else if(pin.readabaleName.equals("bank_profile")){
                    if(FeaturePrefManager.getInstance().hasBankAccount(SessionManager.getInstance().businessId)){
                        ivCompletionCheck.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("bank_account_saved","true")
                    }else{
                        ivCompletionCheck.visibility = View.GONE
                        AppAnalytics.setUserProperty("bank_account_saved","false")
                    }
                }else if(pin.readabaleName.equals("user_kyc")){
                    if(FeaturePrefManager.getInstance().hasCompletedKYC()){
                        ivCompletionCheck.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("kyc_status","VERIFIED")
                    }else{
                        ivCompletionCheck.visibility = View.GONE
                    }

                }else if (pin.readabaleName.equals("brick_integration")){
                    if(FeaturePrefManager.getInstance().isBrickIntegrated() && FeaturePrefManager.getInstance().isAutoRecordEnabled()){
                        ivCompletionCheck.visibility = View.VISIBLE
                    }else{
                        ivCompletionCheck.visibility = View.GONE
                    }
                }
                tvUserProfileOptionItem.text = pin.name
            }
        }
    }
}