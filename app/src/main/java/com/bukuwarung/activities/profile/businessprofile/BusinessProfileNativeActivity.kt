package com.bukuwarung.activities.profile.businessprofile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import com.bukuwarung.R
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.profile.update.EditUserBusinessProfileFragment
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityUserBusinessProfileBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BusinessProfileNativeActivity : BaseActivity() {

    private val businessFormViewModel: DetailedBusinessFormViewModel by viewModels()
    private lateinit var binding: ActivityUserBusinessProfileBinding
    private var bookId : String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(intent.hasExtra(BOOK_ID)){
            bookId = intent.getStringExtra(BOOK_ID) ?: ""
            supportFragmentManager.beginTransaction().add(
                R.id.main_container,
                EditUserBusinessProfileFragment.instance(bookId)).commit()
        }
    }

    override fun setViewBinding() {
        binding = ActivityUserBusinessProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {

    }

    override fun subscribeState() {

    }

    companion object {

        public val BOOK_ID: String = "book_id"

        fun createIntent(origin: Context?, bookId:String): Intent {
            val intent = Intent(origin, BusinessProfileNativeActivity::class.java)
            intent.putExtra(BOOK_ID, bookId)
            return intent
        }
    }
}