package com.bukuwarung.activities.expense.category

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.text.Html
import android.view.Gravity
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.expense.CashTransactionViewModel
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.activities.expense.adapter.CategoryAdapter
import com.bukuwarung.activities.expense.adapter.CategoryDetailAdapter
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.activities.expense.data.CategoryInfo
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ActivitySelectCategoryBinding
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.TooltipBuilder
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip
import javax.inject.Inject

class SelectCategory: BaseActivity() {

    companion object {
        const val TRANSACTION_TYPE = "transaction_type"
        const val TRANSACTION_CATEGORY = "transaction_category"
    }

    private lateinit var binding: ActivitySelectCategoryBinding
    var transactionType = -1
    var trxCategory: String? = null
    lateinit var categoriesToDisplay: ArrayList<Category>

    override fun setViewBinding() {
        binding = ActivitySelectCategoryBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)

        val toolbar = binding.tbCategory

        setUpToolbarWithHomeUp(toolbar)

        var categories: String? = null

        val gson : Gson = GsonBuilder().create()
        var jsonType = object : TypeToken<List<Category?>?>() {}.type

        if (intent.hasExtra(TRANSACTION_TYPE)) {

            transactionType = intent.getIntExtra(TRANSACTION_TYPE, -1)
            if (transactionType != -1) {
                try{
                    categories = RemoteConfigUtils.SelectCategory.getCreditCategoriesNew()
                    categoriesToDisplay = gson.fromJson(categories, jsonType)
                }catch(e : Exception){
                    categories = RemoteConfigUtils.SelectCategory.getCreditCategoriesNewBackUp()
                    categoriesToDisplay = gson.fromJson(categories, jsonType)
                }
                categoriesToDisplay.add(0,com.bukuwarung.activities.expense.data.Category("https://i.ibb.co/GJ32gQT/penjualan.webp","Penjualan",1))

            } else {
                try{
                    categories = RemoteConfigUtils.SelectCategory.getDebitCategoriesNew()
                    categoriesToDisplay = gson.fromJson(categories, jsonType)
                }catch(e : Exception){
                    categories = RemoteConfigUtils.SelectCategory.getDebitCategoriesNewBackUp()
                    categoriesToDisplay = gson.fromJson(categories, jsonType)
                }
            }
        }

        if (intent.hasExtra(TRANSACTION_CATEGORY)) {
            trxCategory = intent.getStringExtra(TRANSACTION_CATEGORY)
        }

        if(transactionType == 1){
            binding.tvToolbarTitle.text = getString(R.string.credit_category_title)
        }
        else{
            binding.tvToolbarTitle.text = getString(R.string.debit_category_title)
        }


        val categoryAdapter = CategoryAdapter(categoriesToDisplay, trxCategory) {
            val data = Intent()
            data.putExtra(NewCashTransactionActivity.CATEGORY, categoriesToDisplay[it].categoryName)
            setResult(RESULT_OK, data)
            finish()
        }

        var categorieDetails: String? = null

        if (transactionType != -1) {
            categorieDetails = RemoteConfigUtils.SelectCategory.getCreditCategoryDetails()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                binding.tvSelectCategoryInfo.text = Html.fromHtml(getString(R.string.category_info_credit), Html.FROM_HTML_MODE_LEGACY)
            } else {
                binding.tvSelectCategoryInfo.text = Html.fromHtml(getString(R.string.category_info_credit))
            }

        } else {
            categorieDetails = RemoteConfigUtils.SelectCategory.getDebitCategoryDetails()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                binding.tvSelectCategoryInfo.text = Html.fromHtml(getString(R.string.category_info_debit), Html.FROM_HTML_MODE_LEGACY)
            } else {
                binding.tvSelectCategoryInfo.text = Html.fromHtml(getString(R.string.category_info_debit))
            }
        }

        if(RemoteConfigUtils.getCategoryUIVariant()==1){
            binding.rvCategoryDetails.visibility = View.VISIBLE
            binding.tvCategoryHeader.visibility = View.VISIBLE
            binding.tvHelpIcon.visibility = View.GONE

            jsonType = object : TypeToken<List<CategoryInfo?>?>() {}.type
            val categoryInfoToDisplay: List<CategoryInfo> = gson.fromJson(categorieDetails, jsonType)

            val categoryDetailsAdapter = CategoryDetailAdapter(categoryInfoToDisplay)

            binding.rvCategoryDetails.layoutManager = LinearLayoutManager(this)

            binding.rvCategoryDetails.adapter = categoryDetailsAdapter

            binding.rvSelectCategory.layoutManager = GridLayoutManager(this, 3)

        }
        else{
            binding.rvSelectCategory.layoutManager = LinearLayoutManager(this)
        }

        binding.rvSelectCategory.adapter = categoryAdapter

        binding.clInfo.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_CATEGORY_EXPLAIN_DETAIL)
            startActivity(Intent(this, SelectCategoryInfo::class.java))
        }

        binding.tvHelpIcon.setOnClickListener {
            val intent = Intent(this, CategoryDescriptionActivity::class.java)
            intent.putExtra(TRANSACTION_TYPE, transactionType)
            startActivity(intent)
        }

        if(!FeaturePrefManager.getInstance().isTxnCategoryMigrationShown && RemoteConfigUtils.getCategoryUIVariant()==2)showCategoryRevampTooltip()

    }

    private fun showCategoryRevampTooltip() {
        FeaturePrefManager.getInstance().isTxnCategoryMigrationShown = true
        var tooltip: SimpleTooltip? = null
        val tooltipBuilder = TooltipBuilder.builder(this)
            .setAnchor(binding.tvHelpIcon)
            .setText(getString(R.string.txn_category_revamp_intro))
            .setGravity(Gravity.BOTTOM)
        tooltip = tooltipBuilder.build()
        tooltip.show()
    }

    override fun subscribeState() {

    }
}