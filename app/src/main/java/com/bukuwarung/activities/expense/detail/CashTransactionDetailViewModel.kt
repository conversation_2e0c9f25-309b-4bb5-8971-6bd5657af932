package com.bukuwarung.activities.expense.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.Application
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.extensions.postEvent
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst.RECORD_IN_DEBT_AND_CASH
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.constants.KycStatusMetadata
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.FinproPayments
import com.bukuwarung.payments.data.model.PaymentAccountingExtras
import com.bukuwarung.payments.data.model.PaymentCollection
import com.bukuwarung.payments.data.model.PaymentCollectionInfo
import com.bukuwarung.payments.data.model.PaymentExtras
import com.bukuwarung.payments.data.model.PpobProductDetail
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isTrue
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject

@HiltViewModel
internal class CashTransactionDetailViewModel @Inject constructor(
        private val cashRepository: CashRepository,
        private val customerRepository: CustomerRepository,
        private val finproUseCase: FinproUseCase,
        private val paymentUseCase: PaymentUseCase,
        private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    val alert: MutableLiveData<Event<String>> = MutableLiveData()

    val bookId: String? = SessionManager.getInstance().businessId
    private var orderResponse: FinproOrderResponse? = null

    private lateinit var transactionData: LiveData<CashTransactionEntity>

    val stateData: MediatorLiveData<CashTransactionDetailState> = MediatorLiveData()
    private fun currentDetailState(): CashTransactionDetailState = stateData.value!!
    private var bankAccountList: List<BankAccount>? = null
    private val eventStatus = MutableLiveData<PaymentEvent>()
    val observeEvent: LiveData<PaymentEvent> = eventStatus
    private val trxEventStatus = MutableLiveData<TransactionEvent>()
    val observeTrxEvent: LiveData<TransactionEvent> = trxEventStatus
    private var tempBankAccount: BankAccount? = null
    private var hasShownOnboarding = false

    private var cashEntity : CashTransactionEntity? = null
    private var _transactionId: String
        get() = savedStateHandle.get<String>("transactionId").orEmpty()
        set(value) = savedStateHandle.set("transactionId", value)

    fun setTransactionId(newTransactionId: String) {
        stateData.removeSource(transactionData)
        this._transactionId = newTransactionId

        transactionData = cashRepository
                .getObservableCashTransactionById(newTransactionId)

        stateData.addSource(
                transactionData
        ) {
            cashEntity = it
            addDataToState(it)
        }
    }

    fun init() {
        getData()
    }

    private fun getData(transactionId: String = this._transactionId) {
        transactionData = cashRepository.getObservableCashTransactionById(transactionId)

        stateData.addSource(transactionData) {
            cashEntity = it
            addDataToState(it)
        }
    }

    private fun addDataToState(cashEntity: CashTransactionEntity?) {
        val currentState = stateData.value ?: CashTransactionDetailState()
        var customer: CustomerEntity? = null
        if (cashEntity?.customerId?.isNotBlank().isTrue) {
            customer = customerRepository.getCustomerById(cashEntity?.customerId)
        }

        val metadata = PaymentPrefManager.getInstance().getPaymentMetadata()
        val needKycAndNotVerified = metadata?.isKycUsers() ?: false && metadata?.kyc != null &&
                (metadata.kyc.status == null || (metadata.kyc.status != KycStatusMetadata.VERIFIED))
        stateData.value = currentState.copy(
            transaction = cashEntity,
            transactingCustomer = customer,
            needKycAndNotVerified = needKycAndNotVerified
        )
        showSmallBannerMessage(cashEntity?.orderId.isNullOrBlank())

        val paymentRequestId = if (PaymentUtils.isPpob(cashEntity?.cashCategoryId)) {
            // ppob
            cashEntity?.cashTransactionId ?: ""
        } else {
            cashEntity?.orderId ?: ""
        }
        if(paymentRequestId.isNotBlank())
            getOrderDetail(paymentRequestId)
        else {
            stateData.value = currentState.copy(
                currentState = if (cashEntity == null) {
                    CashTransactionDetailStateType.NotFound
                } else {
                    CashTransactionDetailStateType.Loaded
                },
                transaction = cashEntity
            )
        }
    }

    private fun getOrderDetail(paymentRequestId: String) = viewModelScope.launch {
        setState(currentDetailState().copy(currentState = if (orderResponse == null) CashTransactionDetailStateType.Loading else CashTransactionDetailStateType.Loaded))
        when (val response = finproUseCase.getOrderDetail(SessionManager.getInstance().businessId, paymentRequestId)) {
            is ApiSuccessResponse -> {
                orderResponse = response.body
                if (orderResponse?.orderId == null) {
                    return@launch
                }
                setPaymentData()
            }
            is ApiErrorResponse -> {
                alert.postEvent(response.errorMessage)
                stateData.value = currentDetailState().copy(currentState = CashTransactionDetailStateType.Loaded)
            }
            else -> {}
        }
    }

    private suspend fun setPaymentData() {
        orderResponse?.run {
            val transaction = transactionData.value
            transaction ?: return
            if(transaction.orderId.isNullOrBlank() || PaymentUtils.isPpob(transaction.cashCategoryId)) {
                setPaymentEvent(PaymentEvent.SetFinproResponse(this))
                stateData.value = currentDetailState().copy(
                    isPpob = true,
                    pulsaUpdatedAt = this.updatedAt,
                    paymentCollection = orderResponse?.paymentCollectionInfo,
                    paymentId = this.transactionId ?: "-",
                    orderId = this.orderId,
                    mobileNumber = this.items?.firstOrNull()?.beneficiary?.number,
                    pdtName = this.items?.firstOrNull()?.name,
                    payment = this.payments?.firstOrNull(),
                    ppobItem = orderResponse?.items?.firstOrNull(),
                    showPaymentError = false,
                    currentState = CashTransactionDetailStateType.Loaded
                )
            } else if (AppConst.BELUM_LUNAS == transactionData.value?.status) {
                val beneficiary = this.items?.firstOrNull()?.beneficiary
                setPaymentEvent(PaymentEvent.ShowPendingPaymentDetail(this.payments?.firstOrNull()?.paymentUrl
                        ?: "",
                        this.transactionId ?: "", this.payments?.firstOrNull()?.expiredAt ?: "",
                        beneficiary?.code, beneficiary?.accountNumber, beneficiary?.name, this))
                stateData.value = currentDetailState().copy(currentState = CashTransactionDetailStateType.Loaded)
            } else {
                stateData.value = currentDetailState().copy(currentState = CashTransactionDetailStateType.Loaded)
            }
        }
    }

    fun shareInvoice(useWhatsapp: Boolean = false) {
        val bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
        if (transactionData.value?.status != AppConst.BELUM_LUNAS) {
            eventStatus.value =
                PaymentEvent.ShareInvoiceUnpaid(bookEntity, useWhatsapp = useWhatsapp)
        } else {
            var prop: AppAnalytics.PropBuilder? = null
            if(transactionData.value?.customerName != null && transactionData.value?.amount != null && orderResponse?.payments?.firstOrNull()?.paymentUrl != null &&
                    orderResponse?.payments?.firstOrNull()?.expiredAt != null) {
                val expiryDate = DateTimeUtils.getDateFromPaymentUTC(orderResponse?.payments?.firstOrNull()?.expiredAt)
                if(expiryDate < Date()) {
                    onCreatePaymentButtonClicked()
                    prop = AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.TRANSACTION_ID, _transactionId)
                            .put(AnalyticsConst.ORDER_ID, transactionData.value?.orderId)
                            .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION)
                            .put(AnalyticsConst.INWARDS_AMOUNT, (transactionData.value?.amount
                                    ?: 0.0).toLong())
                            .put(AnalyticsConst.BANK, tempBankAccount?.bankCode)
                            .put(AnalyticsConst.RETRY, true)
                    eventStatus.value = PaymentEvent.PaymentInCreated(prop)
                    return
                }
                prop = AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.TRANSACTION_ID, _transactionId)
                        .put(AnalyticsConst.ORDER_ID, transactionData.value?.orderId)
                        .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION)
                        .put(AnalyticsConst.INWARDS_AMOUNT, (transactionData.value?.amount
                                ?: 0.0).toLong())
                        .put(AnalyticsConst.BANK, tempBankAccount?.bankCode)
            }
            eventStatus.value = PaymentEvent.ShareInvoiceUnpaid(
                bookEntity,
                transactionData.value?.customerName
                    ?: "",
                Utility.formatAmount(transactionData.value!!.amount),
                orderResponse?.payments?.firstOrNull()?.paymentUrl,
                prop,
                useWhatsapp = useWhatsapp
            )
        }
    }

    fun onCreatePaymentButtonClicked() = viewModelScope.launch {
        if (bankAccountList == null) {
            bankAccountList = paymentUseCase.getLocalMerchantBankAccounts(SessionManager.getInstance().businessId)
        }
        eventStatus.value = PaymentEvent.OnInitialCreatePayment(bankAccountList
                ?: emptyList(), SessionManager.getInstance().businessId)
    }

    fun onRetry() = viewModelScope.launch {
        if(orderResponse != null) {
            setPaymentData()
        } else {
            eventStatus.value = PaymentEvent.ResetPayment
        }
    }

    fun temporarySelectBankAccount(bankAccount: BankAccount?) {
        tempBankAccount = bankAccount
        createInDisbursement()
    }

    private fun getCustomerId(): String {
        val phoneNumber = orderResponse?.items?.firstOrNull()?.beneficiary?.phoneNumber
        val customer = customerRepository.getCustomerByMobileNumber(phoneNumber)
        return customer?.customerId ?: customerRepository.saveCustomer(phoneNumber, phoneNumber)
    }

    fun createInDisbursement(bankAccount: BankAccount? = tempBankAccount) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            val transaction = cashRepository.getSingleRawCashTransaction(_transactionId)
            transaction ?: return@withContext
            bankAccount ?: return@withContext
            tempBankAccount = bankAccount
            if (!currentDetailState().showPaymentLoading) setState(currentDetailState().copy(showPaymentLoading = true))
            val result = paymentUseCase.requestPayment(
                    transaction.bookId,
                    transaction.customerId ?: getCustomerId(),
                    PaymentCollection.newCollectionRequest(
                        amount = transaction.amount.toLong(),
                        referenceId = orderResponse?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.transactionId?:"",
                        bankAccountId = bankAccount.bankAccountId,
                        description = transaction.description,
                        customerName = transaction.customerName?.takeIf { it.isNotNullOrBlank() } ?: orderResponse?.items?.firstOrNull()?.beneficiary?.phoneNumber,
                        extras = PaymentExtras(recordIn = RECORD_IN_DEBT_AND_CASH,
                            accounting = PaymentAccountingExtras(transactionId = _transactionId)
                        )
                    )
            )
            when (result) {
                is ApiSuccessResponse -> {
                    val prop = AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.TRANSACTION_ID, _transactionId)
                            .put(AnalyticsConst.ORDER_ID, result.body.paymentRequestId)
                            .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION)
                            .put(AnalyticsConst.INWARDS_AMOUNT, transaction.amount.toLong())
                            .put(AnalyticsConst.BANK, bankAccount.bankCode)
                            .put(AnalyticsConst.RETRY, false)
                    setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = false))
                    setPaymentEvent(PaymentEvent.PaymentInCreated(prop))
                    AppConfigManager.getInstance().sethasPendingPayment(true)
                }
                is ApiErrorResponse -> setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = true))
                else -> setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = true))
            }
        }
    }

    fun markAsPaid(description: String) {
        val transaction = transactionData.value
        transaction ?: return
        if (transaction.orderId.isNullOrBlank() || PaymentUtils.isPpob(transaction.cashCategoryId)) {
            setTransactionAsPaid(description)
        } else expirePayment(description)
    }

    private fun setTransactionAsPaid(description: String, transactionId: String? = null) {
        val transaction = transactionData.value
        transaction ?: return
        if (PaymentUtils.isPpob(cashEntity?.cashCategoryId)) {
            // ppob
            TransactionRepository.getInstance(Application.getAppContext()).updateCashTransaction(transaction.cashTransactionId,"",null,1,"","")
        }
        TransactionRepository.getInstance(Application.getAppContext()).settleUnpaidCashTransaction(User.getBusinessId(),
            transaction.customerId, transaction.amount, Utility.getStorableDateString(Date()),
            description, 1, transaction.cashTransactionId)
        trxEventStatus.value = TransactionEvent.OnMarkAsPaidFinished(transaction.cashTransactionId, transaction.amount, transactionId, AnalyticsConst.CASH)
    }

    fun deleteAndExpirePayment() {
        if(orderResponse != null && !orderResponse!!.isCompleted()) expirePayment(isDelete = true)
        else eventStatus.value = PaymentEvent.OnTransactionDeleted(transactionData.value?.cashTransactionId)
    }

    private fun expirePayment(description: String? = null, isDelete: Boolean = false) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            val transaction = transactionData.value
            transaction ?: return@withContext
            if (transaction.customerId == null) return@withContext
            if (transaction.orderId.isNullOrBlank()) return@withContext
            setState(currentDetailState().copy(showPaymentLoading = true, showPaymentError = false))
            when (paymentUseCase.expirePayment(transaction.bookId, transaction.customerId!!, transaction.orderId)) {
                is ApiSuccessResponse -> {
                    setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = false))
                    handleExpiredPayment(description, isDelete)
                }
                is ApiErrorResponse -> setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = true))
                else -> setState(currentDetailState().copy(showPaymentLoading = false, showPaymentError = true))
            }
        }
    }

    private suspend fun handleExpiredPayment(description: String? = null, isDelete: Boolean = false)= withContext(Dispatchers.Main) {
        if(!isDelete && description != null)
            setTransactionAsPaid(description)
        else {
            eventStatus.value = PaymentEvent.OnTransactionDeleted(transactionData.value?.cashTransactionId)
        }
    }

    fun checkPaymentTooltip() {
        if (hasShownOnboarding) return
        hasShownOnboarding = true
        if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_LINK_UNPAID_CASH_TO_PAYMENT)) {
            OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.TUTORIAL_LINK_UNPAID_CASH_TO_PAYMENT)
            eventStatus.value = PaymentEvent.ShowPaymentTooltip
        }
    }

    private fun showSmallBannerMessage(show: Boolean) {
        val paymentMetadata = PaymentPrefManager.getInstance().getPaymentMetadata()
        val isPaid = paymentMetadata?.fee?.default ?: 0.0 > 0.0
        eventStatus.value = PaymentEvent.SetPaymentInfo(
            show,
            isPaid,
            Utility.formatAmount(paymentMetadata?.fee?.default)
        )
    }

    private suspend fun setState(state: CashTransactionDetailState) = withContext(Dispatchers.Main) {
        stateData.value = state
    }
    private suspend fun setPaymentEvent(event: PaymentEvent) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }
}

sealed class TransactionEvent {
    data class OnMarkAsPaidFinished(val transactionId: String?, val amount: Double?, val orderId: String?, val mode: String) : TransactionEvent()
}

sealed class PaymentEvent {
    data class OnInitialCreatePayment(val list: List<BankAccount>, val bookId: String?) : PaymentEvent()
    data class ShareInvoiceUnpaid(
        val bookEntity: BookEntity?,
        val customerName: String? = null,
        val amount: String? = null,
        val url: String? = null,
        val prop: AppAnalytics.PropBuilder? = null,
        val useWhatsapp: Boolean = false
    ) : PaymentEvent()
    data class ShowPendingPaymentDetail(val url: String, val paymentId: String, val expiredAt: String?, val bankName: String?,
                                        val accountNumber: String?, val accountName: String?,
                                        val orderResponse: FinproOrderResponse) : PaymentEvent()
    object ResetPayment: PaymentEvent()
    object ShowPaymentTooltip: PaymentEvent()
    data class OnTransactionDeleted(val transactionId: String?): PaymentEvent()
    data class PaymentInCreated(val prop: AppAnalytics.PropBuilder): PaymentEvent()
    data class SetFinproResponse(val response: FinproOrderResponse): PaymentEvent()
    data class SetPaymentInfo(val show: Boolean, val isPaid: Boolean, val fee: String): PaymentEvent()
}

data class CashTransactionDetailState(
    val amount: Double? = null,
    val fee: Double? = null,
    val transaction: CashTransactionEntity? = null,
    val transactingCustomer: CustomerEntity? = null,
    val currentState: CashTransactionDetailStateType = CashTransactionDetailStateType.Loading,
    val paymentId: String = "",
    val orderId: String? = "",
    val pulsaUpdatedAt: String? = "",
    val pdtName: String? = "",
    val mobileNumber: String? = "",
    val payment: FinproPayments? = null,
    val showPaymentLoading: Boolean = false,
    val showPaymentError: Boolean = false,
    val isPpob: Boolean = false,
    val ppobItem: PpobProductDetail? = null,
    val needKycAndNotVerified: Boolean = false,
    val paymentCollection: PaymentCollectionInfo? = null
)

enum class CashTransactionDetailStateType {
    Loading,
    Loaded,
    NotFound
}