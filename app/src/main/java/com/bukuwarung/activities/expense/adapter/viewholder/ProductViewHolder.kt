package com.bukuwarung.activities.expense.adapter.viewholder

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ItemProductBinding
import com.bukuwarung.databinding.ItemSellingProductBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getColorCompat

class ProductViewHolder(private val binding: ViewBinding) : RecyclerView.ViewHolder(binding.root) {
    fun bind(
        dataHolder: ProductDataHolder,
        isInventoryEnabled: Boolean,
        isExpense: Boolean,
        checkCallback: (ProductDataHolder, ProductViewHolderEvent) -> Unit
    ) {
        when (binding) {
            is ItemSellingProductBinding -> bindSelling(binding, dataHolder, checkCallback)
            is ItemProductBinding -> bindExpense(binding, dataHolder)
        }
    }

    private fun bindExpense(
        binding: ItemProductBinding,
        dataHolder: ProductDataHolder
    ) {
        with(binding) {
            if (dataHolder.productEntity?.trackInventory == AppConst.INVENTORY_TRACKING_DISABLED) {
                tvStock.visibility = View.GONE
            }

            tvProductName.text = dataHolder.productEntity?.name

            numberStepper.setup(checkbox, dataHolder) { product ->
                product ?: return@setup
            }

            tvProductName.setOnClickListener { numberStepper.toggleChecked() }

            tvStock.setTextColor(root.context.getColorCompat(R.color.black_80))
            tvStock.text = root.resources.getString(R.string.product_stock_placeholder)
                .format(Utility.getRoundedOffPrice(dataHolder.productEntity?.stock), " ")
            val stockUnit =
                "${root.resources.getString(R.string.forward_slash)}${dataHolder.productEntity?.measurementName}"
            tvStockUnit.text = stockUnit
        }
    }

    private fun bindSelling(
        binding: ItemSellingProductBinding,
        dataHolder: ProductDataHolder,
        checkCallback: (ProductDataHolder, ProductViewHolderEvent) -> Unit
    ) {
        with(binding) {
            sellingPriceTxt.text = Utility.formatCurrency(dataHolder.productEntity?.unitPrice)
            sellingPriceCurrencyTxt.text = Utility.getCurrency()
            tvEdit.setOnClickListener { checkCallback(dataHolder, ProductViewHolderEvent.Edit) }
            tvEdit.visibility = if (dataHolder.isChecked) View.VISIBLE else View.GONE

            if (dataHolder.productEntity?.trackInventory == AppConst.INVENTORY_TRACKING_DISABLED) {
                tvStock.visibility = View.GONE
            }

            tvProductName.text = dataHolder.productEntity?.name

            numberStepper.setup(checkbox, dataHolder) { product ->
                product ?: return@setup
                checkCallback(product, ProductViewHolderEvent.Selection)
                dataHolder.isEditShow = checkbox.isChecked
            }

            tvProductName.setOnClickListener { numberStepper.toggleChecked() }

            tvStock.setTextColor(root.context.getColorCompat(R.color.black_80))
            tvStock.text = root.resources.getString(R.string.product_stock_placeholder)
                .format(Utility.getRoundedOffPrice(dataHolder.productEntity?.stock), " ")
            val stockUnit =
                "${root.resources.getString(R.string.forward_slash)}${dataHolder.productEntity?.measurementName}"
            tvStockUnit.text = stockUnit
        }
    }
}

class ProductDividerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

enum class ProductViewHolderEvent {
    Selection, Edit, Delete
}