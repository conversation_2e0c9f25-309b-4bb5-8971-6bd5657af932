package com.bukuwarung.activities.expense.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.CategoryInfo
import com.bukuwarung.databinding.ItemCategoryDetailsBinding

class CategoryDetailAdapter(val categoryInfo: List<CategoryInfo>): RecyclerView.Adapter<CategoryDetailAdapter.CategoryDetailViewHolder>()  {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryDetailViewHolder {
        val binding = ItemCategoryDetailsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryDetailViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CategoryDetailAdapter.CategoryDetailViewHolder, position: Int) {
        holder.bind(categoryInfo[position])
    }

    override fun getItemCount(): Int {
        return categoryInfo.size
    }

    inner class CategoryDetailViewHolder(private val binding: ItemCategoryDetailsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(categoryInfo: CategoryInfo) {
            with(binding) {
                tvCategoryName.text = categoryInfo.categoryName
                tvCategoryDetails.text = categoryInfo.categoryDescription
            }
        }
    }
}