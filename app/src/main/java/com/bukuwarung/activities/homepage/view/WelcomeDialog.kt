package com.bukuwarung.activities.homepage.view

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.databinding.DialogHomepageWelcomeBinding

class WelcomeDialog(context: Context, private val callback:() -> Unit): BaseDialog(context, BaseDialogType.POPUP) {

    init {
        setUseFullWidth(false)
        setCancelable(true)
    }

    private val binding by lazy {
        DialogHomepageWelcomeBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.btnOk.setOnClickListener {
            callback()
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.ACTION, AnalyticsConst.PROCEED)
            AppAnalytics.trackEvent(AnalyticsConst.HOMEPAGE_INTRO_BANNER_CLICK, prop)
            dismiss()
        }
        this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 40f)
                }
            }
            binding.clWelcomeDialog.outlineProvider = provider
            binding.clWelcomeDialog.clipToOutline = true
        }
    }

}