package com.bukuwarung.activities.homepage.view

import android.animation.ValueAnimator
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.animation.doOnPause
import androidx.fragment.app.viewModels
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.LayoutHomeBnplFragmentBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.deeplink.handler.OrderHistorySignalHandler
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.ppob.confirmation.view.SaldoFreezeBottomSheet
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomeBnplFragment : BaseFragment(), Navigator {

    private var homeSaldoBukumodalAdapter: HomeSaldoBukumodalAdapter? = null
    private var runnable: Runnable? = null
    private val handler = Handler()

    companion object {
        private const val BNPL_HOMEPAGE_SOURCE = "&source=ANDROID_SALDO_TALANGIN_DULU_ICON"
        private const val BNPL_PEMBAYARAN_SOURCE = "&source=PAYMENT_SALDO_TALANGIN_DULU_ICON"
        private const val FROM = "from"
        fun createIntent(from: String): HomeBnplFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
            }
            return HomeBnplFragment().apply {
                arguments = bundle
            }
        }
    }

    private val viewModel: PaymentTabViewModel by viewModels()

    @Inject
    lateinit var neuro: Neuro

    private var _binding: LayoutHomeBnplFragmentBinding? = null
    private val binding get() = _binding!!

    private val from by lazy { arguments?.getString(FROM).orEmpty() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LayoutHomeBnplFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        with(binding){
            if (from == AnalyticsConst.PEMBAYARAN) tvSupportedBanks.hideView()
            layoutSaldoTile.slLayout.startShimmer()
            layoutSaldoTile.clCard.hideView()
            baruTag.root.hideView()

            vpEntryPoint.isNestedScrollingEnabled = false
            vpEntryPoint.apply {
                homeSaldoBukumodalAdapter =
                    HomeSaldoBukumodalAdapter(layoutSaldoTile.root.layoutParams.height) { position ->
                        kotlin.run {
                            onItemClick(position)
                        }
                    }
                vpEntryPoint.adapter = homeSaldoBukumodalAdapter
            }
            TabLayoutMediator(
                tbEntryPoint,
                vpEntryPoint,
            ) { tab, position ->
            }.attach()
            vpEntryPoint.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    runnable = Runnable { vpEntryPoint.currentItem = (position + 1) % homeSaldoBukumodalAdapter!!.itemCount }
                    if (RemoteConfigUtils.getBnplEntryPointAutoRotateDuration() > 0) {
                        runnable?.let {
                            handler.postDelayed(it, RemoteConfigUtils.getBnplEntryPointAutoRotateDuration())
                        }
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {
                    super.onPageScrollStateChanged(state)
                    if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                        handler.removeMessages(0)
                    }
                }

            })
            vpEntryPoint.offscreenPageLimit = 1
            context?.let {
                val itemDecoration = VerticalMarginItemDecorator(
                    it,
                    R.dimen._12dp,
                    R.dimen._12dp
                )
               vpEntryPoint.addItemDecoration(itemDecoration)
            }

        }
        viewModel.checkSaldoBalance()
        viewModel.fetchBnplInfo()
    }

    private fun onItemClick(position: Int) {
        if (position == 0) {
            startActivity(
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getBukuModalUrl(),
                    ""
                )
            )
        } else {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT, from)
                put(AnalyticsConst.HOMEPAGE_SECTION, AnalyticsConst.DRAWER)
                put(AnalyticsConst.HomePage.BUTTON_NAME, AnalyticsConst.SALDO_TALANGIN_DULU)
            }
            AppAnalytics.trackEvent(AnalyticsConst.HomePage.EVENT_HOMEPAGE_BUTTON_CLICK, prop)
            val source =
                if (from == AnalyticsConst.HOME_PAGE) BNPL_HOMEPAGE_SOURCE else BNPL_PEMBAYARAN_SOURCE
            startActivity(
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getBnplUrl() + source,
                    ""
                )
            )
        }
    }

    override fun subscribeState() = with(binding) {
        subscribeSingleLiveEvent(viewModel.saldoState) { state ->
            when (state) {
                is PaymentTabViewModel.State.SaldoState -> {
                    if (!state.fetching) {
                        homeSaldoBukumodalAdapter?.updateUi()
                        layoutSaldoTile.apply {
                            slLayout.stopShimmer()
                            slLayout.hideView()
                            clCard.showView()
                            ivIcon.setImageResource(R.drawable.ic_saldo_icon_home)
                            tvItemTitle.text = getString(R.string.saldo_bukuwarung)
                        }
                        when {
                            state.isSaldoFreeze -> setSaldoFreezeUi(state.saldoData?.subBalance?.saldo)
                            !state.error -> setSaldoUi(
                                state.saldoData?.subBalance?.saldo,
                                state.saldoData?.subBalance?.cashback
                            )
                            else -> setSaldoErrorUi()
                        }
                    }
                }
                else -> {}
            }
        }
        subscribeSingleLiveEvent(viewModel.bnplState) { state ->
            when (state) {
                is PaymentTabViewModel.State.BnplInfo -> {
                    SessionManager.getInstance()
                        .setBnplInfo(state.isRegistered, state.bnplCurrentLimit)
                    baruTag.root.visibility = (!state.isRegistered).asVisibility()
                    if (state.isRegistered) {
                        homeSaldoBukumodalAdapter?.updateIsBnplRegistered(state.bnplCurrentLimit)
                    } else {
                        homeSaldoBukumodalAdapter?.updateIsBnplNotRegistered()
                    }
                }
                is PaymentTabViewModel.State.BnplError -> {
                    homeSaldoBukumodalAdapter?.updateSaldoErrorState()
                }
                else -> {}
            }
        }
    }

    private fun initialiseSaldoAnimation() {
        val animatorSaldo = ValueAnimator.ofFloat(0.0f, 1.0f)
        with(binding.layoutSaldoTile) {
            animatorSaldo.repeatMode = ValueAnimator.REVERSE
            animatorSaldo.repeatCount = ValueAnimator.INFINITE
            animatorSaldo.interpolator = LinearInterpolator()
            animatorSaldo.duration = AppConst.ANIMATION_TIME
            tvBlueFont.hideView()
            animatorSaldo.addUpdateListener { animation ->
                val progress = (animation.animatedValue as Float)
                val height = tvBlackFont.height
                val translationY = height * progress
                if (animation.currentPlayTime % animation.duration / 12 == 0L) {
                    animation.pause()
                }
                tvBlackFont.translationY = translationY
                tvBlueFont.translationY = translationY - height
            }
            animatorSaldo.doOnPause {
                Handler(Looper.getMainLooper()).postDelayed({
                    animatorSaldo.resume()
                    tvBlueFont.showView()
                }, AppConst.ANIMATION_STOP_TIME)
            }
        }
        if (from != AnalyticsConst.HOME_PAGE) animatorSaldo.start()
        setTopUpSaldoClickListener()
        setSaldoClickListener()
        setCashbackClickListener()
    }

    private fun setTopUpSaldoClickListener() {
        binding.layoutSaldoTile.ivIsiSaldo.setSingleClickListener {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ENTRY_POINT, from)
                put(AnalyticsConst.WALLET, AnalyticsConst.SALDO)
                put(AnalyticsConst.CURRENT_WALLET_BALANCE, viewModel.viewState.value?.saldoBalance.orNil)
            }
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, prop)
            Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, "wallet_top_up_home_bnpl")
            redirect(
                "${SaldoSignalHandler.saldoLink}?${AnalyticsConst.ENTRY_POINT}=${AnalyticsConst.PAYMENTS}"
            )
        }
    }

    private fun setSaldoClickListener() {
        binding.layoutSaldoTile.tvBlackFont.setSingleClickListener {
            redirect(
                OrderHistorySignalHandler.getOrdersLink(
                    PaymentConst.TYPE_SALDO_TAB,
                    PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS.toString()
                )
            )
        }
    }

    private fun setCashbackClickListener() {
        binding.layoutSaldoTile.tvBlueFont.setSingleClickListener {
            redirect(
                OrderHistorySignalHandler.getOrdersLink(
                    PaymentConst.HISTORY_TABS.SALDOBONUS.toString(),
                    PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS.toString()
                )
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.layoutSaldoTile.slLayout.stopShimmer()
        _binding = null
    }

    private fun redirect(redirection: String) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "${AppConst.DEEPLINK_INTERNAL_URL}?type=act&data=$redirection&from=home&launch=1"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    private fun setSaldoFreezeUi(saldoBalance: Double?) = with(binding.layoutSaldoTile) {
        tvBlackFont.text = Utility.formatAmount(saldoBalance)
        tvBlackFont.setDrawable(right = R.drawable.ic_lock_black)
        ivIsiSaldo.setImageResource(R.drawable.ic_info_red)
        ivIsiSaldo.setSingleClickListener {
            SaldoFreezeBottomSheet.createInstance()
                .show(parentFragmentManager, SaldoFreezeBottomSheet.TAG)
        }
    }

    private fun setSaldoUi(saldoBalance: Double?, saldoCashback: Double?) =
        with(binding.layoutSaldoTile) {
            tvBlackFont.text = Utility.formatAmount(saldoBalance)
            tvBlackFont.setDrawable(right = 0)
            ivIsiSaldo.setImageResource(R.drawable.ic_saldo_topup_plus)
            tvBlueFont.text = getString(R.string.saldo_bonus_x, Utility.formatAmount(saldoCashback))
            initialiseSaldoAnimation()
        }

    private fun setSaldoErrorUi() = with(binding.layoutSaldoTile) {
        tvBlackFont.hideView()
        tvBlueFont.hideView()
        tvFailureText.showView()
        ivIsiSaldo.hideView()
    }
}