package com.bukuwarung.activities.homepage.view

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Point
import android.os.Build
import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import android.widget.ImageView
import android.widget.ScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.activities.edc.orderdetail.viewmodel.EdcOrderDetailViewModel
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.homepage.data.HomeFabConfig
import com.bukuwarung.activities.homepage.data.HomeFabConfigType
import com.bukuwarung.activities.homepage.viewmodel.HomePageViewModel
import com.bukuwarung.activities.notification.NotificationActivity
import com.bukuwarung.activities.onboarding.helper.onboarding.OnBoardingRedirection
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.commonview.view.BukuTileView
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.BD
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.HOME_PAGE
import com.bukuwarung.constants.AnalyticsConst.ID
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.AppNotification
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentHomeBinding
import com.bukuwarung.domain.auth.BureauUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.game.viewmodel.GameViewModel
import com.bukuwarung.managers.local_notification.LocalNotificationData
import com.bukuwarung.managers.local_notification.LocalNotificationIcon
import com.bukuwarung.managers.local_notification.LocalNotificationManager
import com.bukuwarung.managers.local_notification.LocalNotificationStyle
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.constants.KycTier
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.saldo.SaldoTopupRestrictDialog
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.referral.usecase.ReferralUseCase
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class HomeFragment : BaseFragment(), OnboardingWidget.OnboardingWidgetListener, Navigator {

    lateinit var binding: FragmentHomeBinding

    val scope = MainScope()

    @Inject
    lateinit var bureauUseCase: BureauUseCase

    private val viewModel: HomePageViewModel by viewModels()

    lateinit var data: List<FragmentBlock>

    private val paymentTabViewModel: PaymentTabViewModel by viewModels()

    @Inject
    lateinit var referralUseCase: ReferralUseCase

    private val gameViewModel: GameViewModel by viewModels()

    private val edcOrderDetailViewModel: EdcOrderDetailViewModel by viewModels()

    @Inject
    lateinit var paymentUseCase: PaymentUseCase

    @Inject
    lateinit var neuro: Neuro

    var nextSection: Int? = null

    var isKYCExluded: Boolean = false

    var isBusinessDashboardEnabled = false

    private val homeFabConfigBody by lazy { RemoteConfigUtils.getHomePageFabConfig() }

    interface HomeFragmentBackListener {
        fun handleNormalBackPressedFromHome()
    }

    private var listener: HomeFragmentBackListener? = null
    private var onBoardingCampaignFragment: HomeOnBoardingCampaignWidgetFragment?=null

    private val fragmentPool = mutableListOf<Fragment>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AuthHelper.refreshUserSession()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHomeBinding.inflate(layoutInflater, container, false)


        showSaldoTopUpInfo()
        return binding.root
    }

    private fun initializeView() {

        val activity = activity
        if (activity != null) {
            val mainActivity = activity as MainActivity
            mainActivity.setSupportActionBar(binding.tbHomepage)
            val supportActionBar = mainActivity.supportActionBar
            supportActionBar!!.setDisplayShowTitleEnabled(false)
            supportActionBar.setDisplayHomeAsUpEnabled(true)
            supportActionBar.setHomeButtonEnabled(true)
            supportActionBar.setHomeAsUpIndicator(R.mipmap.ic_menu_white_24dp)

            binding.tbHomepage.setNavigationOnClickListener {
                if (activity != null) {
                    activity.handleSideMenuIconClick()
                }
            }
        }

        getLoyaltyGameTypeName(RemoteConfigUtils.getOnBoardingCampaignGameType())
        val businessId = BusinessRepository.getInstance(requireContext())
            .getBusinessByIdSync(User.getBusinessId())

        var bookName = "Usaha Saya"

        businessId?.let {
            bookName = businessId.businessName.orEmpty()
        }

        binding.clHome.setTag(R.id.hansel_ignore_view_excluding_children, true)

        binding.tvToolbarTitle.text = bookName

        var loyaltyWidgetType = "new"
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            loyaltyWidgetType = "old"
        }

        if (RemoteConfigUtils.getBellBannerTab() == TabName.HOME.name) {

            binding.notificationIcon.visibility = View.VISIBLE
            if (!FeaturePrefManager.getInstance().hasUnseenNotification()) {
                binding.notifyHighlighterExp.setVisibility(View.GONE);
            } else {
                try {
                    val notificationList =
                        BusinessRepository.getInstance(context).notificationsList;
                    val filteredNotificationList = mutableListOf<AppNotification>()
                    for (appNotification: AppNotification in notificationList) {
                        filteredNotificationList.add(appNotification);
                    }

                    if (filteredNotificationList.isNotEmpty()) {
                        binding.notifyHighlighterExp.visibility = View.VISIBLE;
                        binding.notifyHighlighterExp.text = filteredNotificationList.size.toString()
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
            binding.notificationIcon.setOnClickListener {
                val prop = AppAnalytics.PropBuilder()
                prop.put(ENTRY_POINT2, HOME_PAGE)
                AppAnalytics.trackEvent(AnalyticsConst.OPEN_NOTIFICATION, prop)
                FeaturePrefManager.getInstance().setUnseenNotification(false)
                binding.notifyHighlighterExp.visibility = View.GONE
                startActivity(Intent(context, NotificationActivity::class.java))
            }

        } else {
            binding.notifyHighlighterExp.visibility = View.GONE;
            binding.notificationIcon.visibility = View.GONE
        }

        var homePageJson: String = RemoteConfigUtils.NewHomePage.getHomepageBlock()
        val type = object : TypeToken<List<FragmentBlock>>() {}.type
        val gson: Gson = GsonBuilder().create()
        var homeData: List<FragmentBlock> = listOf()

        try {
            homeData = gson.fromJson(homePageJson, type)
        } catch (e: JsonSyntaxException) {
            homePageJson = RemoteConfigUtils.NewHomePage.getHomePageFailSafeBlock()
            homeData = gson.fromJson(homePageJson, type)
        }

        val isKycCompleted = FeaturePrefManager.getInstance()
            .hasCompletedKYC() && FeaturePrefManager.getInstance().paymentTransactionCount > 0

        val versionCode = BuildConfig.VERSION_CODE

        data =
            homeData.filter { it.start_version!! <= versionCode && (it.end_version!! >= versionCode || it.end_version == -1) }
                .sortedBy { it.rank }

        if (isKycCompleted) {
            isKYCExluded = true
            data = data.filter { !it.analytics_block_name?.contains("kyc_cta_clicked", true)!! }
        }

        val fragmentManager = activity?.supportFragmentManager
        var transaction: FragmentTransaction?
        if (RemoteConfigUtils.NewHomePage.isHomeTooLargeFixEnabled()) {
            fragmentManager?.commit {
                for (fragment in fragmentPool) {
                    remove(fragment)
                }
            }
        }
        fragmentPool.clear()

        val linearLayout = binding.frameHomepage
        if (linearLayout.childCount > 0) {
            linearLayout.removeAllViews()
        }

        for (fragmentData: FragmentBlock in data) {
            binding.apply {
                transaction = fragmentManager?.beginTransaction()
                var fragment = Fragment()
                val bodyBlock = fragmentData.body_block_name

                var bodyContent: FragmentBodyBlock? = null

                bodyBlock?.let {
                    bodyContent = getBodyContents(fragmentData.body_block_name)
                }

                when (fragmentData.layout_type) {
                    -1 -> {
                        fragment = Fragment()
                    }
                    -2 -> {
                        fragment = HomeTickerFragment.createIntent()
                    }
                    0 -> {
                        fragment = HomeTopBlueFragment.createIntent(bodyContent!!, fragmentData)
                    }
                    1 -> {
                        fragment =
                            HomePaymentsBannerFragment.createIntent(bodyContent!!, fragmentData)
                    }
                    2 -> {
                        fragment = BukuTileView.createIntent(bodyContent!!, fragmentData)
                    }
                    3 -> {
                        fragment =
                            HomeLendingBannerFragment.createIntent(bodyContent!!, fragmentData)
                    }
                    4 -> {
                        fragment = HomeHelpSectionFragment.createIntent(bodyContent!!, fragmentData)
                    }
                    5 -> {
                        fragment = if (RemoteConfigUtils.shouldShowBnplFragment()) {
                            HomeBnplFragment.createIntent(HOME_PAGE)
                        } else {
                            HomeSaldoFragment.createIntent()
                        }
                    }
                    6 -> {
                        fragment = HomeLoyaltyFragment.createIntent()
                    }
                    7 -> {
                        onBoardingCampaignFragment =
                            HomeOnBoardingCampaignWidgetFragment.createIntent(bodyContent)
                        fragment = onBoardingCampaignFragment as HomeOnBoardingCampaignWidgetFragment
                    }
                    8 -> {
                         fragment = HomeRefereeEntryPointFragment.createIntent()
                    }

                    9 -> {
                        fragment = SubscriptionEntryPointFragment.createIntent()
                    }
                }
                fragmentPool.add(fragment)
                transaction?.add(linearLayout.id, fragment, fragmentData?.category)
                    ?.commitAllowingStateLoss()
            }
        }

        binding.svHomepage.viewTreeObserver.addOnScrollChangedListener {
            val lastView = binding.svHomepage.getChildAt(binding.svHomepage.childCount - 1)
            val bottomDetector =
                lastView.bottom - (binding.svHomepage.height + binding.svHomepage.scrollY)
            if (bottomDetector == 0) {
                binding.layoutScrollToTop.root.showView()
                binding.layoutScrollToTop.root.setOnClickListener {
                    binding.svHomepage.moveToTop()
                }
            } else {
                binding.layoutScrollToTop.root.hideView()
            }
        }

        var shouldShowFloatingButton = RemoteConfigUtils.NewHomePage.shouldShowFloatingButton()
        val floatingButtonImage = RemoteConfigUtils.NewHomePage.getFloatingButtonImage()
        val floatingButtonRedirection = RemoteConfigUtils.NewHomePage.getFloatingButtonRedirection()

        if (floatingButtonRedirection.contains("yearend") && FeaturePrefManager.getInstance()
                .hasSeenYearReview()
        ) {
            shouldShowFloatingButton = false
        }

        if (shouldShowFloatingButton && (FeaturePrefManager.getInstance().paymentTransactionCount >= 10 || TransactionRepository.getInstance(
                requireContext()
            ).transactionCount > 0)
        ) {
            binding.clFloatingHomeYearInEnd.showView()
            context?.let {
                Glide.with(it).load(floatingButtonImage).placeholder(R.drawable.undang_float).into(
                    binding.ibYierFloatEntry
                )
            }
            binding.vwHomeFloatEntry.setOnClickListener {
                viewModel.onEventReceived(HomePageViewModel.Event.OnFloatingButtonHide)
            }
            binding.ibYierFloatEntry.setOnClickListener {
                viewModel.onEventReceived(
                    HomePageViewModel.Event.OnFloatingButtonClick(
                        floatingButtonRedirection
                    )
                )
            }
        } else {
            viewModel.onEventReceived(HomePageViewModel.Event.OnFloatingButtonHide)
        }
        if (homeFabConfigBody.isEnabled) {
            if (homeFabConfigBody.checkForWhitelisting.isEmpty()) setFloatingButtonUI()
            else gameViewModel.checkForWhitelisting(homeFabConfigBody.checkForWhitelisting)
        } else {
            binding.clLaporan.hideView()
        }

    }

    private fun setFloatingButtonUI() {
        binding.clLaporan.showView()
        val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()
        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }
        if (homeFabConfigBody.type.equals(HomeFabConfigType.IMAGE.name, ignoreCase = true)) {
            Glide.with(requireContext()).load(homeFabConfigBody.imageUrl)
                .placeholder(shimmerDrawable).into(binding.ivMovableGif)
        } else {
            loadGifIntoImageView(
                binding.ivMovableGif,
                homeFabConfigBody.imageUrl.orEmpty(),
                shimmerDrawable,
                binding.ivMovableGif.context
            )
        }
        binding.clLaporan.setOnClickListener {
            fabRedirectionAndAnalytics(homeFabConfigBody)
        }
        binding.tvClose.setOnClickListener {
            binding.clLaporan.hideView()
        }
    }

    private fun getLoyaltyGameTypeName(gameRuleName: String) {
        if(gameRuleName.isNotBlank()){
            gameViewModel.getGameProgressData(gameRuleName)
        }
    }

    private fun fabRedirectionAndAnalytics(homeFabConfig: HomeFabConfig) {
        val neuroRedirection = homeFabConfig.deeplinkAppNeuro.orEmpty()
        val deeplinkWeb = homeFabConfig.deeplinkWeb.orEmpty()
        when {
            neuroRedirection.isNotBlank() -> {
                val sourceLink = SourceLink(context = requireContext(), link = neuroRedirection)
                neuro.route(
                    sourceLink,
                    navigator = this,
                    onSuccess = {},
                    onFailure = {},
                )
            }
            neuroRedirection.isBlank() && deeplinkWeb.isNotBlank() -> {
                val intent = WebviewActivity.createIntent(
                    requireActivity(),
                    deeplinkWeb,
                    homeFabConfig.title
                )

                val mapArguments = deeplinkWeb.split('?')
                val bundle = Bundle()
                if (mapArguments.size > 1) {
                    for (i in 1 until mapArguments.size) {
                        val args = mapArguments[i].split("=")
                        bundle.putString(args[0], args[1])
                    }
                }
                intent.putExtras(bundle)
                startActivity(intent)
            }
        }
        handleFabAnalytics(homeFabConfig.title.orEmpty())
    }

    private fun handleFabAnalytics(title: String) {
        val prop = AppAnalytics.PropBuilder()
        prop.put(ENTRY_POINT2, HOME_PAGE)
        prop.put(ID, title)
        AppAnalytics.trackEvent("homepage_floating_icon_click", prop)
    }

    private fun loadGifIntoImageView(
        imageView: ImageView,
        resourceId: String,
        shimmerDrawable: ShimmerDrawable,
        context: Context?
    ) {
        Glide.with(requireContext()).asGif().load(resourceId).placeholder(shimmerDrawable)
            .into(imageView)
    }

    private fun getBodyContents(bodyBlockName: String?): FragmentBodyBlock {
        val type = object : TypeToken<FragmentBodyBlock>() {}.type
        val gson: Gson = GsonBuilder().create()
        var bodyData: FragmentBodyBlock

        var bodyContents: String? =
            RemoteConfigUtils.NewHomePage.getHomePageBodyBlock(bodyBlockName!!)

        bodyData = gson.fromJson(bodyContents, type)

        for (data in bodyData.data!!) {
            if (data?.analytics_name.equals("business_dashboard")) {
                isBusinessDashboardEnabled = true
            }
        }

        return bodyData
    }

    fun onBackPressed() {
        if (activity != null && listener != null) {
            listener?.handleNormalBackPressedFromHome()
        } else if (activity != null) {
            activity?.finish()
        }
    }

    override fun onResume() {
        super.onResume()
        initializeView()

        edcOrderDetailViewModel.getEdcOrderDetailByPhoneNumber()
        edcOrderDetailViewModel.orderDetails.observe(this) {
            val isPaid = it?.data?.paymentDetails?.status?.equals(STATUS_PAID, true).isTrue
            val isKycDone = it?.data?.checks?.kybDone.isTrue
            val isKybDone = it?.data?.checks?.kybDone.isTrue
            val isBankAdded = it?.data?.checks?.bankAccountAdded.isTrue
            val edcConfig = RemoteConfigUtils.getEdcConfig().edcPurchasePendingDetails
            val showSnackbar = edcConfig?.showSnackbar.isTrue
            val completionDate =
                DateTimeUtils.getUTCTimeToLocalDateTime(
                    it?.data?.checks?.completionExpireDate,
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            if (it?.result.isTrue && showSnackbar && isPaid && (!isKybDone || !isKycDone || !isBankAdded)) {
                binding.clPendingEdcDetails.showView()
                binding.edcDetailsTitle.text =
                    edcConfig?.snackbarTitle.orEmpty().replace("date", completionDate.orEmpty())
                binding.edcDetailsDesc.apply {
                    text = Utilities.makeSectionOfTextClickable(
                        edcConfig?.snackbarDesc.orEmpty(),
                        edcConfig?.snackbarDescHighlight.orEmpty(),
                        object : ClickableSpan() {
                            override fun onClick(widget: View) {
                                requireContext().openActivity(EdcOrderDetailsActivity::class.java) {
                                    putString(
                                        EdcOrderDetailsActivity.ORDER_ID,
                                        it?.data?.orderId.orEmpty()
                                    )
                                }
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                super.updateDrawState(ds)
                                ds.isUnderlineText = false
                                ds.color = context.getColorCompat(R.color.blue_60)
                            }
                        }
                    )
                    movementMethod = LinkMovementMethod.getInstance()
                }
            }
        }

        if (activity?.intent?.hasExtra("show_ppob_bottomsheet_val")!!) {
            val merchantCategory = activity?.intent?.getStringExtra("merchant_type")
            isFromNotification = true
            val value = activity?.intent?.getStringExtra("show_ppob_bottomsheet_val")
            if (value == "pulsa") {
                showPpobPulsaBottomSheet(merchantCategory)
            } else {
                showPpobListrikBottomSheet(merchantCategory)
            }
        }
        PaymentPrefManager.getInstance().saldoData.observe(viewLifecycleOwner) {
            val transactionFragment: FragmentTransaction? =
                activity?.supportFragmentManager?.beginTransaction()
            val fragment = if (RemoteConfigUtils.shouldShowBnplFragment()) {
                HomeBnplFragment.createIntent(HOME_PAGE)
            } else {
                HomeSaldoFragment.createIntent()
            }
            if (it.visible.isTrue) {
                transactionFragment?.show(fragment)
            } else {
                transactionFragment?.hide(fragment)
            }
        }
    }

    override fun setupView(view: View) {
        paymentTabViewModel.getReminderFlag()
        paymentTabViewModel.getPpobAndPaymentCount()
        if (RemoteConfigUtils.isCrossSellPopupEnabled()) {
            paymentTabViewModel.getFirstTransaction()
        }
    }

    override fun subscribeState() {

        subscribeSingleLiveEvent(viewModel.state) { it ->
            when (it) {
                is HomePageViewModel.State.MoveToSection -> {
                    val next = it.nextSection

                    next?.let {
                        scrollToSection(next - 1)
                    }
                }

                is HomePageViewModel.State.ShowNextCoachmark -> {
                    val next = it.nextSection
                    scope.launch {
                        next?.let {
                            showCoachmark(
                                next!! - 1,
                                data.filter { it.rank == next }[0]
                            )
                        }
                    }
                }
                HomePageViewModel.State.HideFloatingButton -> {
                    binding.clFloatingHomeYearInEnd.hideView()
                }
                is HomePageViewModel.State.OnFloatingButtonClick -> {
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(ENTRY_POINT2, HOME_PAGE)
                    prop.put(ID, BD)

                    AppAnalytics.trackEvent("homepage_floating_icon_click", prop)

                    val webViewIntent = CustomWebviewActivity.createIntent(
                        context,
                        it.redirectionUrl,
                        getString(R.string.year_in_end_review),
                        false,
                        "year_end_review",
                        "homepage",
                        binding.tvToolbarTitle.text.toString()
                    )
                    startActivity(webViewIntent)
                }
                else -> {}
            }
        }

        subscribeSingleLiveEvent(paymentTabViewModel.state) {
            when (it) {
                is PaymentTabViewModel.State.ShowPpobPulsaBottomsheet -> {
                    isFromNotification = false
                    showPpobPulsaBottomSheet(it.merchantCategory)
                }

                is PaymentTabViewModel.State.ShowPpobListrikBottomsheet -> {
                    isFromNotification = false
                    showPpobListrikBottomSheet(it.merchantCategory)
                }
                else -> {}
            }
        }

        gameViewModel.gameData.observe(viewLifecycleOwner) {
            it?.let { gd ->
                onBoardingCampaignFragment?.let { onBoardingFragment ->
                    if(onBoardingFragment.isAdded){
                        onBoardingFragment.getTimerAndGameStatus(
                            gd.gameProgress?.get(0)?.deadline.orEmpty(),
                            gd.gameProgress?.get(0)?.status.orEmpty()
                        )
                    }
                }

            }
        }

        gameViewModel.userWhitelistedForLeaderboard.observe(viewLifecycleOwner) {
            if (it?.result.isTrue && it?.data?.user?.isWhitelisted.isTrue) setFloatingButtonUI()
            else binding.clLaporan.hideView()
        }

    }

    private fun scrollToSection(section: Int) {
        val fragment = binding.frameHomepage.getChildAt(section)
        scrollToView(binding.svHomepage, fragment)
    }

    private fun scrollToView(scrollViewParent: ScrollView, view: View?) {
        // Get deepChild Offset
        val childOffset = Point()
        getDeepChildOffset(scrollViewParent, view?.parent, view, childOffset)
        // Scroll to child.
        scrollViewParent.smoothScrollTo(0, childOffset.y)
    }

    companion object {
        private const val STATUS_PAID = "PAID"

        var isFromNotification = false

        fun instance(): HomeFragment {
            return HomeFragment()
        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {

        if (id == "HOMEPAGE_PPOB_INTRO" && RemoteConfigUtils.isCrossSellPopupEnabled()) {
            paymentTabViewModel.getFirstTransaction()
        }
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        viewModel.onEventReceived(HomePageViewModel.Event.OnNextCoachmarkClick(nextSection))
    }

    private fun getDeepChildOffset(
        mainParent: ViewGroup,
        parent: ViewParent?,
        child: View?,
        accumulatedOffset: Point
    ) {
        if (parent == null) {
            return
        }
        val parentGroup = parent as ViewGroup

        if (parentGroup == mainParent) {
            return
        }
        child?.let {
            accumulatedOffset.x += child.left
            accumulatedOffset.y += child.top
            getDeepChildOffset(mainParent, parentGroup.parent, parentGroup, accumulatedOffset)
        }

    }

    private fun showCoachmark(section: Int, fragmentData: FragmentBlock?) {
        var btnString = getString(R.string.next)
        if (fragmentData?.coachmark_step == fragmentData?.coachmark_max_steps) {
            btnString = getString(R.string.understand)
        }

        fragmentData?.let {
            if (isAdded && activity != null && !requireActivity().isFinishing) {

                if (RemoteConfigUtils.OnBoarding.getRedirectionRule()
                        .equals(OnBoardingRedirection.USAGE_GOAL)
                ) {
                    val tabId = FeaturePrefManager.USAGE_GOAL_TAB
                }

                fragmentData.let {
                    if (activity != null && !requireActivity().isFinishing) {
                        var sectionCount = section
                        if (isKYCExluded) {
                            sectionCount--
                        }

                        if (binding.frameHomepage.getChildAt(section - 1) == null) {
                            scope.launch {
                                delay(200)
                                OnboardingWidget.createInstance(
                                    requireActivity(),
                                    this@HomeFragment,
                                    fragmentData.analytics_block_name,
                                    requireActivity().findViewById(R.id.navigation),
                                    null,
                                    fragmentData.coachmark_header!!,
                                    fragmentData.coachmark_body!!,
                                    btnString,
                                    FocusGravity.CENTER,
                                    ShapeType.RECTANGLE_FULL,
                                    fragmentData.coachmark_step!!,
                                    fragmentData.coachmark_max_steps!!,
                                    sendAnalytics = true,
                                    sendAnalyticsOnDismiss = true,
                                    delay = 5,
                                    isSnackBarClick = false,
                                    isPerformClick = false
                                )
                            }
                        } else {
                            val sectionView: View? = binding.frameHomepage.getChildAt(section)
                            sectionView?.let { view ->
                                scope.launch {
                                    delay(200)
                                    OnboardingWidget.createInstance(
                                        requireActivity(),
                                        this@HomeFragment,
                                        fragmentData.analytics_block_name,
                                        view,
                                        null,
                                        fragmentData.coachmark_header!!,
                                        fragmentData.coachmark_body!!,
                                        btnString,
                                        FocusGravity.CENTER,
                                        ShapeType.RECTANGLE_FULL,
                                        fragmentData?.coachmark_step!!,
                                        fragmentData.coachmark_max_steps!!,
                                        sendAnalytics = true,
                                        sendAnalyticsOnDismiss = true,
                                        delay = 5,
                                        isSnackBarClick = false,
                                        isPerformClick = false
                                    )
                                }
                            }
                        }
                        nextSection = fragmentData.coachmark_next
                    }
                }
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is HomeFragmentBackListener) {
            listener = context
        }
    }

    private fun showPpobPulsaBottomSheet(merchantCategory: String?) {
        if (SessionManager.getInstance()
                .hasPpobPulsaSeenCount() < 2 && !SessionManager.getInstance().hasPpobPulsaSeen()
        ) {
            if (!isFromNotification) {
                createPN("pulsa", merchantCategory)
            }
            val ppobBottomSheet = PpobPulsaBottomSheet.newInstance("pulsa", merchantCategory)
            ppobBottomSheet.show(activity?.supportFragmentManager!!, "ppob_pulsa")
            SessionManager.getInstance().setPpobPulsaBottomSheetSeenCount()
            val prop = AppAnalytics.PropBuilder()
            prop.put(
                AnalyticsConst.CROSS_POPUP_COUNT,
                SessionManager.getInstance().hasPpobPulsaSeenCount()
            )
            AppAnalytics.trackEvent(AnalyticsConst.CROSS_ADOPTION_POPUP_APPEAR, prop)
        }
    }

    private fun showPpobListrikBottomSheet(merchantCategory: String?) {
        if (SessionManager.getInstance()
                .hasPpobListrikSeenCount() < 2 && !SessionManager.getInstance().hasPpobListrikSeen()
        ) {
            if (!isFromNotification) {
                createPN("listrik", merchantCategory)
            }
            val ppobBottomSheet = PpobPulsaBottomSheet.newInstance("listrik", merchantCategory)
            ppobBottomSheet.show(activity?.supportFragmentManager!!, "ppob_listrik")
            SessionManager.getInstance().setPpobListrikBottomSheetSeenCount()
            val prop = AppAnalytics.PropBuilder()
            prop.put(
                AnalyticsConst.CROSS_POPUP_COUNT,
                SessionManager.getInstance().hasPpobListrikSeenCount()
            )
            AppAnalytics.trackEvent(AnalyticsConst.CROSS_ADOPTION_POPUP_APPEAR, prop)
        }
    }

    private fun createPN(source: String, merchantCategory: String?) {
        val localNotificationData = LocalNotificationData(
            resources.getString(R.string.notification_ppob_bottomsheet),
            resources.getString(R.string.notification_ppob_body),
            LocalNotificationIcon.DEFAULT
        )
        val random = Random()
        val randomNumber = random.nextInt(1000)
        val intent = Intent(activity, MainActivity::class.java)
        intent.putExtra("show_ppob_bottomsheet", true)
        intent.putExtra("show_ppob_bottomsheet_val", source)
        intent.putExtra("merchant_type", merchantCategory)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivities(
                activity,
                randomNumber,
                arrayOf(intent),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        } else {
            PendingIntent.getActivities(
                activity,
                randomNumber,
                arrayOf(intent),
                PendingIntent.FLAG_UPDATE_CURRENT
            )
        }
        LocalNotificationManager.showDefaultNotification(
            Application.getAppContext(),
            localNotificationData,
            LocalNotificationStyle.BIG_TEXT,
            pendingIntent, LocalNotificationManager.NOTIFICATION_CHANNEL_TITLE
        )
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    private fun showSaldoTopUpInfo() {
        val saldoTopupRestrictInfo = RemoteConfigUtils.getSaldoTopupRestrictInfo()
        val currentTime = System.currentTimeMillis()
        val startTime = DateTimeUtilsKt.getEpochFromUTCTime(saldoTopupRestrictInfo.startTime)
        val endTime = DateTimeUtilsKt.getEpochFromUTCTime(saldoTopupRestrictInfo.endTime)
        if (PaymentPrefManager.getInstance().getKycTier() == KycTier.ADVANCED &&
            currentTime > startTime && currentTime < endTime &&
            PaymentPrefManager.getInstance().getShowSaldoTopupRestrictDialog()
        ) {
            SaldoTopupRestrictDialog(
                requireContext(),
                saldoTopupRestrictInfo.dialogDescription
            ) {
                val url =
                    "${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?from=Payment"
                val intent =
                    WebviewActivity.createIntent(requireContext(), url, AppConst.EMPTY_STRING)
                startActivity(intent)
            }.show()
        }
    }
}