package com.bukuwarung.activities.transaction.category;

import android.app.Application;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.category.adapter.dataholder.CategoryTransactionDataHolder;
import com.bukuwarung.activities.transaction.category.adapter.dataholder.TransactionEmptyDataHolder;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

public final class TransactionListViewModel extends AndroidViewModel {

    private LiveData<CashCategoryEntity> categoryEntityLiveData ;

    private String categoryId;
    private String brickInstitutionId;
    private String startDate;
    private String endDate;
    private List<CashTransactionEntity> transactionList;

    private MediatorLiveData<ArrayList<DataHolder>> liveDataMerger = new MediatorLiveData<>();

    @Inject
    public TransactionListViewModel(Application application,String categoryId,String brickInstitutionId,String startDate,String endDate,Boolean isBrickInstitution) {
        super(application);
        this.categoryId = categoryId;
        this.brickInstitutionId = brickInstitutionId;
        this.startDate = startDate;
        this.endDate = endDate;
        categoryEntityLiveData = CashRepository.getInstance(application).getObservableCategoryById(this.categoryId);
        try {
            if (Utility.isBlank(this.categoryId) || categoryEntityLiveData==null) {
                FirebaseCrashlytics.getInstance().log("No category entity found: " + this.categoryId);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if(isBrickInstitution){
            this.liveDataMerger.addSource(TransactionRepository.getInstance(application).getTransactionListByBrickInstitutionIdWithDate(this.categoryId,this.brickInstitutionId,startDate,endDate), new Observer<List<CashTransactionEntity>>() {

                @Override
                public void onChanged(List<CashTransactionEntity> list) {
                    transactionList = list;
                    ArrayList convertedList = convertToViewObject();
                    liveDataMerger.setValue(convertedList);
                }
            });
        }else{
            if (startDate != null) {
                this.liveDataMerger.addSource(TransactionRepository.getInstance(application).getTransactionListByCategoryIdWithDate(this.categoryId, this.startDate, this.endDate), new Observer<List<CashTransactionEntity>>() {
                    @Override
                    public void onChanged(List<CashTransactionEntity> list) {
                        transactionList = list;
                        ArrayList convertedList = convertToViewObject();
                        liveDataMerger.setValue(convertedList);
                    }
                });
            } else {
                this.liveDataMerger.addSource(TransactionRepository.getInstance(application).getTransactionListByCategoryId(this.categoryId), new Observer<List<CashTransactionEntity>>() {
                    @Override
                    public void onChanged(List<CashTransactionEntity> list) {
                        transactionList = list;
                        ArrayList convertedList = convertToViewObject();
                        liveDataMerger.setValue(convertedList);
                    }
                });
            }
        }
    }

    public final void deleteSelectedTransactions() {


    }

    public final LiveData<ArrayList<DataHolder>> getDataHolderList() {
        return this.liveDataMerger;
    }

    public final LiveData<CashCategoryEntity> getCategoryLiveData() {
        return this.categoryEntityLiveData;
    }

    public final List<CashTransactionEntity> getTransactionList() {
        return this.transactionList;
    }

    public ArrayList convertToViewObject() {

        ArrayList convertedList = new ArrayList();
        for (CashTransactionEntity transactionEntity : this.transactionList) {
            convertedList.add(new CategoryTransactionDataHolder(transactionEntity));
        }
        if(ListUtils.isEmpty(this.transactionList)){
            convertedList.add(new TransactionEmptyDataHolder());
        }
        return convertedList;
    }
}
