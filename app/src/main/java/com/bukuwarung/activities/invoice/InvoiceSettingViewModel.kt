package com.bukuwarung.activities.invoice

import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.isEditingAddressForFirstTime
import com.bukuwarung.wrapper.EventWrapper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InvoiceSettingViewModel @Inject constructor(
        private val businessUseCase: BusinessUseCase
) : BaseViewModel() {
    private var _bitmap: Bitmap? = null

    sealed class Event {
        object OnCreate : Event()
        data class UpdateBusinessLogo(val bitmap: Bitmap) : Event()
        data class SaveInvoicePreference(val newPhone: String, val newAddress: Address?) : Event()
    }

    sealed class State {
        object InvoiceUpdate : State()

        data class HandleOnCreate(val bookEntity: BookEntity?) : State()
        data class BusinessLogoUpdated(val bitmap: Bitmap?) : State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private fun setState(newState: State) = viewModelScope.launch { _state.postValue(EventWrapper(newState)) }

    fun submitEvent(event: Event) {
        when (event) {
            Event.OnCreate -> handleOnCreateView()
            is Event.UpdateBusinessLogo -> updateBusinessLogo(event.bitmap)
            is Event.SaveInvoicePreference -> updateInvoicePreference(event.newPhone, event.newAddress)
        }
    }

    private fun handleOnCreateView() {
        setState(State.HandleOnCreate(businessUseCase.getCurrentBusiness()))
    }

    private fun updateBusinessLogo(bitmap: Bitmap) = viewModelScope.launch {
        _bitmap = bitmap
        setState(State.BusinessLogoUpdated(_bitmap))
    }

    private fun updateInvoicePreference(newPhone: String, newAddress: Address?) = viewModelScope.launch {
        newAddress ?: return@launch
        val userId = User.getUserId()
        val bookId = businessUseCase.getCurrentBusiness()?.bookId ?: return@launch
        businessUseCase.updateInvoicePref(bookId, userId, _bitmap, newPhone, newAddress.fullAddress.orEmpty())
        businessUseCase.updateBusinessAdministrativeData(bookId, newAddress)

        setState(State.InvoiceUpdate)
    }

    fun isEditingAddressForFirstTime(): Boolean? {
        return businessUseCase.getCurrentBusiness()?.isEditingAddressForFirstTime()
    }
}