package com.bukuwarung.activities.invoice

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.work.WorkInfo
import com.bukuwarung.R
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity.Companion.cashTransactionEntity
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity.Companion.cashTransactionItems
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.view.BusinessAddressActivity
import com.bukuwarung.activities.print.*
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.TransactionItemsEntity
import com.bukuwarung.database.entity.extension.getCustomerForInvoice
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityInvoiceSettingBinding
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.location.LocationUtil
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.extensions.getBeautifiedPhoneNumber
import com.bumptech.glide.Glide
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import java.io.File

@AndroidEntryPoint
class InvoiceSettingActivity : BaseActivity() {
    private var tempWatermarkStatus: Boolean = true
    private lateinit var binding: ActivityInvoiceSettingBinding
    private var profilePicFile: File? = null
    private var profilePicUri: Uri? = null
    private var businessLogoFilled: Boolean = false
    private var hasProvideLocation: Boolean = false

    private var from: String? = null

    private val viewModel: InvoiceSettingViewModel by viewModels()
    private var address: Address? = null

    private lateinit var notesMissionSteps: List<NotesMissionStep>
    private var invoiceData: InvoiceDataBlock? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setUpToolbarWithHomeUp(binding.tb)
        viewModel.submitEvent(InvoiceSettingViewModel.Event.OnCreate)
    }

    override fun onResume() {
        super.onResume()
        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }
        if (FeaturePrefManager.getInstance().isCompletedNotesMission) {
            if (!FeaturePrefManager.getInstance().isNoteRemovalIntroduced) {
                FeaturePrefManager.getInstance().setNoteRemovedIntroduced(true)
                showNoteRemovalIntroTooltip()
            }
        }
//        showNoteRemovalIntroTooltip()

    }

    override fun setViewBinding() {
        binding = ActivityInvoiceSettingBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        var invoiceDataBlock: String = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataBlock()
        val type = object : TypeToken<InvoiceDataBlock>() {}.type
        val gson: Gson = GsonBuilder().create()
        try {
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        } catch (e: JsonSyntaxException) {
            invoiceDataBlock = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataFailsafeBlock()
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        }
        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            binding.cashReceiptNew.findViewById<View>(R.id.banner_container)?.visibility = View.GONE
        } else {
            binding.cashReceipt.findViewById<View>(R.id.banner_container)?.visibility = View.GONE
        }

        binding.apply {
            btnSave.setOnClickListener {
                if (Utility.hasInternet()) {
                    FeaturePrefManager.getInstance().enableBukuWatermark(tempWatermarkStatus)
                    pbSave.visibility = View.VISIBLE
                    val newPhone = binding.etPhone.text.toString()
                    val newAddress = binding.etAddress.text.toString()
                    InputUtils.hideKeyboard(this@InvoiceSettingActivity)
                    address ?: finish()
                    viewModel.submitEvent(
                        InvoiceSettingViewModel.Event.SaveInvoicePreference(
                            newPhone,
                            address
                        )
                    )
                    InputUtils.hideKeyboard(this@InvoiceSettingActivity)
                    val eventProp = AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.LOCATION_FILLED, newAddress.isNotBlank())
                        .put(AnalyticsConst.PHONE_FILLED, newPhone.isNotBlank())
                        .put(AnalyticsConst.IMAGE_FILLED, businessLogoFilled)
                    if (from.isNotNullOrEmpty()) {
                        eventProp.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                    }
                    AppAnalytics.trackEvent(AnalyticsConst.INVOICE_SETTING_SAVE, eventProp)
                } else {
                    CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                        this@InvoiceSettingActivity,
                        supportFragmentManager
                    )
                }
            }

            logoFrame.setOnClickListener {
                showImageSelectorDialog()
            }
            if (!RemoteConfigUtils.shouldShowNewPosInvoice()) {
                cashReceipt.listenForAddressChange(etAddress)
                cashReceipt.listenForPhoneChange(etPhone)
            }

            etAddress.setOnClickListener {
                openAddressSetting()
            }
        }

        binding.watermarkSwitchBtn.setOnCheckedChangeListener { buttonView, isChecked ->

            if (isChecked) {

                binding.tvLogoRemovalMessage.text = getString(R.string.your_note_logo_is_active)

                tempWatermarkStatus = true
                if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                    binding.cashReceiptNew.findViewById<View>(R.id.layout_footer_pos)?.findViewById<View>(R.id.bukuwarung_ad_layout)?.visibility = View.VISIBLE
                } else {
                    binding.cashReceipt.findViewById<View>(R.id.bukuwarung_watermark_layout)?.visibility = View.VISIBLE
                }
            } else {

                binding.tvLogoRemovalMessage.text = getString(R.string.your_note_logo_is_inactive)

                tempWatermarkStatus = false
                if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                    binding.cashReceiptNew.findViewById<View>(R.id.layout_footer_pos)?.findViewById<View>(R.id.bukuwarung_ad_layout)?.visibility = View.GONE
                } else {
                    binding.cashReceipt.findViewById<View>(R.id.bukuwarung_watermark_layout)?.visibility = View.GONE
                }
            }
        }


        notesMissionSteps = getNotesMissionSteps()
        if (FeaturePrefManager.getInstance().isCompletedNotesMission) {
            binding.watermarkSettingsInfoLayout.visibility = View.GONE
            binding.bukuwarungWatermarkSettingOverlay.visibility = View.GONE

            if (FeaturePrefManager.getInstance().hasEnableBukuWatermark()) {
                binding.watermarkSwitchBtn.isEnabled = true
                binding.watermarkSwitchBtn.isChecked = true
                binding.tvLogoRemovalMessage.text = getString(R.string.your_note_logo_is_active)
            } else {
                binding.watermarkSwitchBtn.isEnabled = true
                binding.watermarkSwitchBtn.isChecked = false
                binding.tvLogoRemovalMessage.text = getString(R.string.your_note_logo_is_inactive)
            }
            if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                binding.cashReceiptNew.findViewById<View>(R.id.banner_container)?.visibility = View.GONE
            } else {
                binding.cashReceipt.findViewById<View>(R.id.banner_container)?.visibility = View.GONE
            }
        } else {
            binding.watermarkSettingsInfoLayout.visibility = View.VISIBLE
            binding.bukuwarungWatermarkSettingOverlay.visibility = View.VISIBLE
            binding.bukuwarungWatermarkSettingOverlay.setOnClickListener { }
            FeaturePrefManager.getInstance().enableBukuWatermark(true)
            binding.watermarkSwitchBtn.isChecked = true
        }



        if (FeaturePrefManager.getInstance().hasEnableBukuWatermark()) {
            if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                binding.cashReceiptNew.findViewById<View>(R.id.layout_footer_pos)?.findViewById<View>(R.id.bukuwarung_ad_layout)?.visibility = View.VISIBLE
            } else {
                binding.cashReceipt.findViewById<View>(R.id.bukuwarung_watermark_layout)?.visibility = View.VISIBLE
            }
        } else {
            if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                binding.cashReceiptNew.findViewById<View>(R.id.layout_footer_pos)?.findViewById<View>(R.id.bukuwarung_ad_layout)?.visibility = View.GONE
            } else {
                binding.cashReceipt.findViewById<View>(R.id.bukuwarung_watermark_layout)?.visibility = View.GONE
            }
        }

        val stringBuilder =
            SpannableStringBuilder(getString(R.string.want_to_use_this_feature_watermark))
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
//                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_VIEW_PRIVACY, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN_TUTORIAL), true, false, false)

                val intent = Intent(this@InvoiceSettingActivity, NotesMissionActivity::class.java)
                intent.putExtra(NotesMissionActivity.FROM, FROM.INVOICE_SETTING.name)
                startActivity(intent)
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.color = <EMAIL>(R.color.blue_80)
            }
        }
        stringBuilder.setSpan(
            clickableSpan,
            91,
            stringBuilder.length,
            Spannable.SPAN_INCLUSIVE_INCLUSIVE
        )
        binding.tvInfo.text = stringBuilder
        binding.tvInfo.movementMethod = LinkMovementMethod.getInstance()

        if (!RemoteConfigUtils.getEnableNotaMission()) {
            binding.notaSettingLayout.visibility = View.GONE
        }
    }

    private fun showNoteRemovalIntroTooltip() {

        TooltipBuilder.builder(this)
            .setGravity(Gravity.BOTTOM)
            .setText(this.getString(R.string.nonactivate_bukuwarung_logo))
            .setAnchor(binding.watermarkSwitchBtn)
            .setWidth(null)
            .build()
            .show()
    }

    private fun openAddressSetting() {
        AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA)
            put(AnalyticsConst.ACTION, if (viewModel.isEditingAddressForFirstTime().isTrue) AnalyticsConst.NEW else AnalyticsConst.EDIT)
            AppAnalytics.trackEvent(AnalyticsConst.EDIT_BUSINESS_ADDRESS, this)
        }

        val intent = BusinessAddressActivity.createIntent(this@InvoiceSettingActivity, entryPoint = AnalyticsConst.NOTA)
        startActivityForResult(intent, ADDRESS_FLOW_REQUEST_CODE)
    }

    private fun getLocation() {
        if (Build.VERSION.SDK_INT >= 23 &&
            checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(arrayOf(Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION), PermissionConst.ACCESS_LOCATION)
        } else {
            try {
                // location worker
                binding.locationLoading.visibility = View.VISIBLE
                LocationUtil.getLocation(this, "nota")?.observe(this, Observer {
                    if (it != null && it.state == WorkInfo.State.SUCCEEDED) {
                        val workInfoOutputData = it.outputData
                        val streetName = workInfoOutputData.getString("streetName")
                        binding.apply {
                            etAddress.apply {
                                setText(streetName)
                                setSelection(streetName?.length ?: 0)
                            }

                            locationLoading.visibility = View.GONE
                        }
                    }
                })
            } catch (ex: Exception) {
                FirebaseCrashlytics.getInstance().recordException(ex)
            }

        }
    }


    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is InvoiceSettingViewModel.State.HandleOnCreate -> populateData(it.bookEntity)
                is InvoiceSettingViewModel.State.BusinessLogoUpdated -> handleBusinessLogo(it.bitmap)
                InvoiceSettingViewModel.State.InvoiceUpdate -> finish()
            }
        }
    }

    private fun populateData(book: BookEntity?) {
        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            binding.cashReceiptNew.setup {
                setAsDummy()
                setInvoiceData { invoiceData }
                setFrom { CashTransactionReceipt.FROM.FROM_NOTA_SETTINGS }
                isInvoiceSetting { true }
                bookEntity { book }
                cashTransaction { cashTransactionEntity }
                transactionItem { cashTransactionItems }
                customerEntity { cashTransactionEntity?.getCustomerForInvoice() }
                transactionItem {
                    val transactionItems =
                        TransactionRepository.getInstance(this@InvoiceSettingActivity)
                            .getTransactionItems(
                                cashTransactionEntity?.cashTransactionId
                            )
                    convertTransactionItemsToDto(
                        cashTransactionEntity?.cashTransactionId,
                        transactionItems
                    )
                }
            }
            binding.cashReceiptNew.visibility = View.VISIBLE
            binding.cashReceipt.visibility = View.GONE
        } else {
            binding.cashReceipt.setupOldReceipt {
                setAsDummy()
                setFrom { CashTransactionOldReceipt.FROM.FROM_NOTA_SETTINGS }
                isInvoiceSetting { true }
                bookEntity { book }
                cashTransaction { cashTransactionEntity }
                transactionItem { cashTransactionItems }
                customerEntity { cashTransactionEntity?.getCustomerForInvoice() }
                transactionItem {
                    val transactionItems =
                        TransactionRepository.getInstance(this@InvoiceSettingActivity)
                            .getTransactionItems(
                                cashTransactionEntity?.cashTransactionId
                            )
                    convertTransactionItemsToDto(
                        cashTransactionEntity?.cashTransactionId,
                        transactionItems
                    )
                }
            }
            binding.cashReceipt.visibility = View.VISIBLE
            binding.cashReceiptNew.visibility = View.GONE
        }
            if (address == null && book != null) {
                address = Address(
                    id = "",
                    name = "",
                    province = book?.province ?: "",
                    city = book?.city ?: "",
                    district = book?.district ?: "",
                    subDistrict = book?.subdistrict ?: "",
                    postalCode = book?.postalCode ?: "",
                    fullAddress = book?.businessAddress
                )
            }

        book?.let {
            binding.etAddress.setText(Utility.getCompleteAddress(it))
            binding.etPhone.setText(it.getBeautifiedPhoneNumber())
        }
    }

    private fun convertTransactionItemsToDto(
        cashTransactionId: String?,
        transactionItems: List<TransactionItemsEntity>
    ): List<TransactionItemDto>? {
        val ret = ArrayList<TransactionItemDto>()
        try {
            for (transactionItem in transactionItems) {
                val productEntity = ProductRepository.getInstance(this).getProductsById(transactionItem.productId)
                if (productEntity != null) {
                    val productSelection = TransactionItemDto()
                    if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                    productSelection.productName = productEntity.name
                    productSelection.quantity = transactionItem.quantity
                    productSelection.sellingPrice = productEntity.unitPrice
                    productSelection.measurementUnit = productEntity.measurementName
                    ret.add(productSelection)
                }
            }
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ret
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            try {
                var uri: Uri? = null

                when (requestCode) {
                    PermissionConst.TAKE_PHOTO -> uri = profilePicUri
                    PermissionConst.REQ_PICK_IMAGE_PERMISSON -> uri = data?.data
                    ADDRESS_FLOW_REQUEST_CODE -> {
                        val merchantAddress = data?.getParcelableExtra<Address>(BusinessAddressActivity.ADDRESS) ?: return
                        address = merchantAddress
                        binding.etAddress.setText(Utility.getCompleteAddress(address))
                    }
                }

                uri?.let {
                    var bitmap = MediaStore.Images.Media.getBitmap(contentResolver, uri)


                    bitmap = Utility.fixImageRotation(this, bitmap, profilePicFile, it)
                    profilePicFile = null
                    businessLogoFilled = true
                    viewModel.submitEvent(InvoiceSettingViewModel.Event.UpdateBusinessLogo(bitmap))
                }
            } catch (ex: Exception) {

            }
        }
    }

    private fun handleBusinessLogo(bitmap: Bitmap?) {
        if (bitmap != null) {
            binding.logoFrame.background = getDrawableCompat(R.drawable.frame_rounded_black_10)
            binding.tvUploadLogo.visibility = View.GONE
            binding.imgLogo.visibility = View.VISIBLE
            Glide.with(this).load(bitmap).centerCrop().into(binding.imgLogo)
            if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                binding.cashReceiptNew.updateBusinessLogo(bitmap)
            } else {
                binding.cashReceipt.updateBusinessLogo(bitmap)
            }
        } else {
            binding.logoFrame.background = getDrawableCompat(R.drawable.frame_rounded_black_10_solid)
            binding.tvUploadLogo.visibility = View.VISIBLE
            binding.imgLogo.visibility = View.GONE
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        val propBuilder = AppAnalytics.PropBuilder()

        if (grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_DENY)
            return
        }
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                setImageSourceFromCamera()
            }
            PermissionConst.ACCESS_LOCATION -> {
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_ALLOW)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST, propBuilder)
                getLocation()
            }
        }


    }

    private fun showImageSelectorDialog() {
        val imageSelectorDialog = ImageSelectorDialog2(this, { setImageSourceFromCamera() }) {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                type = "image/*"
                addCategory(Intent.CATEGORY_OPENABLE)
            }
            startActivityForResult(Intent.createChooser(intent, getString(R.string.select_image_instruction)), PermissionConst.REQ_PICK_IMAGE_PERMISSON)
        }
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && this.checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(PermissionConst.CAMER_AND_STORAGE, PermissionConst.REQ_TAKE_PICTURE_PERMISSON)
            }
            if (intent.resolveActivity(packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }


    companion object {
        fun createIntent(context: Context) = Intent(context, InvoiceSettingActivity::class.java)
        private const val ADDRESS_FLOW_REQUEST_CODE = 1133
    }
}