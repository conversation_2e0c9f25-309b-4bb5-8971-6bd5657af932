package com.bukuwarung.activities.customer.transactiondetail

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.expense.dialog.DeleteTrxDialog
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.print.BluetoothPrinter
import com.bukuwarung.activities.print.ReceiptTextParser
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionItemsEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityCustomerTransactionDetailExpandedBinding
import com.bukuwarung.dialogs.printer.OpenSetupPrinterDialog
import com.bukuwarung.dialogs.printer.PrintingDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.*
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.AndroidEntryPoint
import java.util.*

@AndroidEntryPoint
class TransactionDetailExpandedActivity : AppActivity() {

    private val viewModel: CustomerTransactionDetailViewModel by viewModels()

    internal lateinit var handler: Handler

    // these global objects is for printing only
    private var customer: CustomerEntity? = null
    private var transaction: TransactionEntity? = null
    private var customerPayment: CustomerTransactionDetailState? = null
    private var trxProducts: List<TransactionItemDto>? = null
    private var printingDialog: PrintingDialog? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var transactionNote: String = ""

    private lateinit var binding: ActivityCustomerTransactionDetailExpandedBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerTransactionDetailExpandedBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize ViewModel with parameters from intent
        val trxId: String =
            intent.getStringExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM) ?: "-"
        val cstId: String =
            intent.getStringExtra(CustomerTransactionDetailActivity.CST_ID_PARAM) ?: "-"
        viewModel.initData(trxId, cstId)

        handler = Handler()
        setInitValues()
    }

    fun setInitValues() {
        observeData()
//        binding.tvDate.text = "%s %s ".format(Utility.formatReceiptDate(transactionEntity.date), Utility.getReadableTimeString(transactionEntity.createdAt))
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    private fun showBasedOnState(state: CustomerTransactionDetailState) {
        when (state.currentState) {
            CustomerTransactionDetailStateType.Loading -> {
                binding.mainContainer.visibility = View.GONE
                binding.editBtn.visibility = View.GONE
                binding.deleteBtn.visibility = View.GONE
            }
            CustomerTransactionDetailStateType.NotFound -> {
                binding.mainContainer.visibility = View.GONE
                binding.editBtn.visibility = View.GONE
                binding.deleteBtn.visibility = View.GONE

                handler.postDelayed({
                    finish()
                }, 1000)
            }
            CustomerTransactionDetailStateType.Loaded -> {
                binding.mainContainer.visibility = View.VISIBLE
                //requirement change, edit is not allowed for transaction_type = CASH_TRANSACTION
                binding.editBtn.visibility = View.GONE
                binding.deleteBtn.visibility = View.VISIBLE

                customerPayment = state
                customerTransactionSummaryDto = state.customerTransactionSummaryDto

                state.transaction?.let {
                    val transactionType = if (it.amount >= 0) {
                        1
                    } else {
                        -1
                    }


//                    binding.editBtn.visibility = (!it.isPaymentTransaction).asVisibility()
//                    binding.deleteBtn.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_receipt.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_payment_receipt.visibility = it.isPaymentTransaction.asVisibility()

                    if (it.isPaymentTransaction) {
                        loadPaymentData(state)
                    } else {
                        populateReceipt(it)
                    }

                    customer = state.customer
                    transaction = state.transaction

                    binding.tvAccountName.text = customer?.name
                    binding.tvName.text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                    binding.tvDate.text = "%s %s".format(Utility.formatReceiptDate(it.date), Utility.getReadableTimeString(it.createdAt))
                    binding.tvTotalTransaction.text = "${Utility.getCurrency()}${Utility.formatCurrency(transaction?.amount)}"

                    binding.tvPhoneNumberRight.text = customer?.phone
                    binding.tvPhoneNumber.text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                        ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                                ?: "-"

                    val cashTransactionEntity = TransactionRepository.getInstance(this).getCashTransactionByCustomerTransactionId(transaction?.transactionId)

                    binding.ivLunas.text = "Lunas"

                    if (cashTransactionEntity.status == 0) {
                        binding.ivLunas.text = "Belum Lunas"
                    }

                    val transactionItems = TransactionRepository.getInstance(this).getTransactionItems(cashTransactionEntity.cashTransactionId)

                    trxProducts = convertTransactionItemsToDto(cashTransactionEntity.cashTransactionId, transactionItems)


                    if (transactionItems.isNotEmpty()) {
                        showItems(trxProducts)
                    } else {
                        binding.tvHeadTransactionItems.visibility = View.GONE
                        binding.tvHeadTransactionItemsQty.visibility = View.GONE
                        binding.vBelowItems.visibility = View.GONE
                    }

                    binding.btnClose.setOnClickListener {
                        finish()
                    }

                    binding.btnSuccess.setOnClickListener {
                        TransactionRepository.getInstance(Application.getAppContext()).settleUnpaidCashTransaction(
                            User.getBusinessId(),
                            customer!!.customerId, transaction!!.amount, Utility.getStorableDateString(Date()),
                            if (customer!!.balance > 0) getString(R.string.filter_nil) else getString(R.string.paid_off), 1, cashTransactionEntity.cashTransactionId
                        )

                        val intent = Intent(this, MainActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                        startActivity(intent)
                        finish()
                    }

                    binding.editBtn.setOnClickListener { _ -> goToEditPage(it.transactionId) }
                    binding.deleteBtn.setOnClickListener { _ -> showDeleteDialog(it.transactionId) }
                    binding.btnPrint.setOnClickListener { _ -> checkPrintRequirement(transactionType) }
                    binding.btnShare.setOnClickListener { _ -> shareTrx(it.isPaymentTransaction) }
                }
            }
            else -> {}
        }
    }

    fun showItems(transactionItems: List<TransactionItemDto>?) {
        binding.tvHeadTransactionItems.visibility = View.VISIBLE
        binding.tvHeadTransactionItemsQty.visibility = View.VISIBLE
        binding.vBelowItems.visibility = View.VISIBLE

        val linearItemLayout = binding.llTransactionItems

        val linearChildItems = LinearLayout(this)
        linearChildItems.orientation = LinearLayout.VERTICAL

        linearItemLayout.removeAllViews()

        for (i in 0 until transactionItems!!.size) {
            val rl = RelativeLayout(this)
            val textItemName = TextView(this)
            textItemName.gravity = Gravity.LEFT

            val textItemQty = TextView(this)
            textItemQty.gravity = Gravity.RIGHT

            val lp: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT, textItemName.id)

            textItemName.layoutParams = lp

            lp.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, textItemQty.id)
            textItemQty.layoutParams = lp

            textItemName.text = transactionItems[i].productName
            textItemQty.text = transactionItems[i].quantity.toString()

            rl.addView(textItemName)
            rl.addView(textItemQty)

            linearChildItems.addView(rl)
        }

        linearItemLayout.addView(linearChildItems)
    }

    @SuppressLint("SetTextI18n")
    private fun populateReceipt(transactionEntity: TransactionEntity) {
        try {
            val receipt = layoutInflater.inflate(R.layout.utang_receipt_layout, binding.mainContainer, false)

            receipt.findViewById<TextView>(R.id.tvWarungName).visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()
            receipt.findViewById<TextView>(R.id.tvWarungPhone).visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()

            receipt.findViewById<TextView>(R.id.tvWarungName).text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                ?: "-"
            receipt.findViewById<TextView>(R.id.tvWarungPhone).text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                        ?: "-"
            receipt.findViewById<TextView>(R.id.tvTransactionDate).text = "%s %s".format(Utility.formatReceiptDate(transactionEntity.date), Utility.getReadableTimeString(transactionEntity.createdAt))
            val customerEntity = CustomerRepository.getInstance(this).getCustomerById(transactionEntity.customerId)
            receipt.findViewById<TextView>(R.id.tv_cst_name).text = customerEntity.name
            receipt.findViewById<TextView>(R.id.tv_cst_phone).text = Utility.beautifyPhoneNumber(customerEntity.phone)
//            receipt.tv_transaction_type.text = if (transactionEntity.amount.orNil >= 0) {
//                /**Menerima*/
//                getString(R.string.receiving_label)
//            } else {
//                /**Memberikan*/
//                getString(R.string.giving_label)
//            }
            receipt.findViewById<TextView>(R.id.tv_transaction_note).setTextOrDefault(transactionEntity.description)
            transactionNote = transactionEntity.description
            receipt.findViewById<TextView>(R.id.tvTransactionNominal).text = "${Utility.getCurrency()}${Utility.formatCurrency(transactionEntity.amount)}"

            receipt.findViewById<TextView>(R.id.tv_transaction_total).text = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            receipt.findViewById<TextView>(R.id.tv_paid_total).text = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            receipt.findViewById<TextView>(R.id.tv_remaining_total).text = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

//            receipt.expandWithAnimation(receipt.transaction_detail_layout, detailHeight, viewModel.isReceiptExpanded)
            viewModel.isReceiptExpanded.observe(this, Observer {
                receipt.findViewById<View>(R.id.transaction_detail_layout).visibility = it.asVisibility()
            })

            // Remove old receipt and add the new one
            binding.mainContainer.removeAllViews()
            binding.mainContainer.addView(receipt)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun loadPaymentData(state: CustomerTransactionDetailState) {
        when (state.currentPaymentState) {
            CustomerTransactionPaymentDetailStateType.Loading -> {
                // binding.paymentInfoLoader.visibility = View.VISIBLE
                // binding.paymentInfoError.visibility = View.GONE
            }
            CustomerTransactionPaymentDetailStateType.Loaded -> {
                // binding.paymentInfoLoader.visibility = View.GONE
                // binding.paymentInfoError.visibility = View.GONE

                populatePaymentReceipt(state)

            }
            CustomerTransactionPaymentDetailStateType.Error -> {
                // binding.paymentInfoLoader.visibility = View.GONE
                // binding.paymentInfoError.visibility = View.VISIBLE
            }
            else -> {}
        }
    }

    private fun shareTrx(isPayment: Boolean) {
        val viewToShare = binding.mainContainer
        generateAndShareViewImage(this, "com.whatsapp", viewToShare, "", true)
    }

    private fun generateAndShareViewImage(context: Context?, packageNm: String?, receiptLayout: View?, mobile: String?, useWA: Boolean) {
        try {
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(receiptLayout, false)
            val shareLayoutImage = ShareLayoutImage(getString(R.string.utang_invoice_sharing_text), context, packageNm, mobile, useWA, false)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun checkPrintRequirement(transactionType: Int) {

        when (val printers: List<PrinterDataHolder>? = PrinterPrefManager(this).installedPrinters) {
            null -> {
                val intent = Intent(this, SetupPrinterActivity::class.java)
                val isPrinterPaired = PrinterPrefManager(this).defaultDeviceAddress.isNotEmpty()

                if (isPrinterPaired) {
                    startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                } else {
                    val dialog = OpenSetupPrinterDialog(this) {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                            AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                        )

                        startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                    }
                    dialog.show()
                }

            }
            printers -> {
                when {
                    printers.isEmpty() -> {
                        val dialog = OpenSetupPrinterDialog(this) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                            )
                            val intent = Intent(this, SetupPrinterActivity::class.java)
                            startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                        }

                        dialog.show()
                    }
                    printers.isNotEmpty() -> {
                        startPrinting()
                    }
                }
            }
        }
    }


    private fun goToEditPage(transactionId: String) {
        val intent = Intent(this, AddTransactionActivity::class.java)
        intent.putExtra("transactionId", transactionId)
        intent.putExtra("OPERATION_TYPE", "")

        startActivityForResult(intent, CustomerTransactionDetailActivity.EDIT_TRX_REQUEST_CODE)
    }

    private fun showDeleteDialog(transactionId: String) {
        val dialog = DeleteTrxDialog(this) {
            if (it) {
                deleteCustomerTransaction(transactionId)
                finish()
            }
        }
        dialog.show()
    }

    private fun deleteCustomerTransaction(transactionId: String) {
        val t = Thread(DeleteCustomerTransactionDetailRunnable(this, transactionId))
        t.start()
        try {
            t.join()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun populatePaymentReceipt(state: CustomerTransactionDetailState) {
        try {
            val trxEntity = state.transaction ?: return
            val customerEntity = state.customer ?: return

            val receipt = layoutInflater.inflate(R.layout.utang_payment_receipt_layout, binding.mainContainer, false)

            receipt.findViewById<TextView>(R.id.tvWarungName).text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                ?: "-"
            receipt.findViewById<TextView>(R.id.tvWarungPhone).text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                        ?: "-"
            receipt.findViewById<TextView>(R.id.tvTransactionDate).text = "%s %s".format(Utility.formatReceiptDate(trxEntity.date), Utility.getReadableTimeString(trxEntity.createdAt))

            receipt.findViewById<TextView>(R.id.tv_cst_name).text = customerEntity.name
            receipt.findViewById<TextView>(R.id.tv_cst_phone).text = Utility.beautifyPhoneNumber(customerEntity.phone)
            receipt.findViewById<TextView>(R.id.tvTransactionNominal).text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trxEntity.amount))

            val showFreeCharge = AppConfigManager.getInstance().paymentFreeChargeStatus

            val amount = state.transaction.amount ?: 0.0
            if (amount > 0) {
                // payment in
                state.paymentCollection?.let {
                    // set transaction ID
                    receipt.findViewById<TextView>(R.id.tv_invoice_number).text = it.transactionId
                    // set free charge
                    receipt.findViewById<TextView>(R.id.tv_free_charge).visibility = showFreeCharge.asVisibility()
                    // set sender name (customer name)
                    receipt.findViewById<TextView>(R.id.tv_sender_name).text = "%s - %s".format(
                        it.paymentChannel, customerEntity.name
                            ?: "-"
                    )
                    // set receiver name
                    receipt.findViewById<TextView>(R.id.tv_recipient_name).text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.findViewById<TextView>(R.id.tv_recipient_aaccount_number).text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "${getString(R.string.label_payment_in_note_plain)} - $str"
                        }

                    }
                    receipt.findViewById<TextView>(R.id.tv_transaction_note).text = desc
                }
            } else {
                // payment out
                state.paymentDisbursement?.let {
                    // set transaction ID
                    receipt.findViewById<TextView>(R.id.tv_invoice_number).text = it.transactionId
                    // set free charge
                    receipt.findViewById<TextView>(R.id.tv_free_charge).visibility = showFreeCharge.asVisibility()
                    // set sender name (merchant name)
                    receipt.findViewById<TextView>(R.id.tv_sender_name).text = "%s - %s".format(
                        it.paymentChannel, currentBook?.businessOwnerName
                            ?: "-"
                    )
                    // set receiver name
                    receipt.findViewById<TextView>(R.id.tv_recipient_name).text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.findViewById<TextView>(R.id.tv_recipient_aaccount_number).text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "${getString(R.string.label_payment_in_note_plain)} - $str"
                        }

                    }
                    receipt.findViewById<TextView>(R.id.tv_transaction_note).text = desc
                }
            }

            receipt.findViewById<TextView>(R.id.tv_transaction_total_payment).text = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            receipt.findViewById<TextView>(R.id.tv_paid_total_payment).text = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            receipt.findViewById<TextView>(R.id.tv_remaining_total_payment).text = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

            viewModel.isReceiptExpanded.observe(this, Observer {
                receipt.findViewById<View>(R.id.layout_transaction_detail).visibility = it.asVisibility()
            })

            receipt.findViewById<TextView>(R.id.tv_footer).text = Html.fromHtml(getString(R.string.label_transaction_security_guaranteed))

            binding.mainContainer.removeAllViews()
            binding.mainContainer.addView(receipt)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }



    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                CustomerTransactionDetailActivity.EDIT_TRX_REQUEST_CODE -> {
                    data?.getStringExtra(CustomerTransactionDetailActivity.TRX_ID_EDIT_PARAM)?.let { newTrxId ->
                        viewModel.setTransactionId(newTrxId)
                    }
                }
                SetupPrinterActivity.RECORD_DETAIL -> startPrinting()
            }
        }
    }


    private fun startPrinting() {
        transaction ?: return
        var entryPoint: String = AnalyticsConst.TRANSACTION

        // not-null assertion is ok since both objects have checked using elvis operator
        val textToPrint = if (transaction!!.isPaymentTransaction.isTrue) {
            customerPayment ?: return
            entryPoint = AnalyticsConst.PAYMENT
            ReceiptTextParser(this, currentBook).parseCustomerPaymentReceipt(customerPayment!!, customerTransactionSummaryDto, true)
        } else {
            customer ?: return
            ReceiptTextParser(this, currentBook).parseCustomerReceipt(customer!!, transaction!!, customerTransactionSummaryDto, true)

        }

        val printer = BluetoothPrinter(this, entryPoint)
        printingDialog = printer.printingDialog
        printer.printPrintables(textToPrint, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                printingDialog?.dismiss()
                permissionLauncher.launch(permissions)
            }
        })
    }

    private val permissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
            if (it.containsValue(false)) {
                Toast.makeText(this, R.string.location_permission_denied_message, Toast.LENGTH_SHORT)
                    .show()
            }
        }

    private fun convertTransactionItemsToDto(
        cashTransactionId: String?,
        transactionItems: List<TransactionItemsEntity>
    ): List<TransactionItemDto>? {
        val ret = ArrayList<TransactionItemDto>()
        try {
            for (transactionItem in transactionItems) {
                val productEntity = ProductRepository.getInstance(this).getProductsById(transactionItem.productId)
                if (productEntity != null) {
                    val productSelection = TransactionItemDto()
                    if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                    productSelection.productName = productEntity.name
                    productSelection.quantity = transactionItem.quantity
                    ret.add(productSelection)
                }
            }
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ret
    }


    override fun onDestroy() {
        super.onDestroy()
        printingDialog?.let {
            it.dismiss()
            printingDialog = null
        }
    }

    companion object {

        const val EDIT_TRX_REQUEST_CODE = 11
        const val TRX_ID_EDIT_PARAM = "TrxIdFromEdit"

        const val TRX_ID_PARAM = "transactionId"
        const val CST_ID_PARAM = "customerId"

        fun getNewIntent(origin: Context, trxId: String, customerId: String): Intent {
            val intent = Intent(origin, CustomerTransactionDetailActivity::class.java)
            intent.putExtra(TRX_ID_PARAM, trxId)
            intent.putExtra(CST_ID_PARAM, customerId)

            return intent
        }

    }
}