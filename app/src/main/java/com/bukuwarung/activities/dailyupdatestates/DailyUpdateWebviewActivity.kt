package com.bukuwarung.activities.dailyupdatestates

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.webkit.*
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.EVENT_DAILY_BUSINESS_UPDATE_WEBVIEW_LOADED
import com.bukuwarung.constants.AnalyticsConst.EVENT_OPEN_WEBVIEW
import com.bukuwarung.constants.AnalyticsConst.SHARE_DAILY_BUSINESS_UPDATE_CLICK
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrEmpty
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.bukuwarung.databinding.ActivityWebviewBinding
import org.json.JSONArray

class DailyUpdateWebviewActivity : BaseWebviewActivity() {

    private lateinit var binding: ActivityWebviewBinding

    var bookId: String? = null
    var interfaceName: String = "BukuWarungStory"

    override fun onCreate(savedInstanceState: Bundle?) {
        this.requestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        binding = ActivityWebviewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val propBuilder = PropBuilder()
        propBuilder.put(getString(R.string.webview_link_key), getLink())
        propBuilder.put(getString(R.string.webview_title_key), getTitleText())
        AppAnalytics.trackEvent(EVENT_OPEN_WEBVIEW, propBuilder)

        intent.getStringExtra(BOOKID)?.let {
            bookId = it
        }

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        binding.toolbar.visibility = View.GONE
        if (bookId.isNotNullOrEmpty()) {
            setupWebViewClient()
            binding.webView?.addJavascriptInterface(JsObject(this), interfaceName)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }

    override fun hideToolBar(): Boolean {
        return false
    }

    private fun saveImageAndShare() {
        ImageUtils.saveLayoutConvertedImage(binding.webView, true)
        val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(binding.webView, false)
        val shareLayoutImage = ShareLayoutImage(getString(R.string.share_with), this, null, null, false, false)
        saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
    }

    private fun setupWebViewClient() {
        bookId?.let { DailyUpdateClient(it, this) }?.let { webViewClient ->
            binding.webView?.let {
                it.webViewClient = webViewClient
            }
        }
    }

    companion object {

        private val BOOKID: String = "BOOKID"

        @kotlin.jvm.JvmField
        var WEBVIEW_PARAM_IS_DAILY_UPDATE = "webview_param_is_daily_update"

        fun createIntent(origin: Context?, link: String?, title: String?, bookId: String): Intent {
            val intent = Intent(origin, DailyUpdateWebviewActivity::class.java)
            intent.putExtra(LINK, link)
            intent.putExtra(TITLE, title)
            intent.putExtra(BOOKID, bookId)
            return intent
        }
    }

    override fun getLink(): String? {
        return intent.getStringExtra(LINK)
    }

    override fun getTitleText(): String? {
        return intent.getStringExtra(TITLE)
    }

    override fun getToolbarColor(): Int {
        return R.color.colorPrimary
    }

    override fun getUserAgent(): String? {
        return null
    }

    override fun getDeeplinkScheme(): String {
        return BuildConfig.DEEPLINK_SCHEME
    }


    override fun allowDebug(): Boolean {
        return false
    }

    override fun getAppToken(): String? {
        return SessionManager.getInstance().bukuwarungToken
    }

    override fun getUserId(): String? {
        return SessionManager.getInstance().userId
    }

    class DailyUpdateClient(val bookId: String, val context: Context) : WebViewClient() {

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError) {
            (context as DailyUpdateWebviewActivity).binding.errorStateImg.setImageResource(R.drawable.ic_no_inet)
            context.binding.errorState.visibility = View.VISIBLE
            context.binding.webView?.visibility = View.GONE
        }

        override fun onPageFinished(view: WebView, url: String) {
            super.onPageFinished(view, url)
            val sessionManager = SessionManager.getInstance()
            val token = sessionManager.bukuwarungToken
            val userId = sessionManager.userId

            val yesterdayDate = DateTimeUtils.getYesterdayDate(context.getString(R.string.yyyy_MM_dd))

            val creditValue = Utility.formatAmount(
                BusinessRepository.getInstance(context)
                    .getCreditByDate(bookId, yesterdayDate)
            )

            val debitValue = Utility.formatAmount(
                Math.abs(
                    BusinessRepository.getInstance(context)
                        .getDebitByDate(bookId, yesterdayDate)
                )
            )

            val cashInValue = Utility.formatAmount(
                BusinessRepository.getInstance(context)
                    .getCashInByDate(bookId, yesterdayDate)
            )

            val buyingPriceCashOutValue = Math.abs(BusinessRepository.getInstance(context).getBuyingPriceCashOutByDate(bookId, yesterdayDate))

            val cashOutValue = Utility.formatAmount(
                Math.abs(
                    BusinessRepository.getInstance(context)
                        .getCashOutByDate(bookId, yesterdayDate)
                ) + buyingPriceCashOutValue
            )


            val yesterDayDateTime = DateTimeUtils.getYesterdayDateTime()
            val todayDatetime = DateTimeUtils.getCurrentDateTime()
            var frequentProductName = ProductRepository.getInstance(context).getFrequentProductName(bookId, yesterdayDate)
            if (frequentProductName.isNullOrEmpty()) frequentProductName = "-"

            var fiveFrequentProductDtos = ProductRepository.getInstance(context).getTopFiveFrequentProductName(bookId, yesterdayDate)

            val storeName = BusinessRepository.getInstance(context).getBusinessByIdSync(bookId)
            val txnCount = TransactionRepository.getInstance(context).getYesterdaySalesTransactionCountWithBookIdAndDate(bookId, yesterdayDate)
            // script to run "supplyToken(ACCESS_TOKEN, USER_ID, REFERRAL_ID, TAB_INDEX)"
            val reportObj = JsonObject()
            val jsonArray = JsonArray()

            for (productDto in fiveFrequentProductDtos) {
                val dataObject = JsonObject()
                dataObject.addProperty("name", productDto.productName)
                dataObject.addProperty("quantity", productDto.productCount)
                jsonArray.add(dataObject)
            }
            reportObj.add("fequentProduct", jsonArray);

            val script = StringBuilder("javascript:supplyTokenStory(")
            //token, userId, bookId, frequentProductName,storeName,saleValue,expenseValue,creditValue,debitValue
            script.append("'").append(token).append("'").append(",")
            script.append("'").append(userId).append("'").append(",")
            script.append("'").append(bookId).append("'").append(",")
            script.append("'").append(reportObj).append("'").append(",")
            script.append("'").append(storeName.bookName).append("'").append(",")
            script.append("'").append(cashInValue).append("'").append(",")
            script.append("'").append(cashOutValue).append("'").append(",")
            script.append("'").append(creditValue).append("'").append(",") //write query to get this data
            script.append("'").append(debitValue).append("'").append(",")
            script.append("'").append(txnCount).append("'").append(",")
            script.append("'").append(JSONArray(fiveFrequentProductDtos)).append("'").append(",")
            script.append(")")
            view.evaluateJavascript(script.toString(), null)

        }
    }

    internal class JsObject(var dailyUpdateWebviewActivity: DailyUpdateWebviewActivity) {
        @JavascriptInterface
        open fun shareCalled() {
            AppAnalytics.trackEvent(SHARE_DAILY_BUSINESS_UPDATE_CLICK)
            dailyUpdateWebviewActivity.saveImageAndShare()
        }

        @JavascriptInterface
        open fun timerFinished() {
            dailyUpdateWebviewActivity.finish()
        }

        @JavascriptInterface
        open fun onColorAndTextPicked(colorHexString: String, motivationalText: String) {
            val builder = PropBuilder()
            builder.put(AnalyticsConst.MOTIVATIONAL_TEXT, motivationalText)
            builder.put(AnalyticsConst.COLOR_VARIANT, colorHexString)
            AppAnalytics.trackEvent(EVENT_DAILY_BUSINESS_UPDATE_WEBVIEW_LOADED, builder)

        }

        @JavascriptInterface
        override fun toString(): String {
            return "injectedObject"
        }
    }
}