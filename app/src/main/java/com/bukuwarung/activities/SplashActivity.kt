package com.bukuwarung.activities

import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.LoginActivity
import com.bukuwarung.activities.onboarding.NewLoginActivity
import com.bukuwarung.activities.onboarding.WelcomeActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.ClevertapMessageListener.Companion.FCM_DEEPLINK_KEY
import com.bukuwarung.analytics.ClevertapMessageListener.Companion.FCM_WEB_URL_KEY
import com.bukuwarung.base_android.extensions.showAlertDialog
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tracking.TraceConstants
import com.bukuwarung.utils.RemoteConfigFetchCompleteListener
import com.bukuwarung.utils.RemoteConfigUtils.getAuthVariant
import com.bukuwarung.utils.RemoteConfigUtils.shouldShowAppShortcuts
import com.bukuwarung.utils.RemoteConfigUtils.shouldShowNewLoginScreen
import com.bukuwarung.utils.Utility
import com.bukuwarung.widget.PoweredByFooterView
import com.google.firebase.perf.metrics.AddTrace
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import kotlin.system.exitProcess

@AndroidEntryPoint
class SplashActivity : AppCompatActivity(), RemoteConfigFetchCompleteListener {
    var sessionManager = SessionManager.getInstance()
    private var moveToNextScreenComplete = false

    @AddTrace(name = TraceConstants.onCreateSplash)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        val footer = findViewById<PoweredByFooterView>(R.id.powered_by_footer)
        footer.setView(R.color.independence_day_color, null)
        val shouldShowAppShortcuts = shouldShowAppShortcuts()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1 && shouldShowAppShortcuts) {
            createShortcut()
        }
        Handler().postDelayed({ onComplete() }, 500)
    }

    @AddTrace(name = TraceConstants.onShortcutCreation)
    @RequiresApi(api = Build.VERSION_CODES.N_MR1)
    private fun createShortcut() {
        val shortcutManager = getSystemService(ShortcutManager::class.java)
        val infoIntent = Intent(this, WebviewActivity::class.java)
        infoIntent.putExtra(BaseWebviewActivity.LINK, "https://www.bukuwarung.com")
        infoIntent.putExtra(BaseWebviewActivity.TITLE, "About Bukuwarung")
        infoIntent.action = Intent.ACTION_VIEW
        infoIntent.type = getString(R.string.about_bukuwarung)
        val info = ShortcutInfo.Builder(this, getString(R.string.shortcut_1))
            .setShortLabel(getString(R.string.about))
            .setLongLabel(getString(R.string.about))
            .setIcon(Icon.createWithResource(this, R.drawable.ic_about))
            .setIntent(infoIntent)
            .setRank(1)
            .build()
        val shouldShowNewLoginScreen = shouldShowNewLoginScreen()
        val transcationIntent = Intent(
            this@SplashActivity,
            if (shouldShowNewLoginScreen) NewLoginActivity::class.java else LoginActivity::class.java
        )
        transcationIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        transcationIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        transcationIntent.action = Intent.ACTION_VIEW
        transcationIntent.type = getString(R.string.from_splash_login)
        val transaction = ShortcutInfo.Builder(this@SplashActivity, getString(R.string.shortcut_2))
            .setShortLabel(getString(R.string.register))
            .setLongLabel(getString(R.string.register))
            .setIntent(transcationIntent)
            .setIcon(Icon.createWithResource(this, R.drawable.ic_login))
            .setRank(2)
            .build()
        assert(shortcutManager != null)
        shortcutManager!!.dynamicShortcuts = Arrays.asList(info, transaction)
    }

    override fun onComplete() {
        if (!moveToNextScreenComplete) {
            moveToNextScreenComplete = true

            if (Utility.isRooted(this)) {
                showAlertDialog(
                    title = R.string.access_denied,
                    message = getString(R.string.security_check_failed),
                    cancelable = false,
                    positiveButton = R.string.label_close,
                    positiveButtonAction = { _, _ ->
                        finishAffinity()
                        exitProcess(0)
                    },
                    showNegativeButton = false
                )
            } else {
                showNextScreen()
            }
        }
    }

    @AddTrace(name = TraceConstants.onNextScreen)
    private fun showNextScreen() {
        if (!sessionManager.isLoggedIn || getAuthVariant() != 0 && !sessionManager.userLoggedInWithPin && sessionManager.userLoggedInWithPinAtleastOnce) {
            //login
            startActivity(Intent(this@SplashActivity, WelcomeActivity::class.java))
            finish()
        } else {
            var mainIntent = Intent(this@SplashActivity, MainActivity::class.java)
            intent?.extras?.let {
                mainIntent.putExtras(it)
                if (it.containsKey(FCM_DEEPLINK_KEY)) {
                    mainIntent.setAction(Intent.ACTION_VIEW)
                    val url = it.getString(FCM_DEEPLINK_KEY)
                    mainIntent.setData(Uri.parse(url))
                }
                if (it.containsKey(FCM_WEB_URL_KEY)){
                    val url = it.getString(FCM_WEB_URL_KEY)
                    mainIntent = WebviewActivity.createIntent(origin= this, link = url, title = "")
                }
            }
            mainIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            startActivity(mainIntent)
            finish()
        }
    }
}