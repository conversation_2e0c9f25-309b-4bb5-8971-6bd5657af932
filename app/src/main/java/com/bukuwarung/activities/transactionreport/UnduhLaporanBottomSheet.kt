package com.bukuwarung.activities.transactionreport

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.databinding.UnduhLaporanBottomSheetBinding
import com.bukuwarung.enums.ReportFileType

class UnduhLaporanBottomSheet : BaseBottomSheetDialogFragment()  {
    private var listener: UnduhLaporanBottomSheet.DownloadReportListener? = null
    private val startDate by lazy {
        arguments?.getString(UnduhLaporanBottomSheet.START_DATE) ?: ""
    }
    private val endDate by lazy {
        arguments?.getString(UnduhLaporanBottomSheet.END_DATE) ?: ""
    }
    private val transactionType by lazy {
        arguments?.getString(UnduhLaporanBottomSheet.TRANSACTION_TYPE) ?: ""
    }
    private val dateFilterSelected by lazy {
        arguments?.getString(UnduhLaporanBottomSheet.DATE_FILTER_SELECTED) ?: ""
    }
    private val defaultFilter by lazy {
        arguments?.getBoolean(UnduhLaporanBottomSheet.DEFAULT_FILTER) ?: false
    }
    private var fileType:ReportFileType = ReportFileType.PDF;
    private var binding: UnduhLaporanBottomSheetBinding? = null
    public companion object {
        const val TAG = "downloadReportBottomSheet"
        private const val START_DATE = "START_DATE"
        private const val END_DATE = "END_DATE"
        private const val TRANSACTION_TYPE = "transaction_type"
        private const val DATE_FILTER_SELECTED = "DATE_FILTER_SELECTED"
        private const val DEFAULT_FILTER = "DEFAULT_FILTER"
        fun newInstance(startDate: String = "",endDate: String = "",transactionType:String, dateFilterSelected : String,defaultFilter :  Boolean,listener: UnduhLaporanBottomSheet.DownloadReportListener? = null): UnduhLaporanBottomSheet {
            val dialog = UnduhLaporanBottomSheet()
            dialog.listener = listener
            val bundle = Bundle()
            bundle.putString(START_DATE, startDate)
            bundle.putString(END_DATE, endDate)
            bundle.putString(TRANSACTION_TYPE,transactionType)
            bundle.putString(DATE_FILTER_SELECTED,dateFilterSelected)
            bundle.putBoolean(DEFAULT_FILTER,defaultFilter)
            dialog.arguments = bundle
            return dialog
        }
    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = UnduhLaporanBottomSheetBinding.inflate(inflater, container, false)

        binding?.btnReportDownload?.setOnClickListener {
            listener?.downloadReport(startDate,endDate,fileType)
            dismiss()
        }

        binding?.btnShare?.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.START_DATE,startDate)
            propBuilder.put(AnalyticsConst.END_DATE,endDate)
            propBuilder.put(AnalyticsConst.SOURCE,transactionType)
            propBuilder.put(AnalyticsConst.FILE_TYPE,fileType)
            propBuilder.put(AnalyticsConst.DATE_FILTER_SELECTED,dateFilterSelected)
            propBuilder.put(AnalyticsConst.DEFAULT_FILTER,defaultFilter)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_BOOK_REPORT,propBuilder)
            listener?.shareReport(startDate,endDate,fileType)
            dismiss()
        }

        binding?.excelBtn?.setOnClickListener {
            binding?.excelBtn?.background =
                resources.getDrawable(R.drawable.blue_drawable_round_20dp)
            binding?.pdfBtn?.background = resources.getDrawable(R.drawable.grey_drawable_round_20dp)
            fileType=ReportFileType.EXCEL
        }

        binding?.pdfBtn?.setOnClickListener {
            binding?.pdfBtn?.background = resources.getDrawable(R.drawable.blue_drawable_round_20dp)
            binding?.excelBtn?.background =
                resources.getDrawable(R.drawable.grey_drawable_round_20dp)
            fileType=ReportFileType.PDF
        }

        binding?.closeBtn?.setOnClickListener {
            dismiss()
        }
        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }
    interface DownloadReportListener {
        fun downloadReport(startDate: String, endDate: String, fileType: ReportFileType)
        fun shareReport(startDate: String, endDate: String, fileType: ReportFileType)
    }
}