package com.bukuwarung.activities.onboarding.form

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.CategoryListFormFragmentBinding
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.asVisibility
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CategoryListFormFragment : BaseFragment() {
    private lateinit var binding: CategoryListFormFragmentBinding
    private val data = mutableListOf<CategoryOption>()
    private val vm: FormViewModel by activityViewModels()
    private var selectedCategory: CategoryOption? = null

    private var requireReferral: Boolean = false
    private var referralCode: String = ""

    private var formType: FormType = FormType.BusinessCategory

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = CategoryListFormFragmentBinding.inflate(inflater)

        return binding.root
    }

    override fun setupView(view: View) {

        with(requireArguments()) {
            binding.apply {
                tvHeader.text = getString(HEADER_TEXT)
                tvSubHeader.text = getString(SUB_HEADER_TEXT)
                tvSubHeaderLabel.text = getString(SUB_HEADER_LABEL_TEXT)
                tvBody.text = getString(BODY_TEXT)

                val useSubHeaderText = getBoolean(SHOW_SUB_HEADER)
                tvSubHeader.visibility = useSubHeaderText.asVisibility()
                tvSubHeaderLabel.visibility = useSubHeaderText.asVisibility()
                tvBody.visibility = (!useSubHeaderText).asVisibility()

                requireReferral = getBoolean(REQUIRE_REFERRAL, false)
                tilReferral.visibility = requireReferral.asVisibility()
            }
        }

        binding.btnAction.apply {
            setOnClickListener {
                vm.formAction(formType,selectedCategory?.name?:"NA",selectedCategory?.resourceId.toString())
            }
        }

        binding.rvCategory.apply {
            adapter = OnBoardingCategoryAdapter {
                selectedCategory = it
                validateForm()
            }.apply {
                setData(data)
            }
            layoutManager = GridLayoutManager(requireContext(), 3)
        }

        binding.tvReferral.afterTextChanged { txt ->
            referralCode = txt
            validateForm()
        }
    }

    override fun subscribeState() {

    }

    private fun validateForm() {
        binding.btnAction.isEnabled = if (requireReferral) {
            selectedCategory != null && referralCode.trim().isNotEmpty()
        } else {
            selectedCategory != null
        }
    }

    private fun setData(_data: List<CategoryOption>) {
        data.apply {
            clear()
            addAll(_data)
        }
    }

    private fun setFormType(_formType: FormType) {
        formType = _formType
    }

    companion object {
        private const val HEADER_TEXT = "header_text"
        private const val SHOW_SUB_HEADER = "use_sub_header"
        private const val BODY_TEXT = "body_text"
        private const val SUB_HEADER_TEXT = "sub_header_text"
        private const val SUB_HEADER_LABEL_TEXT = "sub_header_text"
        private const val REQUIRE_REFERRAL = "require_referral"

        fun getInstance(
                header: String = "",
                showSubHeader: Boolean = false,
                body: String = "",
                subHeader: String = "",
                subHeaderLabel: String = "",
                requireReferral: Boolean = false,
                formType: FormType = FormType.BusinessCategory,
                data: List<CategoryOption>
        ): CategoryListFormFragment {
            val bundle = Bundle().apply {
                putString(HEADER_TEXT, header)
                putBoolean(SHOW_SUB_HEADER, showSubHeader)
                putString(BODY_TEXT, body)
                putString(SUB_HEADER_TEXT, subHeader)
                putString(SUB_HEADER_LABEL_TEXT, subHeaderLabel)
                putBoolean(REQUIRE_REFERRAL, requireReferral)
            }

            return CategoryListFormFragment().apply {
                arguments = bundle
                setData(data)
                setFormType(formType)
            }
        }
    }
}