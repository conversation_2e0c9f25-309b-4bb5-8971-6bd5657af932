package com.bukuwarung.activities.onboarding

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.provider.Settings
import android.telephony.TelephonyManager
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.AbsoluteSizeSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.Gravity
import android.view.View
import android.widget.Toast
import androidx.core.text.parseAsHtml
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP.SmsCallback
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.DETAIL_CLICK_RETRY
import com.bukuwarung.constants.AnalyticsConst.DETAIL_ENTERED_OTP
import com.bukuwarung.constants.AnalyticsConst.DETAIL_INVALID_OTP
import com.bukuwarung.constants.AnalyticsConst.DETAIL_OTP_CORRECT
import com.bukuwarung.constants.AnalyticsConst.DETAIL_WA_SUCCESS
import com.bukuwarung.constants.AnalyticsConst.EVENT_AUTO_DETECT_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_INPUT_INVALID_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_REGISTRATION_VERIFY_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_REQUEST_WHATSAPP_LOGIN
import com.bukuwarung.constants.AnalyticsConst.EVENT_REQUEST_WHATSAPP_VERIFY
import com.bukuwarung.constants.AnalyticsConst.EVENT_WHATSAPP_AUTO_LOGIN
import com.bukuwarung.constants.AnalyticsConst.STATUS_CANCELLED
import com.bukuwarung.constants.AnalyticsConst.STATUS_COMPLETE
import com.bukuwarung.constants.AnalyticsConst.STATUS_CONNECTED
import com.bukuwarung.constants.AnalyticsConst.STATUS_CONNECTION_FAIL
import com.bukuwarung.constants.AnalyticsConst.STATUS_FAIL
import com.bukuwarung.constants.AnalyticsConst.STATUS_F_AUTH
import com.bukuwarung.constants.AnalyticsConst.STATUS_RECEIVED_TOKEN
import com.bukuwarung.constants.AnalyticsConst.STATUS_START
import com.bukuwarung.constants.AnalyticsConst.STATUS_SUCCESS
import com.bukuwarung.constants.AnalyticsConst.STATUS_WA_LINK
import com.bukuwarung.constants.AppConst.*
import com.bukuwarung.databinding.ActivityVerifyOtpBinding
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import com.google.firebase.firestore.DocumentSnapshot
import java.util.*
import javax.inject.Inject
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VerifyOtpActivity : BaseActivity() {

    companion object {
        const val OTP_LENGTH = 4
        private const val VERIFY_OTP_PARAM_PHONE = "phone"
        private const val VERIFY_OTP_PARAM_OTP = "otp"
        private const val VERIFY_OTP_PARAM_COUNTRY_CODE = "country"
        private const val SHOW_DIALOG = "show_dialog"
        fun createIntent(origin: Activity?, phone: String?, otpCode: String?, countryCode: String, showDialog: Boolean): Intent {
            val intent = Intent(origin, VerifyOtpActivity::class.java)
            intent.putExtra(VERIFY_OTP_PARAM_PHONE, phone)
            intent.putExtra(VERIFY_OTP_PARAM_OTP, otpCode)
            intent.putExtra(VERIFY_OTP_PARAM_COUNTRY_CODE, countryCode.replace("+", ""))
            intent.putExtra(SHOW_DIALOG, showDialog)
            return intent
        }
    }

    private lateinit var binding: ActivityVerifyOtpBinding
    private var hasObserveFirestore = false
    private var phone = ""
    private var countryCode = ""
    private var autoVerify = false
    private var cTimer: CountDownTimer? = null
    private var otpChannel = RemoteConfigUtils.getOtpChannel()
    private val showDefaultOtpScreen = RemoteConfigUtils.shouldShowDefaultOtpScreen()

    private val viewModel: VerifyOtpViewModel by viewModels()

    //timer function
    private var autoDetectOTP: AutoDetectOTP? = null
    private var autoDetect = false
    override fun setViewBinding(){
        binding = ActivityVerifyOtpBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }
    override fun setupView() {
        lifecycle.addObserver(viewModel)
        try {
            setAutoDetectOTP()
            binding.backBtn.setOnClickListener { onBackPressed() }
            binding.btnHelp.setOnClickListener {
                openWaBotHelp()
            }
            binding.txtTryAgain.setOnClickListener { tryOTPAgain() }
            binding.btnManualVerification.setOnClickListener { requestWhatsappCode() }
            binding.skipLogin.setOnClickListener {
                binding.loadingPanel.visibility = View.VISIBLE
                binding.bottomContainer.visibility = View.GONE
                binding.verifyTxt.text = getString(R.string.preparing)
                viewModel.onEventReceived(VerifyOtpViewModel.Event.OnRequestGuestSession)
            }
            if (intent.hasExtra("token")) {
                val intent = intent
                val token = intent.getStringExtra("token")
                phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) ?: ""
                countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) ?: ""
                autoVerify = true
                showVerifying(true)
                viewModel.onEventReceived(VerifyOtpViewModel.Event.AfterOtpVerify(token, phone, autoVerify))
            } else if (intent.hasExtra(VERIFY_OTP_PARAM_PHONE)) {
                val intent = intent
                phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) ?: ""
                countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) ?: ""

                //            this.otp = intent.getStringExtra("otp");
            } else {
                callLoginActivity()
                return
            }


            viewModel.onEventReceived(VerifyOtpViewModel.Event.OnCreateView(countryCode, phone))
            showNoOtpWarning()
            binding.inputOtp.afterTextChanged {
                if (binding.inputOtp.text.length == OTP_LENGTH) {
                    verifyOtp(binding.inputOtp.text.toString())
                } else {
                    binding.otpErrorText.visibility = View.GONE
                }
            }

            val otpChannelUsed = if (otpChannel == NotificationChannel.SMS.value) getString(R.string.sms) else getString(R.string.whatsapp)
            binding.phoneNumber.apply {
                val phoneNum = "+$countryCode$phone"
                val formattedMsg = if (showDefaultOtpScreen) {
                    getString(R.string.please_enter_otp, phoneNum)
                } else {
                    getString(R.string.otp_message_placeholder, otpChannelUsed, phoneNum)
                }

                text = formattedMsg.parseAsHtml()
            }

            binding.tvChangeOtpChannel.apply {
                val newChannel = if (otpChannel == NotificationChannel.SMS.value) getString(R.string.whatsapp) else getString(R.string.sms)
                val formattedText = getString(R.string.otp_change_channel_placeholder, newChannel)
                val stringBuilder = SpannableStringBuilder(formattedText).apply {
                    val boldStyle = StyleSpan(Typeface.BOLD)
                    setSpan(boldStyle, 5, formattedText.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

                    val blueColor = ForegroundColorSpan(getColorCompat(R.color.blue_60))
                    setSpan(blueColor, 5, formattedText.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

                    val sizeSpan = AbsoluteSizeSpan(14.sp)
                    setSpan(sizeSpan, 5, formattedText.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                }

                text = stringBuilder
                setOnClickListener {
                    val newChannel = if (otpChannel == NotificationChannel.SMS.value)  NotificationChannel.WA.value else NotificationChannel.SMS.value
                    trackRetryOtpRequest(otpChannel, newChannel, true)
                    viewModel.onEventReceived(VerifyOtpViewModel.Event.RequestNewOtp(false))
                }
            }

            if (!showDefaultOtpScreen) {
                Toast.makeText(this, getString(R.string.otp_sent_placeholder, otpChannelUsed), Toast.LENGTH_SHORT).apply {
                    setGravity(Gravity.TOP, 0, 20)
                }.show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        binding.inputOtp.requestFocus()
    }

    private fun setAutoDetectOTP() {
        try {
            autoDetectOTP = AutoDetectOTP(this)
            autoDetectOTP?.startSmsRetriver(object : SmsCallback {
                override fun connectionfailed() {
                    AppAnalytics.trackEvent(EVENT_AUTO_DETECT_OTP, STATUS_CONNECTION_FAIL, "")
                }

                override fun connectionSuccess(aVoid: Void?) {
                    AppAnalytics.trackEvent(EVENT_AUTO_DETECT_OTP, STATUS_CONNECTED, AutoDetectOTP.getHashCode(this@VerifyOtpActivity))
                }

                override fun smsCallback(sms: String) {
                    try {
                        if (sms.contains("adalah") || sms.contains("BukuWarung")) {
                            val otp = sms.substring(0, 5).trim { it <= ' ' }
                            AppAnalytics.trackEvent(EVENT_AUTO_DETECT_OTP, STATUS_SUCCESS, "")
                            binding.inputOtp.setText(otp)
                            autoDetect = true
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is VerifyOtpViewModel.State.Error -> handleError(it.message)
                is VerifyOtpViewModel.State.ShowLoading -> showVerifying(it.isLoading)
                VerifyOtpViewModel.State.WrongOtp -> handleWrongOtp()
                VerifyOtpViewModel.State.FirebaseAuthError -> handleFirebaseAuthError()
                VerifyOtpViewModel.State.StartAfterVerifyOtp -> handleStartAfterVerifyOtp()
                VerifyOtpViewModel.State.SuccessSignInFirebase -> handleOnFirebaseSignIn()
                VerifyOtpViewModel.State.ProceedWithLogin -> proceedWithLogin()
                is VerifyOtpViewModel.State.SkipUserLogin -> skipUserLogin(it.userId, it.propBuilder)
                is VerifyOtpViewModel.State.OtpChannelChanged -> otpChannel = it.channel
                VerifyOtpViewModel.State.NewOtpRequested -> {
                    cTimer?.cancel()
                    binding.inputOtp.setText("")

                    binding.progressBar.visibility = View.VISIBLE
                    binding.bottomContainer.visibility = View.GONE
                    binding.tvChangeOtpChannel.visibility = View.GONE
                    binding.btnManualVerification.visibility = View.GONE
                    binding.skipLogin.visibility = View.GONE
                    binding.btnHelp.visibility = View.GONE
                    binding.tvChangeOtpChannel.visibility = View.GONE
                    binding.tryAgainContainer.visibility = View.GONE
                    binding.otpErrorText.visibility = View.GONE
                }
                VerifyOtpViewModel.State.NewOtpRequestSuccess -> {
                    binding.progressBar.visibility = View.GONE
                    binding.bottomContainer.visibility = View.VISIBLE
                    setupView()
                }
                is VerifyOtpViewModel.State.OtpRequestError -> handleError(it.message)
                else -> {}
            }
        }
    }

    private fun skipUserLogin(userId: String, propBuilder: AppAnalytics.PropBuilder) {
        AppAnalytics.trackEvent(AnalyticsConst.SKIP_LOGIN,propBuilder)
        MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
        finish()
    }
    private fun trackRegistrationVerifyOtpAnalytics(status: String, detail: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.DETAIL, detail)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        AppAnalytics.trackEvent(EVENT_REGISTRATION_VERIFY_OTP, propBuilder)
    }

    private fun trackRetryOtpRequest(currentMethod: String, newMethod: String, isChanged: Boolean){
        val propBuilder = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.CURRENT_METHOD, currentMethod)
            put(AnalyticsConst.NEW_METHOD, newMethod)
            put(AnalyticsConst.IS_OTP_METHOD_CHANGED, isChanged)
        }

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_RESEND_OTP_CLICKED, propBuilder)
    }

    private fun handleOnFirebaseSignIn() {
        if (autoVerify) {
            AppAnalytics.trackEvent(EVENT_WHATSAPP_AUTO_LOGIN, STATUS_COMPLETE, phone)
            trackRegistrationVerifyOtpAnalytics(STATUS_SUCCESS, DETAIL_WA_SUCCESS)
        } else {
            trackRegistrationVerifyOtpAnalytics(STATUS_SUCCESS, DETAIL_OTP_CORRECT)
        }
    }

    private fun handleStartAfterVerifyOtp() {
        trackRegistrationVerifyOtpAnalytics(STATUS_RECEIVED_TOKEN, DETAIL_ENTERED_OTP)
    }

    private fun handleFirebaseAuthError() {
        // If sign in fails, display a message to the user.
        onTimerFinish()
        binding.otpErrorText.visibility = View.VISIBLE
        binding.tryAgainContainer.visibility = View.GONE
        AppAnalytics.trackEvent(EVENT_INPUT_INVALID_OTP, STATUS_F_AUTH, phone)
        showVerifying(false)
    }

    private fun handleError(message: String) {
        trackRegistrationVerifyOtpAnalytics(STATUS_FAIL, DETAIL_INVALID_OTP)
        binding.otpErrorText.text = message
        binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
        onTimerFinish()
        binding.tryAgainContainer.visibility = View.GONE
        binding.otpErrorText.visibility = View.VISIBLE
        binding.loadingPanel.visibility = View.GONE
        binding.bottomContainer.visibility = View.VISIBLE
    }

    private fun handleWrongOtp() {
        trackRegistrationVerifyOtpAnalytics(STATUS_FAIL, DETAIL_INVALID_OTP)
        val message = getString(R.string.otp_error_wrong)
        val stringBuilder = SpannableStringBuilder(message)
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                tryOTPAgain()
            }
        }
        //bold WA atau SMS
        stringBuilder.setSpan(clickableSpan, 16, 25, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        // OTP code error
        binding.otpErrorText.text = stringBuilder
        binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
        onTimerFinish()
        binding.tryAgainContainer.visibility = View.GONE
        binding.otpErrorText.visibility = View.VISIBLE
    }

    private fun tryOTPAgain() {
        trackRetryOtpRequest(otpChannel, otpChannel, false)
        trackRegistrationVerifyOtpAnalytics(STATUS_CANCELLED, DETAIL_CLICK_RETRY)
        callLoginActivity()
        finish()
    }

    public override fun onDestroy() {
        super.onDestroy()
        cancelTimer()
    }

    private fun callLoginActivity() {
        val intent = LoginActivity.createIntent(this, skipToLogin = true)
        startActivity(intent)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        callLoginActivity()
    }

    private fun showNoOtpWarning() {
        binding.textCounter.visibility = View.VISIBLE
        cTimer = object : CountDownTimer(OTP_WAIT_TIME * ONE_SECOND, ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                val counter = getString(R.string.retry_in) + " " + millisecondsToTime(millisUntilFinished)
                binding.textCounter.text = counter
            }

            override fun onFinish() {
                onTimerFinish()
            }
        }
        cTimer?.start()
    }

    private fun onTimerFinish() {
        cancelTimer()
        // we don't want to show both at the same time
        if (binding.otpErrorText.visibility == View.VISIBLE) {
            binding.tryAgainContainer.visibility = View.GONE
        } else {
            binding.tryAgainContainer.visibility = View.VISIBLE
        }
        binding.textCounter.visibility = View.GONE
        binding.btnManualVerification.visibility = View.VISIBLE
        binding.skipLogin.visibility = View.VISIBLE
        binding.btnHelp.visibility = View.VISIBLE
        binding.tvChangeOtpChannel.visibility = (!showDefaultOtpScreen).asVisibility()
    }

    //cancel timer
    private fun cancelTimer() {
        cTimer?.cancel()
    }

    private fun millisecondsToTime(milliseconds: Long): String {
        val minutes = milliseconds / 1000 / 60
        val seconds = milliseconds / 1000 % 60
        val secondsStr = seconds.toString()
        val secs = if (secondsStr.length >= 2) secondsStr.substring(0, 2)
        else "0$secondsStr"
        return "$minutes:$secs"
    }

    private fun proceedWithLogin() {
        try {
            MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
            MainActivity().sendAppsFlyerId()
            finish()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getDeviceIMEI(): String? {
        var deviceUniqueIdentifier: String? = null
        val tm = this.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        if (Build.VERSION.SDK_INT >= 26) {
            if (tm.getPhoneType() == TelephonyManager.PHONE_TYPE_CDMA) {
                deviceUniqueIdentifier = tm.getMeid();
            } else if (tm.getPhoneType() == TelephonyManager.PHONE_TYPE_GSM) {
                deviceUniqueIdentifier = tm.getImei();
            } else {
                deviceUniqueIdentifier =null; // default!!!
            }
        } else {
            deviceUniqueIdentifier = tm.getDeviceId();
        }
        if (null != tm) {
            deviceUniqueIdentifier = tm.deviceId
        }
        if (null == deviceUniqueIdentifier || 0 == deviceUniqueIdentifier.length) {
            deviceUniqueIdentifier =
                Settings.Secure.getString(this.contentResolver, Settings.Secure.ANDROID_ID)
        }
        return deviceUniqueIdentifier
    }

    fun verifyOtp(otp: String) {
        binding.otpErrorText.visibility = View.GONE
        binding.otpErrorText.text = null
        if (!Utility.hasInternet()) {
            onTimerFinish()
            binding.tryAgainContainer.visibility = View.GONE
            binding.otpErrorText.visibility = View.VISIBLE
            val stringBuilder = SpannableStringBuilder()
            stringBuilder.append(getString(R.string.otp_no_internet))
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(view: View) {
                    try {
                        verifyOtp(binding.inputOtp.text.toString())
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                }
            }
            stringBuilder.setSpan(clickableSpan, 75, stringBuilder.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            // OTP code error
            binding.otpErrorText.text = stringBuilder
            binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
            return
        }
        if (Utility.invalidOTPExceeded()) {
            onTimerFinish()
            binding.tryAgainContainer.visibility = View.GONE
            binding.otpErrorText.visibility = View.VISIBLE
            binding.otpErrorText.text = getString(R.string.otp_too_many)
            return
        }
        trackRegistrationVerifyOtpAnalytics(STATUS_START, DETAIL_ENTERED_OTP)
        val androidId: String? = try {
            Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
        InputUtils.hideKeyboard(this)
        val imeiNumber: String? = try {
            getDeviceIMEI()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }

        val advertisingId: String? = SessionManager.getInstance().advertisingId
        val wideVineId: String? = DeviceUtils.getWideVineId()
        val rooted: Boolean = Utility.isRooted(this)
        viewModel.onEventReceived(VerifyOtpViewModel.Event.OnVerifyOtp(
                phone, countryCode.replace("+", ""), otp, androidId, imeiNumber, wideVineId, advertisingId, rooted, autoVerify))
    }

    private fun showVerifying(show: Boolean) {
        if (show) {
            binding.verifyTxt.text = getString(R.string.verifying_number)
            binding.loadingPanel.visibility = View.VISIBLE
            binding.bottomContainer.visibility = View.GONE
        } else {
            binding.loadingPanel.visibility = View.GONE
            binding.bottomContainer.visibility = View.VISIBLE
        }
    }

    private fun openWaBotHelp() {
        var bundle = Bundle()
        bundle.putString(AnalyticsConst.ENTRY_POINT,AnalyticsConst.LOGIN_OTP_PAGE)
        WhatsAppUtils.openWABotWithHelpText(this, getString(R.string.wa_help_text_general), bundle)
    }

    private fun requestWhatsappCode() {
        AppAnalytics.trackEvent(EVENT_REQUEST_WHATSAPP_VERIFY, STATUS_WA_LINK, phone)
        viewModel.onEventReceived(VerifyOtpViewModel.Event.DeleteFirestoreData(phone))
        if (!hasObserveFirestore) {
            viewModel.listenToFirestoreWaAuth().observe(this) { list ->
                for (dc in list) {
                    val doc: DocumentSnapshot = dc.document
                    var token: String? = null
                    var reqPhone: String? = null
                    try {
                        token = doc["token"] as String?
                        reqPhone = doc["phone"] as String?
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                    if (reqPhone != null && !token.isNullOrBlank() && reqPhone == phone) {
                        autoVerify = true
                        viewModel.onEventReceived(VerifyOtpViewModel.Event.AfterOtpVerify(token, phone, autoVerify))
                    }
                }
            }
            hasObserveFirestore = true
        }
        try {
            val sb = StringBuilder()
            sb.append(getString(R.string.please_verify, phone))
            try {
                val phoneNumberWithCountryCode = WA_BW_NUMBER
                val url = String.format(WA_INTENT_URL,
                        phoneNumberWithCountryCode, sb.toString())
                val packageName = ShareUtils.getWhatsAppPackageName(this, url, false)
                if (packageName == null || !packageName.equals("BOTH", ignoreCase = true)) {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    if (packageName != null) intent.setPackage(packageName)
                    startActivity(intent)
                } else {
                    // will be handled in dialog picker
                }
            } catch (e: ActivityNotFoundException) {
                NotificationUtils.alertToast(getString(R.string.whatsapp_not_installed))
            }
            AppAnalytics.trackEvent(EVENT_REQUEST_WHATSAPP_LOGIN)
        } catch (e: Exception) {
        }
    }
}
