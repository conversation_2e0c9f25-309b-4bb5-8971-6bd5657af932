package com.bukuwarung.activities.onboarding.form

import android.app.TimePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.RadioButton
import androidx.fragment.app.activityViewModels
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileFragment
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.OperationHourSelectorFragmentBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isNotNullOrEmpty
import java.util.*
import javax.inject.Inject


class OperationHourSelectorFragment : BaseFragment() {
    private lateinit var binding: OperationHourSelectorFragmentBinding
    private val data = mutableListOf<CategoryOption>()
    private val vm: DetailedBusinessFormViewModel by activityViewModels()
    private var bookEntity: BookEntity? = null
    private var bookId:String = ""

    private var formType: FormType = FormType.BusinessCategory

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        if (arguments != null) {
            if (requireArguments().containsKey(NgBusinessProfileFragment.BOOK_ID)) {
                bookId = requireArguments().getString(NgBusinessProfileFragment.BOOK_ID).toString()
            }
        }
        if(bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(context)?.getBusinessByIdSync(bookId)
        } else {
            bookEntity = BookEntity()
        }
        binding = OperationHourSelectorFragmentBinding.inflate(inflater)
        return binding.root
    }

    override fun setupView(view: View) {

        with(requireArguments()) {
            binding.apply {
                tvToolBarTitle.text = getString(HEADER_TEXT)
                btnAction.text = getString(ACTION_BUTTON_TEXT)
                val useToolbar = getBoolean(SHOW_HEADER)
                val useSubmitButton = getBoolean(SHOW_ACTION_BUTTON)
                toolbar.visibility = useToolbar.asVisibility()
                btnAction.visibility = useSubmitButton.asVisibility()
            }
        }

        binding.btnAction.setOnClickListener {
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.ENTRY_POINT2, "business_profile")
            val radioButton = binding.rgOperationDays.findViewById<View>(binding.rgOperationDays.checkedRadioButtonId) as RadioButton
            prop.put("business_operating_days", radioButton.text.toString())
            prop.put(
                "business_operating_hours",
                binding.etMinute.text.toString().plus("-")
                    .plus(binding.etHour.text.toString())
            )
            AppAnalytics.trackEvent("save_business_operations_time", prop)
            vm.setBusinessTiming(binding.rgOperationDays.checkedRadioButtonId,radioButton.text.toString(),binding.etHour.text.toString(),binding.etMinute.text.toString(),binding.cbAlwaysOpen.isChecked)
            requireActivity().supportFragmentManager.beginTransaction().remove(this).commit()
            requireActivity().supportFragmentManager.popBackStack()
        }

        binding.backBtn.setOnClickListener {
            requireActivity().supportFragmentManager.beginTransaction().remove(this).commit()
        }
        if(vm.bookData?.operatingHourEnd.isNotNullOrEmpty() && vm.checkedRadioId>0){
            binding.etMinute.setText(vm.bookData?.operatingHourEnd)
            binding.etHour.setText(vm.bookData?.operatingHourStart)
            binding.rgOperationDays.check(vm.checkedRadioId)
            binding.cbAlwaysOpen.setChecked(vm.alwaysOpenFlag)
            validateForm()
        }

        binding.etHour.setOnClickListener {
            setSelectedTime(binding.etHour)
        }
        binding.etMinute.setOnClickListener {
            setSelectedTime(binding.etMinute)
        }
        binding.cbAlwaysOpen.setOnCheckedChangeListener {_, b ->
            if(b) {
                binding.etHour.setText("00:00")
                binding.etMinute.setText("00:00")
                binding.etHour.setEnabled(false)
                binding.etMinute.setEnabled(false)
            }else{
                binding.etHour.setEnabled(true)
                binding.etMinute.setEnabled(true)
            }
        }

        binding.rgOperationDays.setOnCheckedChangeListener { group, checkedId ->  validateForm()}
    }

    fun setSelectedTime(etTime:EditText){
        val c: Calendar = Calendar.getInstance()
        val hour: Int = c.get(Calendar.HOUR_OF_DAY)
        val minute: Int = c.get(Calendar.MINUTE)
        val timePickerDialog = TimePickerDialog(requireActivity(),
            { timePicker, selectedHour, selectedMinute -> etTime.setText("$selectedHour:$selectedMinute") },
            hour,
            minute,
            true
        )
        timePickerDialog.show()
    }

    override fun subscribeState() {

    }

    private fun validateForm() {
        binding.btnAction.isEnabled = binding.rgOperationDays.checkedRadioButtonId != null
    }

    private fun setData(_data: List<CategoryOption>) {
        data.apply {
            clear()
            addAll(_data)
        }
    }

    private fun setFormType(_formType: FormType) {
        formType = _formType
    }

    companion object {
        private const val HEADER_TEXT = "header_text"
        private const val SHOW_HEADER = "show_header"
        private const val SHOW_ACTION_BUTTON = "show_action_button"
        private const val ACTION_BUTTON_TEXT = "action_button_text"
        const val BOOK_ID = "BOOK_ID"
        fun getInstance(
            header: String = "Jam Buka",
            actionBtnText: String = "Simpan",
            showHeader: Boolean = true,
            showActionButton: Boolean = true,
            formType: FormType = FormType.BusinessCategory,
            data: List<CategoryOption>,
            targetBookId:String
        ): OperationHourSelectorFragment {
            val bundle = Bundle().apply {
                putString(HEADER_TEXT, header)
                putString(ACTION_BUTTON_TEXT, actionBtnText)
                putBoolean(SHOW_HEADER, showHeader)
                putBoolean(SHOW_ACTION_BUTTON, showActionButton)
                putString(BOOK_ID, targetBookId)
            }

            return OperationHourSelectorFragment().apply {
                arguments = bundle
                setData(data)
                setFormType(formType)
            }
        }
    }
}