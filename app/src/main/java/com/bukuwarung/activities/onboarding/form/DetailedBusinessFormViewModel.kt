package com.bukuwarung.activities.onboarding.form

import android.graphics.Bitmap
import androidx.lifecycle.*
import com.bukuwarung.Application
import com.bukuwarung.activities.BaseViewModelEventState
import com.bukuwarung.activities.ViewModelEvent
import com.bukuwarung.activities.ViewModelState
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.domain.payments.RiskUseCase
import com.bukuwarung.payments.data.model.BookValidationRequest
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.AppIdGenerator
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.UploadTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class DetailedBusinessFormViewModel @Inject constructor(
    private val riskUseCase: RiskUseCase,
    private val bankingUseCase: BankingUseCase,
    private val paymentUseCase: PaymentUseCase
) :
    BaseViewModelEventState<DetailedBusinessFormViewModel.State, DetailedBusinessFormViewModel.Event>() {
    var bookData: BookEntity = BookEntity()
    val bookDataLive: MutableLiveData<BookEntity> = MutableLiveData()
    val edcPaymentBook: MutableLiveData<String?> = MutableLiveData()
    var checkedRadioId:Int = -1
    var alwaysOpenFlag:Boolean = false
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    sealed class State : ViewModelState{
        object OpenAddressFlow : State()
    }

    sealed class Event : ViewModelEvent{
        data class BookValidationError(val bookName: String?) : Event()
        object BookValidationSuccess: Event()
        object RequestToOpenAddressFlow : Event()
    }

    override fun onEventReceipt(event: Event) {
        when(event){
            Event.RequestToOpenAddressFlow -> setState(State.OpenAddressFlow)
            else -> {}
        }
    }

    fun categoryFormAction(formType: FormType, formValue: String, formValueId: String = "") = viewModelScope.launch {
        when (formType) {
            FormType.BusinessCategory -> setBusinessCategory(formValue,formValueId)
            FormType.BusinessTiming -> setBusinessCategory(formValue,formValueId)
            else -> {}
        }
    }

    private fun setBusinessCategory(businessCategory: String, businessCategoryId:String) {
        bookData.bookTypeName = businessCategory
        bookData.bookType = businessCategoryId.toInt()
        bookDataLive.postValue(bookData)
    }

    fun setBusinessTiming(id:Int,operationDays: String, operationHourStart:String, operationHourEnd:String,cdAlwaysOpen:Boolean = false) {
        checkedRadioId = id
        bookData.operatingDays = operationDays
        bookData.operatingHourStart = operationHourStart
        bookData.operatingHourEnd = operationHourEnd
        alwaysOpenFlag = cdAlwaysOpen
        bookDataLive.postValue(bookData)
    }

    fun setBusinessData(bookEntity: BookEntity){
        if(bookEntity!=null){
            bookData = bookEntity
        }
        bookDataLive.postValue(bookData)
    }

    fun setBusinessAddress(address: Address){
        bookData .apply {
            businessAddress = address.fullAddress
            province = address.province
            city = address.city
            district = address.district
            subdistrict = address.subDistrict
            postalCode = address.postalCode
        }
        bookDataLive.postValue(bookData)
    }

    fun saveBusinessDetails() {
        if(bookData.bookId.isNullOrEmpty()){
            bookData.bookId = AppIdGenerator.resourceUUID()
            bookData.ownerId = User.getUserId()
        }
        EntityHelper.fillBusinessMetadata(bookData)
        BusinessRepository.getInstance(Application.getAppContext()).insertBookSync(bookData)
    }

    fun checkBusinessNameValidation(name: String?) =
        viewModelScope.launch {
            withContext(Dispatchers.Main) {
                var bookValidation: ApiResponse<Any>? = null
                coroutineScope {
                    name?.let {
                        launch {
                            bookValidation =
                                riskUseCase.validateBookName(BookValidationRequest(it))
                        }
                    }
                }
                bookValidation?.let { handleBookValidation(it, name) }
            }
        }

    private fun handleBookValidation(validationResponse: ApiResponse<Any>, bookName: String?) = viewModelScope.launch {
        when (validationResponse) {
            is ApiErrorResponse -> {
                if (validationResponse.statusCode == 422) {
                    val blockedBook = PaymentUtils.parseBlacklistedBookName(validationResponse.errorMessage)
                    eventStatus.value = Event.BookValidationError(blockedBook ?: bookName)
                }
            }
            is ApiSuccessResponse -> {
                eventStatus.value = Event.BookValidationSuccess
            }
            else -> {}
        }
    }

    fun updateQrisBookName(bookId: String, bookName: String) = viewModelScope.launch {
        bankingUseCase.updateQrisBookName(bookId, bookName)
    }

    fun uploadToFirebase(businessId:String,bitmap: Bitmap?,forceSave:Boolean=false) {
        var bookId = businessId
        var userId = User.getUserId()
        if(bookId.isNullOrEmpty()){
            bookId = AppIdGenerator.resourceUUID()
        }
        bitmap?.let {
            val baos = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos)
            val data: ByteArray = baos.toByteArray()
            val imageLocationRef = FirebaseStorage.getInstance().reference.child( "profile/$userId/$bookId.jpg")

            val uploadTask: UploadTask = imageLocationRef.putBytes(data)

            uploadTask.addOnFailureListener { }.addOnSuccessListener {
                val result = it.metadata!!.reference!!.downloadUrl;
                result.addOnSuccessListener {
                    val imageLink = it.toString()
                    if(forceSave){
                        BusinessRepository.getInstance(Application.getAppContext()).updateBusinessProfileImage(bookId,imageLink,0)
                    }
                    bookData.businessImage = imageLink
                    bookData.businessImageUploadPending = 0
                    bookDataLive.postValue(bookData)
                }
            }
        }
    }

    fun fetchEDCDeviceDetails() = viewModelScope.launch {
        when(val response = paymentUseCase.fetchDeviceDetails()) {
            is ApiSuccessResponse -> {
                SessionManager.getInstance().paymentAccountId= response.body.data?.paymentAccountId;
                edcPaymentBook.postValue(response.body.data?.paymentAccountId)
            }

            is ApiErrorResponse -> {
                edcPaymentBook.postValue(null)
            }
            else -> {}
        }
    }
}