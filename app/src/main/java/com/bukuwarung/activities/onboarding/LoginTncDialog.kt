package com.bukuwarung.activities.onboarding

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewOutlineProvider
import android.webkit.WebViewClient
import com.bukuwarung.R
import com.bukuwarung.databinding.LayoutLoginTncBinding
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType

class LoginTncDialog(
    private val urlWebView: String,
    context: Context,
    private val callback: () -> Unit
) : BaseDialog(context, BaseDialogType.FULL_SCREEN) {
    private lateinit var binding: LayoutLoginTncBinding

    init {
        setUseFullWidth(false)
        setCancelable(true)
    }

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = LayoutLoginTncBinding.inflate(LayoutInflater.from(context))
        setupViewBinding(binding.root)

        binding.webviewContent.webViewClient = WebViewClient()
        binding.webviewContent.loadUrl(urlWebView)
        binding.btOkay.setOnClickListener {
            callback()
            dismiss()
        }
        this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 40f)
                }
            }
            binding.clTnc.outlineProvider = provider
            binding.clTnc.clipToOutline = true
        }
    }
}