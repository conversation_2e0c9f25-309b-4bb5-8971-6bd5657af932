package com.bukuwarung.activities.onboarding.helper.onboarding

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.webkit.*
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.onboarding.helper.onboarding.OnBoardingRedirection.*
import com.bukuwarung.activities.profile.update.BusinessProfileFormViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.OnboardingWebViewLayoutBinding
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.debugToast
import com.bukuwarung.utils.recordException
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*

class OnBoardingWebViewDialog(
    context: Context,
    val referralCodeFromDeeplink: String,
    val viewModel: BusinessProfileFormViewModel
) :
    BaseDialog(context, BaseDialogType.POPUP) {
    private var interfaceName: String = "OnBoardingWebView"
    private lateinit var binding: OnboardingWebViewLayoutBinding

    init {
        binding = OnboardingWebViewLayoutBinding.inflate(LayoutInflater.from(context))
    }

    private fun getLink(): String = RemoteConfigUtils.OnBoarding.getOnBoardingUrl()
    private val client = object : WebViewClient() {
        override fun onReceivedError(
            view: WebView?,
            request: WebResourceRequest?,
            error: WebResourceError?
        ) {
            binding.errorState.visibility = View.VISIBLE
            binding.webView.visibility = View.GONE
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            val forms = RemoteConfigUtils.OnBoarding.getOnBoardingForms()
            val showReferralField = referralCodeFromDeeplink.isNotEmpty()
            val token = SessionManager.getInstance().bukuwarungToken ?: ""
            val script = """javascript:supplyConfig($forms, $showReferralField, $token)"""
            view?.evaluateJavascript(script, null)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupViewBinding(binding.root)
        setupClient()
        trackOnOpenEvent()
        setCancellable(false)
        setUseFullWidth(true)
    }

    override fun getResId(): Int = 0

    private fun setupClient() {
        binding.webView.apply {
            settings.javaScriptEnabled = true
            addJavascriptInterface(this@OnBoardingWebViewDialog, interfaceName)
            webViewClient = client

            val referralCode = ReferralPrefManager.getInstance().referralCode
            loadUrl(getLink())
        }
    }

    private fun trackOnOpenEvent() {
        try {
            val forms = RemoteConfigUtils.OnBoarding.getOnBoardingForms()
            val listType = object : TypeToken<List<String>>() {}.type
            val formsList = Gson().fromJson<List<String>>(forms, listType)
            val pageType = formsList.joinToString(separator = "_")
            val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.PAGE_TYPE, pageType)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_BUSINESS_DETAIL_DIALOG, prop)
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    @JavascriptInterface
    fun submitForm(
        businessName: String?,
        typeName: String?,
        type: String?,
        pastUsage: String?,
        goalUsage: String?,
        referral: String?
    ) {
        try {
            context.debugToast(goalUsage.toString() + " " + referral.toString())
            goalUsage ?: return
            val tabName = when (RemoteConfigUtils.OnBoarding.getRedirectionRule()) {
                USAGE_GOAL -> {
                    try {
                        when (goalUsage.uppercase(Locale.getDefault())) {
                            TabName.TRANSACTION.name -> TabName.TRANSACTION
                            TabName.CUSTOMER.name -> TabName.CUSTOMER
                            TabName.STOCK.name -> {
                                viewModel.setStockTabEnabled(true)
                                TabName.STOCK
                            }
                            TabName.PAYMENT.name, "PULSA" -> TabName.PAYMENT
                            else -> TabName.TRANSACTION
                        }
                    } catch (ex: Exception) {
                        TabName.TRANSACTION
                    }
                }
                DEFAULT -> TabName.TRANSACTION
            }

            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.BUSINESS_NAME, businessName)
                put(AnalyticsConst.BUSINESS_TYPE_NAME, typeName)
                put(AnalyticsConst.BUSINESS_TYPE, type)
                put(AnalyticsConst.USAGE_GOAL_PAGE, goalUsage)
                put(AnalyticsConst.USAGE_PAST_PAGE, pastUsage)
                if (!referral.isNullOrEmpty()) {
                    put(AnalyticsConst.REFERRAL_CODE, referral)
                    put("referral_input_method", "deeplink")
                    put("entered_referral_code", referral)
                }
            }
            AppAnalytics.trackEvent(AnalyticsConst.LANDING_POPUP, prop)

            AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_NAME, businessName ?: "")
            AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE_NAME, typeName ?: "")
            AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_TYPE, type ?: "")
            AppAnalytics.setUserProperty(AnalyticsConst.USAGE_GOAL_PAGE, goalUsage ?: "")

            trackOnOpenEvent()
            viewModel.updateBusiness(businessName ?: "", type?.toInt() ?: 0, typeName ?: "")
            MainActivity.startActivityClearTopToTab(context, tabName)
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    @JavascriptInterface
    fun onClickNextBusinessName(businessName: String?, referral: String?) {
        try {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.PAGE_NAME, AnalyticsConst.BUSINESS_NAME_PAGE)
                put(AnalyticsConst.BUSINESS_NAME_PAGE, businessName)
                if (!referral.isNullOrEmpty()) {
                    put(AnalyticsConst.REFERRAL_CODE, referral)
                }
            }
            context.debugToast(prop.buildAmp().toString())
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
                prop
            )
        } catch (ex: Exception) {
            ex.recordException()
        }

    }

    @JavascriptInterface
    fun onClickNextBusinessCategory(categoryType: String?, referral: String?) {
        try {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.PAGE_NAME, AnalyticsConst.BUSINESS_CATEGORY_PAGE)
                put(AnalyticsConst.TYPE, categoryType)
                if (!referral.isNullOrEmpty()) {
                    put(AnalyticsConst.REFERRAL_CODE, referral)
                }
            }
            context.debugToast(prop.buildAmp().toString())
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
                prop
            )
        } catch (ex: Exception) {
            ex.recordException()
        }

    }

    @JavascriptInterface
    fun onClickNextUsagePast(pastUsage: String?, referral: String?) {
        try {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.PAGE_NAME, AnalyticsConst.USAGE_PAST_PAGE)
                put(AnalyticsConst.USAGE_PAST_PAGE, pastUsage)
                if (!referral.isNullOrEmpty()) {
                    put(AnalyticsConst.REFERRAL_CODE, referral)
                }
            }
            context.debugToast(prop.buildAmp().toString())
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
                prop
            )
        } catch (ex: Exception) {
            ex.recordException()
        }

    }

    @JavascriptInterface
    fun onClickNextUsageGoal(goal: String?, referral: String?) {
        try {
            val prop = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.PAGE_NAME, AnalyticsConst.USAGE_GOAL_PAGE)
                put(AnalyticsConst.USAGE_GOAL_PAGE, goal)
                if (!referral.isNullOrEmpty()) {
                    put(AnalyticsConst.REFERRAL_CODE, referral)
                }
            }
            context.debugToast(prop.buildAmp().toString())
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
                prop
            )
        } catch (ex: Exception) {
            ex.recordException()
        }

    }

}