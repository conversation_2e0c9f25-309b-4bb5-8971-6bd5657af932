package com.bukuwarung.activities.referral.history.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.models.ReferralHistoryItem
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.databinding.LeaderboardHistoryItemBinding

internal class ReferralHistoryAdapter: DefaultRVAdapter<ReferralHistoryItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<ReferralHistoryItem> {
        val binding = LeaderboardHistoryItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ReferralHistoryViewholder(binding)
    }
}