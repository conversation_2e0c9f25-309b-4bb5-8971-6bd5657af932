package com.bukuwarung.activities.referral.leaderboard

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.ReferralHistoryActivity
import com.bukuwarung.activities.referral.leaderboard.adapters.LeaderboardItemAdapter
import com.bukuwarung.activities.referral.share.UploadReferralImageActivity
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.DefaultAnim
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.databinding.ActivityLeaderboardBinding
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.setTextOrDefault
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers

class LeaderboardActivity : AppActivity(DefaultAnim()), LeaderboardContract {

    private lateinit var binding: ActivityLeaderboardBinding
    private lateinit var leaderboardAdapter: LeaderboardItemAdapter

    private lateinit var referralRepository: ReferralRepository
    private lateinit var presenter: LeaderboardPresenter

    private val compositeDisposable = CompositeDisposable()

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        setupView()
        setupRepository()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.dispose()
    }

    private fun setupRepository() {
        referralRepository = ReferralRepository.getInstance()
        presenter = LeaderboardPresenter(
                referralRepository = referralRepository,
                contract = this
        )

        presenter.initData()
        compositeDisposable.add(
                presenter
                        .data
                        .subscribeOn(Schedulers.io())
                        .subscribe(
                                {
                                    setupViewBasedOnViewModel(it)
                                },
                                {
                                    Log.e("LeaderboardActivity", "Error", it)
                                }
                        )
        )
    }

    private fun setupView() {
        binding = ActivityLeaderboardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.backBtn.setOnClickListener { onBackPressed() }
        binding.shareContainer.setOnClickListener {
            val currentPoint = presenter.currentData?.point
            val currentBook = currentBook

            if (currentBook != null && currentPoint != null) {
                AppAnalytics.trackEvent("referral_share_to_socmed_clicked")
                val intent = UploadReferralImageActivity.getNewIntent(
                        origin = this,
                        businessName = currentBook.businessName,
                        ownerName = currentBook.businessOwnerName,
                        point = currentPoint)
                startActivity(intent)
            }
        }
    }

    private fun setupViewBasedOnViewModel(viewModel: LeaderboardViewModel) {
        setupRankAndPoints(viewModel)
        setupRV(viewModel)
    }

    private fun setupRankAndPoints(viewModel: LeaderboardViewModel) {
        if (viewModel.state != LeaderboardViewState.LOADING) {
            binding.mainDataLoading.visibility = View.GONE
            if (viewModel.rank != null && viewModel.point != null) {
                binding.mainDataContainer.visibility = View.VISIBLE
                setupRank(viewModel.rank.toString())
                setupPoint(viewModel.point.toString())
            } else {
                binding.emptyReferralText.visibility = View.VISIBLE
            }
        }
    }

    private fun setupRank(rank: String?) {
        binding.rankItemTV.setTextOrDefault(rank)
    }

    private fun setupPoint(point: String?) {
        binding.pointItemTV.setTextOrDefault(point)
        //rohendy requested to disable it for phase 1 release
//        pointContainer.setOnClickListener { presenter.goToHistoryPage() }
    }

    override fun goToHistory() {
        val intent = ReferralHistoryActivity.getIntent(this)
        startActivity(intent)
    }

    private fun setupRV(viewModel: LeaderboardViewModel) {
        val items = viewModel.items ?: return
        leaderboardAdapter = LeaderboardItemAdapter()
        leaderboardAdapter.results = items
        binding.leaderboardRV.adapter = leaderboardAdapter
        binding.leaderboardRV.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)

        binding.rvLoading.visibility = View.GONE
        binding.leaderboardRV.visibility = items.isNotEmpty().asVisibility()
        binding.emptyView.root.visibility = items.isEmpty().asVisibility()
    }

    companion object {

        fun getIntent(origin: Activity): Intent = Intent(origin, LeaderboardActivity::class.java)

    }

}