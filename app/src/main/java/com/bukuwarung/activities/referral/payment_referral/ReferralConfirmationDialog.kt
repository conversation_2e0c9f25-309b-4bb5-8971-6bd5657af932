package com.bukuwarung.activities.referral.payment_referral

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.databinding.ReferralConfirmationDialogBinding

class ReferralConfirmationDialog(context: Context,private val action:() -> Unit,private val referralCode: String) : BaseDialog(context, BaseDialogType.POPUP) {
    private lateinit var binding: ReferralConfirmationDialogBinding
    init {
        setCancellable(false)
        setUseFullWidth(false)
    }

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ReferralConfirmationDialogBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        binding.referralConfirmationMessage.text =
            context.getString(R.string.referral_confirmation_message, referralCode)
        binding.buttonSkip.setOnClickListener {
            dismiss()
        }
        binding.buttonSubmit.setOnClickListener {
            action()
            dismiss()
        }
    }


}