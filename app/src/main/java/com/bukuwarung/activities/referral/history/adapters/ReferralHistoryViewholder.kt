package com.bukuwarung.activities.referral.history.adapters

import android.view.View
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.models.ReferralHistoryItem
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.databinding.LeaderboardHistoryItemBinding
import com.bukuwarung.utils.getFirstLetter
import com.bukuwarung.utils.setTextOrDefault

internal class ReferralHistoryViewholder(private val binding: LeaderboardHistoryItemBinding) :
    DefaultRVViewHolder<ReferralHistoryItem>(binding.root) {
    private val txtFirstLetter = binding.firstLetter
    private val txtName = binding.textName
    private val txtPoint = binding.textPoint

    override fun bind(item: ReferralHistoryItem) {
        setUpRankLayout(item.name, item.point)
    }

    private fun setUpRankLayout(name: String?, point: Int?) {
        txtFirstLetter.setTextOrDefault(name.getFirstLetter())
        txtName.setTextOrDefault(name)
        txtPoint.text = binding.root.resources.getString(
            R.string.points_history_placeholder,
            point?.toString() ?: "-"
        )
    }

    override fun free() {
        // DO NOTHING
    }


}