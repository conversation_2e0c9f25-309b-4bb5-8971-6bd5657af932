package com.bukuwarung.activities.referral.payment_referral

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomsheetReferralInfoBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.bukuwarung.activities.experiments.CustomWebviewActivity

class TnCBottomSheet: BottomSheetDialogFragment() {

    companion object {
        const val TAG = "TnCBottomSheet"
    }

    private var _binding: BottomsheetReferralInfoBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = BottomsheetReferralInfoBinding.inflate(inflater, container, false)
        val tncContent: TextView = binding.tvBulletSmartphone
        val tncHeader: TextView = binding.tvHeader

        val referralTncBullet: String = RemoteConfigUtils.getReferralTnCBulletPoint()
        val referralTncHeader: String = RemoteConfigUtils.getReferralTnCHeader()
        if(!Utility.isBlank(referralTncBullet)){
            tncContent.text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Html.fromHtml(referralTncBullet, Html.FROM_HTML_MODE_COMPACT)
            } else {
                Html.fromHtml(referralTncBullet)
            }
        }
        tncContent.movementMethod = LinkMovementMethod.getInstance()
        if(!Utility.isBlank(referralTncHeader)){
            tncHeader.text = referralTncHeader
        }
        binding.btnTncReferral.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_LEARN_MORE_PAGE)

            val intent = CustomWebviewActivity.createIntent(context, RemoteConfigUtils.getTncWebviewUrl.getUrl(), context?.getString(R.string.referral_tnc_title), false, "tnc", "referral")
            context?.startActivity(intent);
        }

        return binding.root
    }

    override fun getTheme(): Int {
        return R.style.BottomSheetDialogTheme_RoundCorner
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}