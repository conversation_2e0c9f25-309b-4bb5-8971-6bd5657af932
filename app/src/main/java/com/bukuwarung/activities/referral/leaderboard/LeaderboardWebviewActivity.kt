package com.bukuwarung.activities.referral.leaderboard

import android.app.Activity
import android.app.PendingIntent
import android.app.ProgressDialog
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.Window
import android.webkit.*
import androidx.activity.viewModels
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardWebViewModel
import com.bukuwarung.activities.referral.share.ReferralUploadReceiver
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.controllers.firebase.FirebaseDynamicLinksController
import com.bukuwarung.database.entity.referral.PaymentUserReferral
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.databinding.ActivityWebviewBinding
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.referral.usecase.ReferralUseCase
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject


@AndroidEntryPoint
class LeaderboardWebviewActivity : BaseWebviewActivity(), ReferralRepository.OnPaymentReferralDataCallback {

    lateinit var bookId: String
    var interfaceName: String = "BukuWarungLeaderboard"

    private lateinit var dynamicLinksController: FirebaseDynamicLinksController
    private lateinit var paymentUserReferralData: PaymentUserReferral
    private lateinit var progressDialog: ProgressDialog
    private lateinit var binding: ActivityWebviewBinding

    @Inject
    lateinit var referralUseCase: ReferralUseCase

    private val viewModel: LeaderboardWebViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        this.requestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        val propBuilder = PropBuilder()
        AuthHelper.newSession()
        if (intent.hasExtra("entry_point")) {
            propBuilder.put(AnalyticsConst.ENTRY_POINT, intent.getStringExtra("entry_point"))
        }
        AppAnalytics.trackEvent("open_campaign_microsite", propBuilder)

        viewModel.event.observe(this) {
            when (it) {
                is LeaderboardWebViewModel.Event.ReferralData -> {
                    ReferralPrefManager.getInstance().myReferalCode = it.data?.userReferralCode
                    ReferralPrefManager.getInstance().paymentReferralInUse = it.data?.referredByCode
                    ReferralPrefManager.getInstance().referralDeeplink = it.data?.userReferralDeeplink
                    progressDialog.dismiss()
                }
            }
        }

        bookId = User.getBusinessId()
        if (bookId.isNotNullOrEmpty()) {
            setupWebViewClient()
            initRefCode()
            webView?.addJavascriptInterface(JsObject(this), interfaceName)
        }

        binding = ActivityWebviewBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    fun initRefCode() {
        progressDialog = ComponentUtil.getProgressDialog(this, getString(R.string.please_wait), false)
        if (ReferralPrefManager.getInstance().myReferalCode.isNullOrEmpty() || ReferralPrefManager.getInstance().paymentReferralInUse.isNullOrEmpty()) {
            progressDialog.show()
            viewModel.getReferralData()
        }
        dynamicLinksController = FirebaseDynamicLinksController()
    }

    override fun onUserReferralDataLoaded(paymentUserReferral: PaymentUserReferral?) {
        progressDialog.dismiss()
        paymentUserReferral?.let {
            paymentUserReferralData = it
            ReferralPrefManager.getInstance().myReferalCode = paymentUserReferralData.myReferralCode
            ReferralPrefManager.getInstance().paymentReferralInUse = it.referralCodeInUse
            if (Utility.isBlank(ReferralPrefManager.getInstance().referralDeeplink)) {
                dynamicLinksController
                    .generateReferralDynamicLink(null, paymentUserReferralData.myReferralCode)
                    .map {
                        val deeplink = it.toString()
                        val referralLink = ReferralRepository.getInstance().getShareableLink(deeplink, paymentUserReferralData.myReferralCode)
                        referralLink
                    }.doAfterSuccess {
                        ReferralPrefManager.getInstance().referralDeeplink = it.deeplink
                    }
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe({
                        ReferralPrefManager.getInstance().referralDeeplink = it.deeplink
                    }, {
                        if (it is FirebaseDynamicLinksController.BusinessOwnerNameEmptyException) {
                            AppAnalytics.trackEvent("referral_share_link_error_empty_data")
                        } else {
                            AppAnalytics.trackEvent("referral_share_link_error_unknown")
                        }
                    });

            }
            return
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }

    private fun setupWebViewClient() {
        val webViewClient = DailyUpdateClient(bookId, this)
        webView.let {
            it?.webViewClient = webViewClient
        }
    }

    override fun hideToolBar(): Boolean {
        return false;
    }

    companion object {

        private val ENTRY_POINT: String = "entry_point"

        @kotlin.jvm.JvmField
        var WEBVIEW_PARAM_IS_LEADERBOARD = "webview_param_is_leaderboard"

        fun createIntent(origin: Context?, link: String?, title: String?, entryPoint: String): Intent {
            val intent = Intent(origin, LeaderboardWebviewActivity::class.java)
            intent.putExtra(LINK, link)
            intent.putExtra(TITLE, title)
            intent.putExtra(ENTRY_POINT, entryPoint)
            return intent
        }
    }

    override fun getLink(): String? {
        return intent.getStringExtra(LINK)
    }

    override fun getTitleText(): String? {
        return intent.getStringExtra(TITLE)
    }

    override fun getToolbarColor(): Int {
        return R.color.colorPrimary
    }

    override fun getUserAgent(): String? {
        return null
    }

    override fun getDeeplinkScheme(): String {
        return BuildConfig.DEEPLINK_SCHEME
    }

    override fun allowDebug(): Boolean {
        return false
    }

    override fun getAppToken(): String? {
        return SessionManager.getInstance().bukuwarungToken
    }

    override fun getUserId(): String? {
        return SessionManager.getInstance().userId
    }

    class DailyUpdateClient(val bookId: String, val context: Context) : WebViewClient() {

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError) {
            val activity = context as LeaderboardWebviewActivity
            activity.binding.errorStateImg.setImageResource(R.drawable.ic_no_inet)
            activity.binding.errorState.visibility = View.VISIBLE
            activity.webView?.visibility = View.GONE
        }

        override fun onPageFinished(view: WebView, url: String) {
            super.onPageFinished(view, url)
            val sessionManager = SessionManager.getInstance()
            val token = sessionManager.bukuwarungToken
            val userId = sessionManager.userId


            // script to run "supplyToken(ACCESS_TOKEN, USER_ID, REFERRAL_ID, TAB_INDEX)"
            val script = StringBuilder("javascript:supplyToken(")
            script.append("'").append(token).append("'").append(",")
            script.append("'").append(userId).append("'").append(",")
            script.append("'").append("code").append("'").append(",")
            script.append("0").append(")")
            view.evaluateJavascript(script.toString(), null)

        }
    }

    internal class JsObject(var leaderboardWebviewActivity: Activity) {
        @JavascriptInterface
        open fun shareReferralCode(entryPoint: String) {
            try {
                var imageUri: Uri? = null
                try {
                    val referralFloatingButtonRedirectionType = RemoteConfigUtils.ReferralFloatingFeature.floatingButtonRedirectionType()
                    if (referralFloatingButtonRedirectionType.equals(AppConst.LEADERBOARD_STR)) {
                        imageUri = Utility.getImageUri(leaderboardWebviewActivity, BitmapFactory.decodeResource(leaderboardWebviewActivity.getResources(), R.drawable.shareable_banner_referral))
                    } else {
                        imageUri = Utility.getImageUri(leaderboardWebviewActivity, BitmapFactory.decodeResource(leaderboardWebviewActivity.getResources(), R.drawable.referral_share_payment))
                    }
                } catch (e: NullPointerException) {
                }
                val referralMessage = AppConfigManager.getInstance()
                    ?.paymentReferralMessage
                    ?.replace("[]", ReferralPrefManager.getInstance().referralDeeplink) ?: "-"

                val receiverIntent = Intent(leaderboardWebviewActivity, ReferralUploadReceiver::class.java)
                receiverIntent.putExtra(AnalyticsConst.ENTRY_POINT, entryPoint)
                val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    PendingIntent.getBroadcast(leaderboardWebviewActivity, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
                } else {
                    PendingIntent.getBroadcast(leaderboardWebviewActivity, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    val intent = ShareUtils.getShareUriOnAllAppsWithPendingIntent(imageUri, referralMessage.replace("$$", ReferralPrefManager.getInstance().myReferalCode) ?: "-", "Bagikan Dengan", pendingIntent)
                    leaderboardWebviewActivity.startActivity(Intent.createChooser(intent, "Bagikan Dengan"))
                } else {
                    val shareIntent = Intent()
                    shareIntent.action = Intent.ACTION_SEND
                    shareIntent.type = "image/*"
                    shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri)
                    shareIntent.putExtra(Intent.EXTRA_TEXT, referralMessage)
                    leaderboardWebviewActivity.startActivity(shareIntent)
                }
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.ENTRY_POINT, entryPoint)
                AppAnalytics.trackEvent("share_referral_link_with_friend", propBuilder)
            } catch (ex: ActivityNotFoundException) {
                NotificationUtils.alertToast("No app found")
            }
        }
    }
}