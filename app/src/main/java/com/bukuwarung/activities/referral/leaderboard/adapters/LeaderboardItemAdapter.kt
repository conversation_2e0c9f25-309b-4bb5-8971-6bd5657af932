package com.bukuwarung.activities.referral.leaderboard.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItem
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.databinding.LeaderboardItemBinding

internal class LeaderboardItemAdapter: DefaultRVAdapter<LeaderboardItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<LeaderboardItem> {
        val binding = LeaderboardItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return LeaderboardItemViewholder(binding)
    }
}