package com.bukuwarung.activities.referral.share

import android.app.Activity
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.main_referral.MainReferralActivity
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.controllers.firebase.FirebaseDynamicLinksController
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.databinding.LayoutShareReferralBinding
import com.bukuwarung.dialogs.loading.LoadingDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.ImageUtils
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.button.MaterialButton
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

class UploadReferralImageActivity : AppActivity() {

    private val dynamicLinksController = FirebaseDynamicLinksController()
    private lateinit var binding: LayoutShareReferralBinding

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        setupView()
    }

    private fun setupView() {
        binding = LayoutShareReferralBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val businessNameString = intent.getStringExtra(BUSINESS_NAME_PARAM)
        val ownerNameString = intent.getStringExtra(OWNER_NAME_PARAM)
        val point = intent.getIntExtra(POINT_PARAM, 0)

        binding.businessName.text = businessNameString
        binding.businessOwnerName.text = ownerNameString
        binding.userPoint.text = point.toString()

        binding.uploadBtn.setOnClickListener { uploadReferralImage() }
    }

    private fun uploadReferralImage() {
        val loadingDialog = LoadingDialog(this)
        loadingDialog.show()

        val referralSharingActive = AppConfigManager.getInstance().useReferralSharing()

        if (referralSharingActive) {
            try {
                val referralRepository = ReferralRepository.getInstance()
                referralRepository.getUserReferralCode { referralCode ->
                    val disposable = dynamicLinksController
                        .generateReferralDynamicLink(currentBook, referralCode)
                        .map {
                            val deeplink = it.toString()
                            val referralLink = referralRepository.getShareableLink(deeplink, referralCode)
                            referralRepository.enableSharedReferralLink(
                                referralLink,
                                currentBook, referralCode
                            )
                            referralLink
                        }
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe({
                            val referralLink = it.sharedlink
                            Log.d("UploadReferral", "Got Referral Link $referralLink")

                            val referralMessage = AppConfigManager.getInstance()
                                ?.referralLeaderboardMessage
                                ?.replace("[]", referralLink) ?: "-"

                            uploadImage(loadingDialog, referralMessage)
                        }, {
                            if (it is FirebaseDynamicLinksController.BusinessOwnerNameEmptyException) {
                                AppAnalytics.trackEvent("referral_share_upload_error_empty_data")
                                loadingDialog.dismiss()

                                val promptDialog = NullProfileReferralDialog(
                                    this
                                ) { promptResult ->
                                    run {
                                        if (promptResult) {
                                            val intent = BusinessProfileFormActivity.getIntent(this)
                                            startActivityForResult(intent, MainReferralActivity.ACTIVITY_FILL_PROFILE_ID)
                                        }
                                    }
                                }

                                promptDialog.show()
                            } else {
                                AppAnalytics.trackEvent("referral_share_upload_error_unknown")
                                uploadImage(loadingDialog, null) // if referral code fetching unsuccessful, upload anyway.
                            }
                        })
                }
            } catch (exception: Exception) {
                Log.e("UploadReferral", "Exception", exception)
                uploadImage(loadingDialog, null) // if referral code fetching unsuccessful, upload anyway.
            }
        } else {
            // only share image, not referral link/code
            uploadImage(loadingDialog, null)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            MainReferralActivity.ACTIVITY_FILL_PROFILE_ID -> {
                if (resultCode == Activity.RESULT_OK) {
                    val businessNameString = data?.getStringExtra(BUSINESS_NAME_PARAM)
                    val ownerNameString = data?.getStringExtra(OWNER_NAME_PARAM)

                    binding.businessName.text = businessNameString
                    binding.businessOwnerName.text = ownerNameString

                    uploadReferralImage()
                }
            }
        }
    }

    private fun uploadImage(dialog: LoadingDialog, text: String?) {
        val layout = binding.mainContainer
        val uploadBtnToBeHidden = layout.findViewById<MaterialButton>(R.id.uploadBtn)
        uploadBtnToBeHidden.visibility = View.GONE

        generateAndShareViewImage(
            this,
            binding.mainContainer,
            text
        )

        Handler().postDelayed({
            dialog.dismiss()
            binding.uploadBtn.visibility = View.VISIBLE
        }, 500) // needed to clear cache of previous layout (with button not hidden)
    }

    private fun generateAndShareViewImage(
        context: Context?,
        layout: View?,
        text: String?
    ) {
        try {
            AppAnalytics.trackEvent("share_referral_upload")
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(layout, false)
            val receiverIntent = Intent(this, ReferralUploadReceiver::class.java)
            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
            } else {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
            }
            val shareLayoutImage = ShareLayoutImage(
                text,
                context,
                "",
                "",
                false,
                "Bagikan Dengan",
                pendingIntent
            )
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {

        const val BUSINESS_NAME_PARAM = "businessName"
        const val OWNER_NAME_PARAM = "ownerName"
        const val POINT_PARAM = "point"

        fun getNewIntent(origin: Activity, businessName: String, ownerName: String, point: Int): Intent {
            val intent = Intent(origin, UploadReferralImageActivity::class.java)
            intent.putExtra(BUSINESS_NAME_PARAM, businessName)
            intent.putExtra(OWNER_NAME_PARAM, ownerName)
            intent.putExtra(POINT_PARAM, point)
            return intent
        }

    }

}