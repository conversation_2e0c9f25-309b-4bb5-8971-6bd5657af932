package com.bukuwarung.activities.referral.main_referral.components

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.bukuwarung.R
import com.bukuwarung.databinding.ReferralStepItemBinding

class ReferralStep: LinearLayout {

    var index: Int = 0
        set(value) {
            field = value
            binding.indexText.text = value.toString()
        }

    var text: String? = resources.getString(R.string.default_placeholder)
        set(value) {
            field = value
            binding.contentText.text = value.toString()
        }

    private val binding: ReferralStepItemBinding by lazy {
        ReferralStepItemBinding.inflate(LayoutInflater.from(context), this, true)
    }

    constructor(context: Context) : this(context, null) {
        binding.root
    }

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

}