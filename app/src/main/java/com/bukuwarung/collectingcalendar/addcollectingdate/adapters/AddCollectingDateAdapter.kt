package com.bukuwarung.collectingcalendar.addcollectingdate.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.addcollectingdate.models.NewCollectingDate
import com.bukuwarung.databinding.CollectingDatePickerItemBinding

internal class AddCollectingDateAdapter(
        private val onSaveCallback: ((customerId: String,
                                      storableDateString: String) -> Unit)
): DefaultRVAdapter<NewCollectingDate>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<NewCollectingDate> {
        val binding = CollectingDatePickerItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AddCollectingDateViewHolder(
            binding,
            results?.size ?: 0,
            onSaveCallback
        )
    }

}