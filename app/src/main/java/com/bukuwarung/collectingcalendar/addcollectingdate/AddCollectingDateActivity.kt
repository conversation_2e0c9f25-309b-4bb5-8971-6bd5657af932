package com.bukuwarung.collectingcalendar.addcollectingdate

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.DefaultAnim
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.collectingcalendar.addcollectingdate.adapters.AddCollectingDateAdapter
import com.bukuwarung.collectingcalendar.addcollectingdate.models.NewCollectingDate
import com.bukuwarung.collectingcalendar.main.CollectingCalendarActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.databinding.ActivityAddCollectingDateBinding
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.smoothScrollToPosition

class AddCollectingDateActivity : AppActivity(DefaultAnim()), OnboardingWidget.OnboardingWidgetListener {

    private lateinit var binding: ActivityAddCollectingDateBinding
    private var justCompletedAllDuedate = false
    private var adapter: AddCollectingDateAdapter? = null
    private var onboardingWidget: OnboardingWidget? = null

    private val viewModel: AddCollectingDateViewModel by viewModels {
        val predefinedUserId = intent.getStringExtra(PARAM_USER_ID)

        AddCollectingDateViewModelFactory(
                this,
                CustomerRepository.getInstance(this),
                predefinedUserId
        )
    }

    override fun onResume() {
        super.onResume()
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_COLLECTING_CALENDAR_CREATE_NEW_COLLECTING_DATE, this)
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        binding = ActivityAddCollectingDateBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()
        observeData()

        if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_DUE_DATE)) {
            onboardingWidget = OnboardingWidget.createInstance(
                    this,
                    this,
                    OnboardingPrefManager.TUTORIAL_DUE_DATE,
                binding.bannerContainer,
                    R.drawable.onboarding_announce,
                    getString(R.string.new_feature),
                    getString(R.string.onboarding_due_date_body),
                    "",
                    FocusGravity.CENTER,
                    ShapeType.RECTANGLE_FULL
            )
        }

    }

    private fun setupView() {
        binding.backBtn.setOnClickListener { onBackPressed() }
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    override fun onBackPressed() {
        val intent = Intent(this, CollectingCalendarActivity::class.java)

        // defaults to future tab
        if (justCompletedAllDuedate) intent.putExtra(CollectingCalendarActivity.DEFAULT_TAB_ARG, 2)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivity(intent)
        finish()
    }

    // logic region

    private fun showBasedOnState(state: AddCollectingDateState) {
        when (state.type) {
            AddCollectingDateStateType.Loading -> {
                binding.loadingBar.visibility = View.VISIBLE
                binding.dateRV.visibility = View.GONE
            }
            AddCollectingDateStateType.Loaded -> {
                binding.loadingBar.visibility = View.GONE
                setupRV(state.items)
                binding.dateRV.visibility = View.VISIBLE
            }
        }
    }

    // end of logic region

    // views region

    private fun setupRV(items: List<NewCollectingDate>) {
        val totalItems = items.size
        val totalAmt = items.fold(0L, operation = { amt, data ->
            amt + data.amount
        })

        binding.tempoTitle.text = getString(R.string.collection_banner_title, totalItems)
        binding.tempoSubtitle.text = getString(
            R.string.collection_banner_subtitle,
            Utility.getCurrency(),
            Utility.formatCurrency(totalAmt.toDouble())
        )

        val adapter = AddCollectingDateAdapter(
                onSaveCallback = { customerId, storableDateString ->
                    onDataSave(customerId, storableDateString)
                }
        )
        adapter.results = items

        this.adapter = adapter
        binding.dateRV.adapter = adapter
        binding.dateRV.layoutManager = LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
        binding.dateRV.isNestedScrollingEnabled = true
    }

    private fun onDataSave(customerId: String, storableDateString: String) {
        val hasUnSelectedItemLeft = adapter?.results?.fold(false,
                { acc, newCollectingDate ->
                    acc || (newCollectingDate?.oldCollectingDate?.isBlank() ?: true)
                }) ?: false

        NotificationUtils.alertToastWithColor(resources.getString(R.string.collecting_calendar_saved), this,
                R.color.in_green, R.color.white)

        Handler().postDelayed({
            CustomerRepository.getInstance(this).setCustomerReminderDate(
                    User.getUserId(), User.getDeviceId(), customerId, storableDateString
            )

            if (!hasUnSelectedItemLeft) {
                this.justCompletedAllDuedate = true
                SessionManager.getInstance().setHasClosedCalendarConfetti(false)
                onBackPressed()
            } else {
                adapter?.results?.indexOfFirst {
                    it?.oldCollectingDate?.isBlank() ?: true
                }?.let {
                    binding.dateRV.smoothScrollToPosition(this, it)
                }
            }
        }, 1000)
    }

    override fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean, isFromCloseButton: Boolean, isFromOutside: Boolean) {
        if (id == null) return
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {}

    // end of views region

    companion object {

        private const val PARAM_USER_ID = "oldReminderId"

        fun getNewIntent(origin: Context): Intent {
            return Intent(origin, AddCollectingDateActivity::class.java)
        }

        fun getNewIntentForUserId(origin: Context, userId: String): Intent {
            val intent = Intent(origin, AddCollectingDateActivity::class.java)
            intent.putExtra(PARAM_USER_ID, userId)
            return intent
        }

    }

}