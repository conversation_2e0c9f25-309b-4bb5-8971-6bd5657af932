package com.bukuwarung.collectingcalendar.main

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.VerticalFadeSwipeAnim
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.collectingcalendar.addcollectingdate.AddCollectingDateActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityCollectingCalendarBinding
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.google.android.material.tabs.TabLayout
import java.lang.Exception
import kotlin.math.absoluteValue

internal class CollectingCalendarActivity: AppActivity(VerticalFadeSwipeAnim()), CollectingCalendarFragment.CollectingSumReceivedCallback {

    private lateinit var binding: ActivityCollectingCalendarBinding
    private lateinit var tabAdapter: CollectingCalendarTabAdapter

    private var amountPast = 0L
    private var amountPresent = 0L
    private var amountFuture = 0L

    private var currentTabPosition = 1

    // hack for callback for showing completed widget always shown
    private var closedConfetti = false
    private val isFromNotification by lazy {
        intent?.getBooleanExtra(IS_FROM_NOTIFICATION, false) ?: false
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        binding = ActivityCollectingCalendarBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        binding.backBtn.setOnClickListener { onBackPressed() }
        if (isFromNotification) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CALENDAR_NOTIF_OPEN)
        }
        binding.confettiClose.setOnClickListener {
            binding.completedContainer.visibility = View.GONE
            closedConfetti = true
            SessionManager.getInstance().setHasClosedCalendarConfetti(true)
        }
        binding.btnTempo.setOnClickListener {
            goToAddCollectingDate()
        }
        setupTab()
    }

    private fun setupTab() {
        tabAdapter = CollectingCalendarTabAdapter(applicationContext, supportFragmentManager)
        binding.viewPager.adapter = tabAdapter

        val defaultTab = intent.getIntExtra("COLLECTING_CALENDAR_TAB", 1)
        binding.viewPager.currentItem = defaultTab
        binding.tabLayout.setupWithViewPager(binding.viewPager)

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabReselected(tab: TabLayout.Tab?) {}

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView = null
                tab?.setCustomView(R.layout.custom_tab_collection_unselected)
                tab?.customView?.findViewById<TextView>(R.id.txtTitle)?.let {
                    it.text = tab.text
                }
            }

            override fun onTabSelected(tab: TabLayout.Tab?) {
                onSelected(tab)
            }

        })

        // Selects initial tab
        onSelected(binding.tabLayout.getTabAt(defaultTab))
    }

    private fun onSelected(tab: TabLayout.Tab?) {
        tab?.position?.let { this.currentTabPosition = it }
        tab?.customView = null

        val amt = when (tab?.position) {
            CollectingCalendarTabAdapter.POSITION_FIRST -> {
                // PAST DUE DATE CUSTOMERS
                amountPast
            }
            CollectingCalendarTabAdapter.POSITION_SECOND -> {
                // PRESENT DUE DATE CUSTOMERS
                amountPresent
            }
            CollectingCalendarTabAdapter.POSITION_THIRD -> {
                // FUTURE DUE DATE CUSTOMERS
                amountFuture
            }
            else -> {
                0L
            }
        }

        tab?.setCustomView(R.layout.custom_tab_collection_selected)
        tab?.customView?.findViewById<TextView>(R.id.txtTitle)?.let {
            it.text = tab.text
        }

        tab?.customView?.findViewById<TextView>(R.id.txtAmt)?.let {
            it.text = "${Utility.getCurrency()} ${Utility.formatCurrency(amt.toDouble())}"
        }
    }

    override fun onAttachFragment(fragment: Fragment) {
        super.onAttachFragment(fragment)

        if (fragment is CollectingCalendarFragment)
            fragment.setOnSumReceivedCallback(this)
    }

    override fun onCompletedAllDueDate() {
        if (closedConfetti.not()) {
            binding.completedContainer.visibility = View.VISIBLE
            binding.notCompletedContainer.visibility = View.GONE
        }

        if (SessionManager.getInstance().hasClosedCalendarConfetti())
            binding.completedContainer.visibility = View.GONE
    }

    private fun goToAddCollectingDate() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put("entryPoint", "calendar")
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_COLLECTING_CALENDAR_CREATE_NEW_COLLECTING_DATE, propBuilder)
        val intent = AddCollectingDateActivity.getNewIntent(this)
        startActivity(intent)
    }

    override fun onIncompletedSumReceived(incompleteSum: Long, customerCount: Int) {
        binding.completedContainer.visibility = View.GONE
        binding.notCompletedContainer.visibility = View.VISIBLE

        binding.tempoTitle.text = getString(R.string.collection_incomplete_title, customerCount)
        binding.tempoSubtitle.text = getString(
            R.string.collection_incomplete_subtitle,
            Utility.getCurrency(),
            Utility.formatCurrency(incompleteSum.absoluteValue.toDouble())
        )
    }

    override fun onCollectingSumReceived(collectingSum: Long, fragmentType: CollectingCalendarType?) {
        try {
            when (fragmentType) {
                CollectingCalendarType.PastCalendar -> {
                    this.amountPast = collectingSum.absoluteValue

                    if (currentTabPosition == CollectingCalendarTabAdapter.POSITION_FIRST)
                        onSelected(binding.tabLayout.getTabAt(currentTabPosition))
                }
                CollectingCalendarType.PresentCalendar -> {
                    this.amountPresent = collectingSum.absoluteValue

                    if (currentTabPosition == CollectingCalendarTabAdapter.POSITION_SECOND)
                        onSelected(binding.tabLayout.getTabAt(currentTabPosition))
                }
                CollectingCalendarType.FutureCalendar -> {
                    this.amountFuture = collectingSum.absoluteValue

                    if (currentTabPosition == CollectingCalendarTabAdapter.POSITION_THIRD)
                        onSelected(binding.tabLayout.getTabAt(currentTabPosition))
                }
                else -> {}
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    companion object {

        const val DEFAULT_TAB_ARG = "COLLECTING_CALENDAR_TAB"

        private const val IS_FROM_NOTIFICATION = "IS_FROM_NOTIFICATION"
        fun createIntent(origin: Context, isFromNotification: Boolean = false): Intent {
            val intent = Intent(origin, CollectingCalendarActivity::class.java)
            intent.putExtra(IS_FROM_NOTIFICATION, isFromNotification)
            return intent
        }

        fun getNewIntent(origin: Activity): Intent {
            return Intent(origin, CollectingCalendarActivity::class.java)
        }

    }

}
