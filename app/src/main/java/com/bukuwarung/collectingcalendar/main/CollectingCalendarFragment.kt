package com.bukuwarung.collectingcalendar.main

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.viewModels
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity
import com.bukuwarung.activities.superclasses.AppFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.collectingcalendar.main.adapters.CollectingCalendarAdapter
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.databinding.FragmentCollectingCalendarBinding

class CollectingCalendarFragment() : AppFragment() {

    private lateinit var onSumReceivedCallback: CollectingSumReceivedCallback

    private var adapter: CollectingCalendarAdapter? = null

    private val viewModel: CollectingCalendarViewModel by viewModels {
        // corner cases that shouldn't ever happened. But we can't pass null, so we pass default.
        val fragmentType = type ?: CollectingCalendarType.PresentCalendar
        CollectingCalendarViewModelFactory(this, CustomerRepository.getInstance(context), fragmentType)
    }

    private var type: CollectingCalendarType? = null

    private constructor(type: CollectingCalendarType) : this() {
        this.type = type
    }

    fun setOnSumReceivedCallback(callback: CollectingSumReceivedCallback) {
        this.onSumReceivedCallback = callback
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            type = type ?: savedInstanceState?.getSerializable(FRAGMENT_TYPE) as CollectingCalendarType
        } catch (ex: Exception) {
            Log.e("CollectingFragment", "Exception", ex)
        }
    }

    private var _binding: FragmentCollectingCalendarBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentCollectingCalendarBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        observeData()
    }

    private fun observeData() {
        viewModel.stateData.observe(viewLifecycleOwner, Observer { state ->
            showBasedOnState(state)
        })
    }

    // logic region

    private fun showBasedOnState(state: CollectingCalendarState) {

        when (state.currentState) {
            CollectingCalendarStateType.Loading -> {
                binding.loadingBar.visibility = View.VISIBLE
                binding.noCustomer.root.visibility = View.GONE
                binding.rvContainer.visibility = View.GONE
            }
            CollectingCalendarStateType.Loaded -> {
                binding.loadingBar.visibility = View.GONE
                when {
                    state.users.isEmpty() -> {
                        binding.noCustomer.root.visibility = View.VISIBLE
                        binding.noCalendar.root.visibility = View.GONE
                        binding.rvContainer.visibility = View.GONE
                        binding.bottomContainer.visibility = View.GONE
                        binding.noCustomer.createBtn.setOnClickListener { goToNewUtang() }
                        this.onSumReceivedCallback.onCollectingSumReceived(0, type)
                    }
                    state.items.isEmpty() -> {
                        binding.noCustomer.root.visibility = View.GONE
                        binding.noCalendar.root.visibility = View.VISIBLE
                        binding.rvContainer.visibility = View.GONE

                        val noCalTitle = when (type) {
                            CollectingCalendarType.PastCalendar -> {
                                getString(R.string.collection_free_past)
                            }
                            CollectingCalendarType.PresentCalendar -> {
                                getString(R.string.collection_free_present)
                            }
                            CollectingCalendarType.FutureCalendar -> {
                                getString(R.string.collection_free_future)
                            }
                            else -> {
                                getString(R.string.collection_free_default)
                            }
                        }
                        binding.noCalendar.subtitleNoCalendar.text = noCalTitle

                        val unrecordedCustomer = state.users.filter {
                            it.amount < 0 && !it.hasDueDate
                        }

                        if (unrecordedCustomer.isNotEmpty()) {
                            val totalAmt = unrecordedCustomer.fold(0L, operation = { amt, data ->
                                amt + data.amount
                            })

                            onSumReceivedCallback.onIncompletedSumReceived(totalAmt, unrecordedCustomer.size)
                            try {
                                val propBuilder = AppAnalytics.PropBuilder()
                                propBuilder.put("customer_count", unrecordedCustomer.size)
                                propBuilder.put("debt_amount", totalAmt)
                                AppAnalytics.trackEvent("collecting_calendar_cta_shown", propBuilder)
                            } catch (ex: Exception) {
                                ex.printStackTrace()
                            }
                        } else {
                            binding.bottomContainer.visibility = View.GONE
                            onSumReceivedCallback.onCompletedAllDueDate()
                        }

                        this.onSumReceivedCallback.onCollectingSumReceived(0, type)
                    }
                    else -> {
                        binding.noCustomer.root.visibility = View.GONE
                        binding.noCalendar.root.visibility = View.GONE
                        binding.rvContainer.visibility = View.VISIBLE

                        val unrecordedCustomer = state.users.filter {
                            it.amount < 0 && !it.hasDueDate
                        }

                        if (unrecordedCustomer.isNotEmpty()) {
                            val totalAmt = unrecordedCustomer.fold(0L, operation = { amt, data ->
                                amt + data.amount
                            })

                            onSumReceivedCallback.onIncompletedSumReceived(totalAmt, unrecordedCustomer.size)
                            try {
                                val propBuilder = AppAnalytics.PropBuilder()
                                propBuilder.put("customer_count", unrecordedCustomer.size)
                                propBuilder.put("debt_amount", totalAmt)
                                AppAnalytics.trackEvent("collecting_calendar_cta_shown", propBuilder)
                            } catch (ex: Exception) {
                                ex.printStackTrace()
                            }
                        } else {
                            binding.bottomContainer.visibility = View.GONE
                            onSumReceivedCallback.onCompletedAllDueDate()
                        }

                        setupRV(state.items)
                    }
                }
            }
        }
    }

    // end logic region

    // views region

    private fun setupRV(items: List<CollectingDate>) {
        val adapter = CollectingCalendarAdapter()
        adapter.results = items
        this.adapter = adapter

        val totalAmt = items.fold(0L, operation = { amt, data ->
            amt + data.amount
        })
        this.onSumReceivedCallback.onCollectingSumReceived(totalAmt, type)

        binding.fragmentRV.adapter = this.adapter
        binding.fragmentRV.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
    }

    private fun goToNewUtang() {
        AppAnalytics.trackEvent("collecting_calendar_create_new_utang")
        val intent = AddCustomerActivity.getNewIntentFromCollecting(activity)
        startActivity(intent)
    }

    // end of views region

    companion object {

        private const val FRAGMENT_TYPE = "FragmentType"

        fun getFragmentByType(type: CollectingCalendarType): CollectingCalendarFragment {
            val fragment = CollectingCalendarFragment(type)

            val bundle = Bundle()
            bundle.putSerializable(FRAGMENT_TYPE, type)
            fragment.arguments = bundle

            return fragment
        }

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface CollectingSumReceivedCallback {
        fun onCollectingSumReceived(collectingSum: Long, fragmentType: CollectingCalendarType?)
        fun onCompletedAllDueDate()
        fun onIncompletedSumReceived(incompleteSum: Long, customerCount: Int)
    }

}