package com.bukuwarung.collectingcalendar.main.adapters

import android.app.DatePickerDialog
import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.DatePicker
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.addcollectingdate.AddCollectingDateActivity
import com.bukuwarung.collectingcalendar.main.CollectingCalendarType
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.databinding.ItemCollectingBinding
import com.bukuwarung.session.User
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import java.util.*
import kotlin.math.absoluteValue

internal class CollectingCalendarViewHolder(
    private val binding: ItemCollectingBinding
) : DefaultRVViewHolder<CollectingDate>(binding.root) {

    private val mainContainer = binding.mainContainer
    private val firstLetterTV = binding.firstLetter
    private val userImage = binding.photo
    private val nameTV = binding.nameText
    private val collectingTV = binding.collectingText
    private val amountTV = binding.amount
    private val setNewDateTV = binding.setNewDate

    override fun bind(item: CollectingDate) {
        val user = item.user ?: return
        firstLetterTV.text = user.name.first().toString()
        userImage.visibility = View.GONE
        firstLetterTV.visibility = View.VISIBLE
        nameTV.text = user.name
        amountTV.text = binding.root.context.resources.getString(
            R.string.collecting_calendar_amount_template,
                Utility.formatCurrency(item.amount.absoluteValue.toDouble()))

        mainContainer.setOnClickListener {
            if (item.tabType == CollectingCalendarType.PastCalendar) {
                setReminderDateAsText(currentReminderDate = Utility.getStorableDateString(item.date), item = item)

            } else {
                goToCustomerDetail(item.user.id)
            }
        }

        collectingTV.text = item.date?.let { Utility.getRegularDateStringId(it) } ?: "-"
        setNewDateTV.visibility = (item.tabType == CollectingCalendarType.PastCalendar).asVisibility()
    }

    private fun onDateSet(datePicker: DatePicker, year: Int, month: Int, date: Int, item: CollectingDate) {
        val instance = Calendar.getInstance()
        instance.set(year, month, date)

        val storableDateString = Utility.getStorableDateString(instance.time)

        val propbuilder = AppAnalytics.PropBuilder()
        propbuilder.put("date", storableDateString)
        propbuilder.put("entryPoint", "calendar")
        AppAnalytics.trackEvent("collecting_date_set_new_collecting_date", propbuilder)

        val anim = AnimationUtils.loadAnimation(binding.root.context, R.anim.slide_right_fade_out)
        anim.setAnimationListener(object:Animation.AnimationListener {
            override fun onAnimationRepeat(p0: Animation?) {}

            override fun onAnimationEnd(p0: Animation?) {
                item.date = instance.time
                CustomerRepository.getInstance(Application.getAppContext())
                        .setCustomerReminderDate(User.getUserId(), User.getDeviceId(),
                                item.user?.id, storableDateString)
            }

            override fun onAnimationStart(p0: Animation?) {}

        })
        binding.root.startAnimation(anim)

    }

    fun setReminderDateAsText(currentReminderDate: String? = null, item: CollectingDate) {
        AppAnalytics.trackEvent("collecting_past_select_existing")

        val calendar = Calendar.getInstance()
        val prefilledDate = currentReminderDate
        if (prefilledDate != null && !prefilledDate.isEmpty()) {
            try {
                calendar.time = DateTimeUtils.convertToDateYYYYMMDD(prefilledDate)
            } catch (ex: Exception) {
                Log.e("PaymentReminderDate", "Exception", ex)
            }
        }
        val y = calendar[Calendar.YEAR]
        val m = calendar[Calendar.MONTH]
        val d = calendar[Calendar.DATE]
        val context: Context = binding.root.context
        val datePickerDialog = DatePickerDialog(
                context,
                R.style.DatePickerDialogParent,
                DatePickerDialog.OnDateSetListener{ datePicker, year, month, date -> onDateSet(datePicker, year, month, date, item) },
                y,
                m,
                d
        )
//        if (!Utility.isBlank(activity!!.paymentReminderDate.text.toString()) && activity!!.reminderDatePicker != null) {
//            val datePicker = DatePicker(view.context)
//            val year = datePicker.year
//            val month = datePicker.month
//            datePicker.background = view.resources.getDrawable(R.drawable.bg_button)
//            datePicker.setBackgroundColor(Color.WHITE)
//            datePickerDialog.updateDate(year, month, datePicker.dayOfMonth)
//        }

        val calendarNow = Calendar.getInstance()
        val datePicker = datePickerDialog.datePicker
        datePickerDialog.setButton(DatePickerDialog.BUTTON_POSITIVE, context.getText(R.string.save), datePickerDialog)
        //subtracting 10sec to avoid validation error for min data, min data cannot me less than or equal to current timestamp
        //problem in using currentTimestamp -> fromDate: Sun Jun 21 01:53:21 GMT+07:00 2020 does not precede toDate: Sun Jun 21 01:53:21 GMT+07:00 2020
        //TODO: remove hacky solution of subtracting time to avoid validation error
        datePicker.minDate = calendarNow.timeInMillis - 10000
        datePickerDialog.show()
    }

    private fun goToCustomerDetail(customerId: String) {
        AppAnalytics.trackEvent("collecting_calendar_to_customer_detail")
        val intent = Intent(binding.root.context, CustomerTransactionActivity::class.java)
        intent.putExtra("customerId", customerId)
        binding.root.context.startActivity(intent)
    }

    private fun goToAddCollectingCalendar(customerId: String) {
        AppAnalytics.trackEvent("collecting_calendar_edit_collecting_date")
        val intent =
            AddCollectingDateActivity.getNewIntentForUserId(binding.root.context, customerId)
        binding.root.context.startActivity(intent)
    }

    override fun free() {}


}