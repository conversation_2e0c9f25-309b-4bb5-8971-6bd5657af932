package com.bukuwarung.collectingcalendar.main.adapters

import android.view.View
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import com.bukuwarung.databinding.ItemCollectingDividerBinding
import java.text.SimpleDateFormat
import java.util.*

internal class CollectingCalendarSeparatorViewHolder(private val binding: ItemCollectingDividerBinding) :
    DefaultRVViewHolder<CollectingDate>(binding.root) {

    private val calendarText = binding.calendarText

    override fun bind(item: CollectingDate) {
        bindTime(item.date)
    }

    private fun bindTime(date: Date?) {
        try {
            val calendar = Calendar.getInstance()
            calendar.time = date

            val dateString = SimpleDateFormat("dd MMMM yyyy", Locale("id", "ID"))
            calendarText.text = dateString.format(calendar.time)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun free() {}


}