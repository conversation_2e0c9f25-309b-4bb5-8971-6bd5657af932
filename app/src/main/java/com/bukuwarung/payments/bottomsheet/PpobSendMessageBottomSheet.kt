package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.PpobSendMessageBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.viewmodels.PpobBillDetailsBsViewModel
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.setSingleClickListener
import javax.inject.Inject
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PpobSendMessageBottomSheet : BaseBottomSheetDialogFragment() {
    companion object {
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        const val TAG = "PpobSendMessageBottomSheet"

        fun createInstance(phoneNumber: String) =
            PpobSendMessageBottomSheet().apply {
                val bundle = Bundle()
                bundle.putString(PHONE_NUMBER, phoneNumber)
                arguments = bundle
            }
    }

    private var _binding: PpobSendMessageBottomSheetBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PpobBillDetailsBsViewModel by viewModels()

    interface CallBack {
        fun shareOnWA(phoneNumber: String)
        fun shareOnSms(phoneNumber: String)
    }

    private var listener: CallBack? = null

    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER) }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? CallBack }
        if (context is CallBack) listener = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PpobSendMessageBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            tvTitle.text = getString(R.string.send_to, phoneNumber.orEmpty())
            tvSubtitle.visibility = phoneNumber.isNotNullOrBlank().asVisibility()
            tvSms.setSingleClickListener {
                listener?.shareOnSms(phoneNumber.orEmpty())
                dismiss()
            }
            tvWhatsapp.setSingleClickListener {
                listener?.shareOnWA(phoneNumber.orEmpty())
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
        _binding = null
    }
}