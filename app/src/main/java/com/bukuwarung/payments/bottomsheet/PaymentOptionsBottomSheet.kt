package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomSheetPaymentOptionsBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.deeplink.handler.PaymentCheckoutSignalHandler
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


@AndroidEntryPoint
class PaymentOptionsBottomSheet : BaseBottomSheetDialogFragment(), Navigator {

    companion object {
        const val TAG = "PaymentOptionsBottomSheet"
        const val ENTRY_POINT = "entry_point"

        fun createInstance(entryPoint: String) = PaymentOptionsBottomSheet().apply {
            arguments = Bundle().apply {
                putString(ENTRY_POINT, entryPoint)
            }
        }
    }

    private var _binding: BottomSheetPaymentOptionsBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var neuro: Neuro

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetPaymentOptionsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val entryPoint = arguments?.getString(ENTRY_POINT)

        with(binding) {
            // if payment in and out are active than only open openPaymentCheckout page as the handler handles all the kyc tier cases so no need to handle kyb separately
            clPaymentIn.setSingleClickListener {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_TAGIH_INWARDS)
                openPaymentCheckout(PaymentHistory.TYPE_PAYMENT_IN, entryPoint)
                dismiss()
            }

            clPaymentOut.setSingleClickListener {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_BAYAR_OUTWARDS)
                openPaymentCheckout(PaymentHistory.TYPE_PAYMENT_OUT, entryPoint)
                dismiss()
            }
        }
    }

    private fun openPaymentCheckout(paymentType: String, entryPoint: String?) {
        val sourceLink = SourceLink(
            requireContext(),
            PaymentCheckoutSignalHandler.getPaymentLink(
                paymentType = paymentType,
                entryPoint = entryPoint,
                from = entryPoint
            )
        )
        neuro.route(
            sourceLink, this, {},
            { FirebaseCrashlytics.getInstance().recordException(it) }
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}
