package com.bukuwarung.payments.selectbank

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivitySelectBankBinding
import com.bukuwarung.payments.adapters.BankListAdapter
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.asVisibility
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SelectBankActivity : BaseActivity() {

    private val viewModel: SelectPayMethodViewModel by viewModels()

    private lateinit var adapter: BankListAdapter
    private lateinit var binding: ActivitySelectBankBinding

    companion object {
        private const val VA_BANK = "VA_BANK"
        private const val BANK_TYPE = "bank_type"
        private const val AMOUNT = "amount"
        const val BANK_RESULT = "bankResult"

        fun createIntent(
            context: Context, vaBank: Boolean, bankType: String? = null, amount: Double? = null
        ): Intent {
            val i = Intent(context, SelectBankActivity::class.java)
            i.putExtra(VA_BANK, vaBank)
            i.putExtra(BANK_TYPE, bankType)
            i.putExtra(AMOUNT, amount)
            return i
        }
    }

    private val isVaBank by lazy {
        intent?.getBooleanExtra(VA_BANK, false) ?: false
    }
    private val bankType: String? by lazy { intent.getStringExtra(BANK_TYPE) }
    private val amount by lazy { intent.getDoubleExtra(AMOUNT, 0.0) }

    override fun setViewBinding() {
        binding = ActivitySelectBankBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        adapter = BankListAdapter {
            val i = Intent()
            setResult(Activity.RESULT_OK, i.putExtra(BANK_RESULT, it))
            finish()
        }
        binding.rvBanks.layoutManager = LinearLayoutManager(this)
        binding.rvBanks.adapter = adapter
        viewModel.setVaBank(isVaBank, bankType)
        binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_arrow_back)
        binding.toolbar.setNavigationOnClickListener {
            InputUtils.hideKeyBoardWithCheck(this)
            onBackPressed()
        }
        binding.inputSearch.afterTextChanged {
            viewModel.setQuery(it)
        }
        binding.iconCancel.setOnClickListener {
            viewModel.onCancelClicked()
        }
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is SelectPayMethodViewModel.Event.ShowLoading -> binding.progressBar.visibility = it.show.asVisibility()
                is SelectPayMethodViewModel.Event.ShowCloseButton -> binding.iconCancel.visibility = it.show.asVisibility()
                SelectPayMethodViewModel.Event.ClearSearch -> binding.inputSearch.setText("")
                else -> {}
            }
        }
        viewModel.banks.observe(this) {
            if (amount > 0) {
                val filteredBanks = PaymentUtils.getFilteredBanks(it, amount)
                adapter.setData(filteredBanks)
            } else {
                adapter.setData(it)
            }
        }
    }
}
