package com.bukuwarung.payments

import android.app.Activity
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.View
import android.webkit.URLUtil
import android.widget.ScrollView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.observe
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.print.BluetoothPrinter
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.referral.main_referral.ReferralSharingReceiver
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.base_android.extensions.observeEvent
import com.bukuwarung.base_android.extensions.shareText
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.COLLAPSE
import com.bukuwarung.constants.AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED
import com.bukuwarung.constants.AnalyticsConst.EVENT_TAP_PAYMENT_GUIDE
import com.bukuwarung.constants.AnalyticsConst.EXPAND
import com.bukuwarung.constants.AnalyticsConst.PAYMENT
import com.bukuwarung.constants.AnalyticsConst.PAYMENT_DETAILS
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.ZERO_DOUBLE
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.BANK_DOWN_TIME
import com.bukuwarung.constants.PaymentConst.RECORD_IN_CASH
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.contact.ui.ContactSearchResultsFragment
import com.bukuwarung.contact.ui.UserContactFragment
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityPaymentHistoryDetailBinding

import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.dialogs.payment.PaymentFinishedDialog
import com.bukuwarung.dialogs.printer.OpenSetupPrinterDialog
import com.bukuwarung.dialogs.printer.PrintingDialog
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.adapters.PaymentHistoryTimelineAdapter
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.bottomsheet.BankAccountListBottomSheetFragment
import com.bukuwarung.payments.bottomsheet.LoyaltyTierDiscountsBottomSheet
import com.bukuwarung.payments.bottomsheet.PlatformFeeBottomSheet
import com.bukuwarung.payments.bottomsheet.PpobSendMessageBottomSheet
import com.bukuwarung.payments.checkout.PaymentCategoryActivity
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_EWALLET
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_LISTRIK
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_LISTRIK_POSTPAID
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PLN_POSTPAID
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PULSA_POSTPAID
import com.bukuwarung.payments.constants.PpobConst.UI_VARIANT_UNIVERSAL_CHECKOUT
import com.bukuwarung.payments.constants.VerificationStatus
import com.bukuwarung.payments.data.model.AgentFeeInfo
import com.bukuwarung.payments.data.model.CustomerProfile
import com.bukuwarung.payments.data.model.DestinationBankInformation
import com.bukuwarung.payments.data.model.FinProPaymentMethodDetail
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.FinproPayments
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_CANCELLED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_COMPLETED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_EXPIRED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_FAILED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_HOLD
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_PAID
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_PENDING
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_REFUNDED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_REFUNDING
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_REFUNDING_FAILED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_REJECTED
import com.bukuwarung.payments.data.model.PaymentProgress
import com.bukuwarung.payments.data.model.PaymentReceiptDto
import com.bukuwarung.payments.data.model.PpobMetaData
import com.bukuwarung.payments.data.model.PpobProductDetail
import com.bukuwarung.payments.deeplink.handler.QrisSignalHandler
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.dialog.AddFavouriteDialog
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.view.VehicleTaxFragment
import com.bukuwarung.payments.ppob.confirmation.view.AdminFeeBottomSheet
import com.bukuwarung.payments.saldo.TopupSaldoActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.viewmodels.PaymentHistoryDetailViewModel
import com.bukuwarung.payments.widget.DisbursalBankView
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.ui.BukuDialog
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.ComponentUtil
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.DateTimeUtils.DD_MMM_YYYY_HH_MM
import com.bukuwarung.utils.DateTimeUtils.getInvoiceExpiryTime
import com.bukuwarung.utils.DateTimeUtilsKt
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.ShareUtilities
import com.bukuwarung.utils.ShareUtils
import com.bukuwarung.utils.SharingUtilReceiver
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.clearDrawableTint
import com.bukuwarung.utils.dp
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.greaterThan
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFalse
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.isNotZero
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orDash
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.parseToZeroHourDate
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setDrawableRightListener
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.toBoolean
import com.bumptech.glide.Glide
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.crashlytics.FirebaseCrashlytics

import java.util.Calendar
import java.util.Date
import javax.inject.Inject
import kotlin.math.abs
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentHistoryDetailsActivity : BaseActivity(),
    BankAccountListBottomSheetFragment.BtSheetBankAccountListener,
    BankAccountListBottomSheetFragment.RefundBankAccountListener,
    PpobChangeSellingPriceBottomSheet.PpobChangeSellingPriceListener,
    ContactSearchResultsFragment.OnCustomerSelectedCallback, BukuDialog.Callback,
    PaymentPendingInfoBottomsheet.ICommunicator, Navigator, PlatformFeeBottomSheet.Callback,
    PpobSendMessageBottomSheet.CallBack {

    private lateinit var binding: ActivityPaymentHistoryDetailBinding
    private val viewModel: PaymentHistoryDetailViewModel by viewModels()
    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID) }
    private val orderId by lazy {
        intent?.getStringExtra(ORDER_ID) ?: intent?.getStringExtra(REQUEST_ID)
    }
    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE) }
    private var category = ""
    private val amount by lazy { intent?.getStringExtra(AMOUNT) }
    private val name by lazy { intent?.getStringExtra(NAME) }
    private val ledgerAccountId by lazy { intent?.getStringExtra(LEDGER_ACCOUNT_ID) }
    private var isFromNotif: Boolean = false
    private var userContactFragment: UserContactFragment? = null
    private lateinit var adapter: PaymentHistoryTimelineAdapter
    private var statusList: List<PaymentProgress>? = null
    private var showStatus: Boolean = true
    private var showTransactionDetail: Boolean = false
    private var showInstructionOne: Boolean = false
    private var showInstructionTwo: Boolean = false
    private var showInstructionThree: Boolean = false
    private var isPending = true
    private var timer: CountDownTimer? = null
    private var paymentHistoryDetailViewModelViewState: PaymentHistoryDetailViewModel.ViewState? = null
    private var analyticsTypeValue: String = ""
    private var printingDialog: PrintingDialog? = null
    private var refundBankAccount: RefundBankAccount? = null
    private var order: FinproOrderResponse? = null
    private var serviceFee: Double? = null
    private var haveLoggedVisitEvent: Boolean = false
    private var showReceipt = false
    private var cashTransactionId = ""
    private var entryPointForRefundBank = PAYMENT_DETAILS
    private var paymentInBanks: List<BankAccount> = emptyList()
    private var isPendingTrxTimeExceed = false
    private var previousStatus = ""
    private var checkTrxInfo = false
    private var refreshScreenEventName = ""
    private var refreshScreenState = ""
    private var downloadId = 0L
    private val handler = Handler(Looper.getMainLooper())



    @Inject
    lateinit var neuro: Neuro

    companion object {
        private const val MESSAGE = "message"
        private const val DISPLAY_NAME = "displayName"
        private const val ORDER_ID = "orderId"
        private const val PAYMENT_TYPE = "paymentType"
        private const val FROM_MAIN_ACTIVITY = "fromMainActivity"
        private const val FROM_ASSIST_ACTIVITY = "FROM_ASSIST_ACTIVITY"
        private const val IS_SUCCESS = "IS_SUCCESS"
        private const val ENTRY_POINT = "entry_point"
        private const val CUSTOMER_ID = "customerId"
        private const val LEDGER_ACCOUNT_ID = "ledger_account_id"
        private const val AMOUNT = "amount"
        private const val NAME = "name"
        private const val REQUEST_ID = "paymentRequestId"
        private const val SUCCESS = "success"
        const val PAYMENT_CATEGORY = "payment_category"

        // for reference how these variables are stored on backend is unpaid=0,paid=1,pending=2
        private const val PENDING = "PENDING"
        private const val UNPAID = "UNPAID"
        private const val PAID = "PAID"

        fun createIntent(
            context: Context,
            customerId: String? = null,
            orderId: String?,
            paymentType: String?,
            displayName: String? = null,
            fromMainActivity: Boolean = true,
            isFromAssistPage: Boolean = false,
            isSuccess: Boolean = false,
            entryPoint: String = "", message: String = "",
            ledgerAccountId: String? = null
        ): Intent {
            val intent = Intent(context, PaymentHistoryDetailsActivity::class.java)
            intent.putExtra(CUSTOMER_ID, customerId)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(PAYMENT_TYPE, paymentType)
            intent.putExtra(FROM_MAIN_ACTIVITY, fromMainActivity)
            intent.putExtra(FROM_ASSIST_ACTIVITY, isFromAssistPage)
            intent.putExtra(IS_SUCCESS, isSuccess)
            intent.putExtra(DISPLAY_NAME, displayName)
            intent.putExtra(ENTRY_POINT, entryPoint)
            intent.putExtra(MESSAGE, message)
            intent.putExtra(LEDGER_ACCOUNT_ID, ledgerAccountId)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState != null) {
            // Restore value of members from saved state
            haveLoggedVisitEvent = savedInstanceState.getString("haveLoggedVisitEvent") == "TRUE"
        }
        if (Build.VERSION.SDK_INT >= 33 && applicationInfo.targetSdkVersion >= 33) {
            registerReceiver(
                onDownloadComplete,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
                Context.RECEIVER_EXPORTED
            )
        } else {
            registerReceiver(
                onDownloadComplete,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            )
        }

    }

    override fun setViewBinding() {
        binding = ActivityPaymentHistoryDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setToolBarView()

        isFromNotif = intent.getStringExtra(AppConst.IS_FROM_NOTIF).toBoolean()
        adapter = PaymentHistoryTimelineAdapter(::showReceiptUI)
        registerObservers()
        binding.bukuErrorView.addCallback(errorViewCallBack)
        viewModel.init(customerId, orderId, paymentType, ledgerAccountId = ledgerAccountId)
        showDialog()
        setTransactionStatus()
        setTransactionDetail()
        binding.includePaymentDetailTop.includeDigitalReceipt.root.setSingleClickListener {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_SHARE_PAYMENT_STATUS_TRACKING_LINK,
                PropBuilder().put(
                    AnalyticsConst.TYPE,
                    if (viewModel.isPaymentIn()) AnalyticsConst.PAYMENT_IN else AnalyticsConst.PAYMENT_OUT
                ).put(AnalyticsConst.CURRENT_STATUS, viewModel.getStatus())
                    .put(AnalyticsConst.AMOUNT, order?.amount), false, false, false
            )
            viewModel.shareRequestPaymentTemplate(true)
        }
        serviceFee = order?.agentFeeInfo?.amount
        binding.includePaymentReceipt.tvEdit.setSingleClickListener {
            val amount = if (viewModel.isPaymentOut())
                order?.amount?.minus(order?.fee.orNil)
            else order?.amount
            EditPaymentReceiptDialog.newInstance(amount, serviceFee, order?.description)
                .show(supportFragmentManager, "EditPaymentReceipt")
        }
        binding.btnCompletePayment.setSingleClickListener {
            if (viewModel.isPaymentIn()) {
                viewModel.shareRequestPaymentTemplate()
            } else {
                viewModel.onInstructionClicked()
            }
        }
        binding.includePaymentStatus.tvRefresh.setSingleClickListener {
            storeInfoAndRefreshScreen(AnalyticsConst.EVENT_CLICK_REFRESH_STATUS, "")
        }
        binding.includePaymentTransactionDetail.ivChangePaymentCategory.setSingleClickListener {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CLICK_PAYMENT_CATEGORY_FIELD,
                PropBuilder()
                    .put(
                        AnalyticsConst.TYPE,
                        if (viewModel.isPaymentOut()) AnalyticsConst.PAYMENT_OUT else AnalyticsConst.PAYMENT_IN
                    )
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_DETAIL)
            )
            startPaymentCategorySelectedForResult.launch(
                PaymentCategoryActivity.createIntent(
                    this,
                    if (viewModel.isPaymentOut()) PaymentConst.DisbursementRequest else PaymentConst.PaymentRequest,
                    order?.paymentCategory?.paymentCategoryId
                )
            )
        }
        binding.tvCsSupport.setSingleClickListener { viewModel.onHelpClicked() }
        binding.tvCsSupportTop.setSingleClickListener { viewModel.onHelpClicked() }
    }

    private fun storeInfoAndRefreshScreen(eventName: String, state: String) {
        refreshScreenEventName = eventName
        refreshScreenState = state
        previousStatus = order?.status ?: ""
        checkTrxInfo = true
        refreshScreen()
    }

    private fun showReceiptUI(showReceipt: Boolean) {
        this.showReceipt = showReceipt
        binding.includePaymentReceipt.root.visibility = showReceipt.asVisibility()
        if(showReceipt) showKirimButtonToolTip()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        val alreadyLoggedEvent = if (haveLoggedVisitEvent) "TRUE" else "FALSE"
        outState.putString("haveLoggedVisitEvent", alreadyLoggedEvent)
        super.onSaveInstanceState(outState)
    }

    private fun setAnalyticsType(state: PaymentHistoryDetailViewModel.ViewState) {
        analyticsTypeValue = when {
            viewModel.isPaymentIn() -> AnalyticsConst.IN
            viewModel.isPaymentOut() -> AnalyticsConst.OUT
            else -> PpobConst.CATEGORY_ANALYTICS_MAP[if (state.ppobCategory == CATEGORY_LISTRIK && state.ppobItem?.beneficiary?.code == CATEGORY_PLN_POSTPAID) {
                CATEGORY_PLN_POSTPAID
            } else {
                state.ppobCategory
            }] ?: ""
        }
    }

    private fun setTransactionDetail() {
        with(binding.includePaymentTransactionDetail) {
            ivArrow.setSingleClickListener {
                if (!showTransactionDetail) {
                    ivArrow.setImageResource(R.drawable.ic_chevron_down)
                    gpExpandable.hideView()
                    clLayout.setPadding(0, 0, 0, 0)
                } else {
                    ivArrow.setImageResource(R.drawable.ic_chevron_up)
                    gpExpandable.showView()
                    checkAndHideNotes()
                    clLayout.setPadding(0, 0, 0, 16.dp)
                }
                showTransactionDetail = !showTransactionDetail
            }

        }
    }

    private fun setTransactionStatus() {
        with(binding.includePaymentStatus) {
            rvTimeline.adapter = adapter
            ivArrow.setSingleClickListener {
                ivArrow.setImageResource(if (showStatus) R.drawable.ic_chevron_down else R.drawable.ic_chevron_up)
                showStatus = !showStatus
                submitList()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.refreshData()
    }

    override fun subscribeState() {
        // no implementation required
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val isFromAssist = intent?.getBooleanExtra(FROM_ASSIST_ACTIVITY, false)
        val isSuccess = intent?.getBooleanExtra(IS_SUCCESS, false)
        with(binding) {
            if (isFromAssist == true) {
                svView.fullScroll(ScrollView.FOCUS_UP)
                if (isSuccess.isFalse) {
                    tvError.showView()
                    tvError.text = getString(R.string.ticket_failed)
                    tvErrorAction.showView()
                    tvErrorAction.text = getString(R.string.retry)
                    tvErrorAction.setSingleClickListener {
                        viewModel.onHelpClicked()
                    }
                }
            } else {
                tvError.hideView()
            }
        }
    }

    override fun onRefundBankAccountSelected(bankAccount: RefundBankAccount, entryPoint: String) {
        refundBankAccount = bankAccount
        entryPointForRefundBank = entryPoint
        startPinSelectBankForResult.launch(
            NewPaymentPinActivity.createIntent(
                this,
                PinType.PIN_CONFIRM.toString()
            )
        )
    }

    override fun addNewRefundBankAccount(entryPoint: String) {
        entryPointForRefundBank = entryPoint
        startPinAddBankForResult.launch(
            NewPaymentPinActivity.createIntent(
                this,
                PinType.PIN_CONFIRM.toString()
            )
        )
    }

    private fun logPaymentDetailVisitEvent(reason: String = "") {
        if (!haveLoggedVisitEvent) {
            val prop = PropBuilder().apply {
                put(AnalyticsConst.PAYMENT_TYPE, paymentType)
                put(AnalyticsConst.ORDER_ID, orderId)
                put(AnalyticsConst.STATUS, order?.status)
                put(AnalyticsConst.REASON, reason)
                put(AnalyticsConst.ENTRY_POINT, intent.getStringExtra(ENTRY_POINT))
            }
            AppAnalytics.trackEvent(AnalyticsConst.PAYMENT_DETAIL_VISIT, prop, false, true, false)
            haveLoggedVisitEvent = true
        }
    }

    private fun setToolBarView() {
        binding.includeToolBar.toolBarLabel.text = getString(R.string.label_payment_detail)
        binding.includeToolBar.tbPpob.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_arrow_back)
        binding.includeToolBar.tbPpob.setNavigationOnClickListener {
            onBackPressed()
        }
        binding.includeToolBar.ivHelp.setSingleClickListener {
            if (AppConfigManager.getInstance().useWebView()) {
                val intent = Intent(this, HelpCenterActivity::class.java)
                intent.putExtra(AppConst.URL, RemoteConfigUtils.getPaymentConfigs().supportUrls.payments)
                intent.putExtra(AppConst.TITLE, getString(R.string.help))
                startActivity(intent)
            } else {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(AppConst.BANTUAN)))
            }
        }
    }

    private fun registerObservers() {
        viewModel.viewState.observe(this, Observer {
            paymentHistoryDetailViewModelViewState = it
            if (it.isPaymentCompleted || it.hasPaidStatus) {
                populatePaymentReceipt(it)
            }

            setAnalyticsType(it)
            loaderView(it.loader || it.retryLoader)
            binding.bukuErrorView.hideView()
            with(binding.bukuErrorView) {
                if (it.serverError) {
                    showView()
                    <EMAIL>()
                    setErrorType(
                        BaseErrorView.Companion.ErrorType.CUSTOM,
                        getString(R.string.server_error_title),
                        getString(R.string.server_error_subtitle),
                        getString(R.string.reload), R.drawable.ic_server_down
                    )
                } else if (it.internetError) {
                    showView()
                    <EMAIL>()
                    setErrorType(
                        BaseErrorView.Companion.ErrorType.CUSTOM,
                        getString(R.string.no_connection_title),
                        getString(R.string.no_connection_message),
                        getString(R.string.reload), R.drawable.ic_no_inet
                    )
                }
            }
            viewModel.selectedBankAccount.observe(this) {
                paymentInBanks = it
            }
            it.transactionDetails?.let { trx ->
                binding.includePaymentDetailTop.tvCollectMoneyValue.setDrawable(
                    0, R.drawable.ic_external_link, 0, 0
                )
                binding.includePaymentDetailTop.tvCollectMoneyValue.setOnClickListener {
                    startActivity(
                        createIntent(
                            this, null,
                            trx.orderId, trx.items?.firstOrNull()?.sku, ""
                        )
                    )
                }
            }
        })

        viewModel.observeDetail.observe(this, Observer {
            when (it) {
                is PaymentHistoryDetailViewModel.DetailEvent.ApiError -> showPaymentDownBottomSheet(
                    it.isServerError,
                    it.errorMessage
                )
                is PaymentHistoryDetailViewModel.DetailEvent.ShowProductData -> handleOrderData(it.detail)
                is PaymentHistoryDetailViewModel.DetailEvent.OnHelpClicked -> openHelp(
                    it.isPpob,
                    it.paymentAmount,
                    it.userId,
                    it.paymentId,
                    it.isFailed,
                    it.hasPaidStatus
                )
                is PaymentHistoryDetailViewModel.DetailEvent.OnInstructionClicked -> continuePayment(
                    it.url,
                    it.prop,
                    it.paymentMethod,
                    it.customerPhoneNumber
                )
                is PaymentHistoryDetailViewModel.DetailEvent.UpdateTheTimerMessage -> updateTheTimerMessage(it.pendingTime)
                is PaymentHistoryDetailViewModel.DetailEvent.ShowPendingTrxHelpOption -> showPendingTrxHelpOption()
                is PaymentHistoryDetailViewModel.DetailEvent.ShowPendingTrxHelpOptionInRed -> showPendingTrxHelpOptionInRed()
                is PaymentHistoryDetailViewModel.DetailEvent.UpdateServiceFee -> updateServiceFeeInReceipt(it.amountFeeInfo)
                is PaymentHistoryDetailViewModel.DetailEvent.ShowToast -> Toast.makeText(
                    this,
                    it.message?:getString(it.stringId),
                    Toast.LENGTH_LONG
                ).show()
                is PaymentHistoryDetailViewModel.DetailEvent.refreshScreen -> refreshScreen()
                is PaymentHistoryDetailViewModel.DetailEvent.OnQrisBankSet -> handleQrisBankSuccess()
                is PaymentHistoryDetailViewModel.DetailEvent.ShowCashTransactionLayout -> it.cashTransactionEntity?.let { cashEntity ->
                    showCashTransaction(
                        cashEntity,
                        it.isPpob
                    )
                }
                is PaymentHistoryDetailViewModel.DetailEvent.ShowAddFavDialog -> showAddFavDialog()
                is PaymentHistoryDetailViewModel.DetailEvent.RefundBankSet -> {
                    updateRefundBankInfo(it.refundBank)
                }
                is PaymentHistoryDetailViewModel.DetailEvent.FinishActivity -> {
                    FirebaseCrashlytics.getInstance().recordException(
                        Exception("NPE: Missing businessId: ${it.missingBusinessId}, missing orderId: ${it.missingOrderId}")
                    )
                    finish()
                }
            }
        })

        viewModel.navigation.observeEvent(this, Observer {
            if (it is PaymentHistoryDetailViewModel.NavigationCommand.ShareInvoiceCommand) {
                val receiverIntent = Intent(this, ReferralSharingReceiver::class.java)
                receiverIntent.putExtra(SharingUtilReceiver.EVENT_NAME, "payment_request_share")
                shareText(it.paymentMessage, receiverIntent)
            }
        })
    }

    private fun showCashTransaction(cashTransactionEntity: CashTransactionEntity, isPpob: Boolean) {
        if (isPpob) {
            binding.includeAutorecordedInfo.root.showView()
            binding.includeAutorecordedInfo.tvCashTransaction.text = Utilities.makeSectionOfTextClickable(
                getString(R.string.cash_transaction_text),
                getString(R.string.cash_transaction_text_bold),
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        finish()
                        val intent = CashTransactionDetailActivity.getNewIntent(
                            this@PaymentHistoryDetailsActivity,
                            cashTransactionEntity.cashTransactionId,
                            true
                        )
                        startActivity(intent)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.color = getColorCompat(R.color.blue_60)
                    }
                })
            binding.includeAutorecordedInfo.tvCashTransaction.movementMethod = LinkMovementMethod.getInstance()
        } else {
            val destinationBankInformation = order?.payments?.firstOrNull()?.destinationBankInformation
            binding.includeCashTransaction.root.visibility =
                (!(destinationBankInformation?.flag == BANK_DOWN_TIME && destinationBankInformation.message.isNotNullOrBlank())).asVisibility()
            binding.includeCashTransaction.tvCashTransaction.text = Utilities.makeSectionOfTextClickable(
                getString(R.string.cash_transaction_text),
                getString(R.string.cash_transaction_text_bold),
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        finish()
                        val intent = CashTransactionDetailActivity.getNewIntent(
                            this@PaymentHistoryDetailsActivity,
                            cashTransactionEntity.cashTransactionId
                        )
                        startActivity(intent)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.color = getColorCompat(R.color.blue_60)
                    }
                })
            binding.includeCashTransaction.tvCashTransaction.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    private fun handleOrderData(order: FinproOrderResponse) {
        this.order = order

        isPending = order.status == STATUS_PENDING
        val item = order.items?.firstOrNull()
        statusList = order.progress?.reversed()
        submitList()
        val reason = PaymentUtils.getReasonForUnsuccessfulPayment(order)
        logPaymentDetailVisitEvent(reason)
        if (checkTrxInfo)
            checkStatusAndRefreshScreen()
        with(binding) {
            tvEdcTag.visibility = order.metadata?.bukuOrigin.equals(AppConst.BUKUWARUNG_EDC).asVisibility()
            includePaymentDetailTop.tvBillAmountValue.text = Utility.formatAmount(order.amount)
            if (order.transactionId.isNotNullOrEmpty()) {
                includePaymentDetailTop.tvTransactionCodeValue.text = order.transactionId
                includePaymentDetailTop.tvTransactionCodeValue.setDrawableRightListener {
                    Utility.copyToClipboard(
                        order.transactionId,
                        this@PaymentHistoryDetailsActivity,
                        getString(R.string.trx_no_copied)
                    )
                }
            } else {
                includePaymentDetailTop.tvTransactionCode.hideView()
                includePaymentDetailTop.tvTransactionCodeValue.hideView()
            }
            tvDate.text = DateTimeUtils.getFormattedLocalDateTime(order.createdAt, DD_MMM_YYYY_HH_MM)
            val paymentMethodName = order.payments?.firstOrNull()?.paymentMethod?.name
            with(includePaymentTransactionDetail){
                tvPaymentMethodValue.text = paymentMethodName.orDash
                tvLoyaltyDiscount.text = getString(R.string.discount_level, order.loyalty?.tierName.orEmpty())
                tvLoyaltyDiscount.setDrawableRightListener {
                    LoyaltyTierDiscountsBottomSheet.createInstance(if (viewModel.isPaymentIn()) PaymentConst.Feature.PAYMENT_IN else PaymentConst.Feature.PAYMENT_OUT)
                        .show(supportFragmentManager, LoyaltyTierDiscountsBottomSheet.TAG)
                }
                val loyaltyDiscountSign = if (viewModel.isPaymentIn()) "+" else "-"
                tvLoyaltyDiscountValue.text =
                    "${loyaltyDiscountSign}${Utility.formatAmount(order.loyalty?.tierDiscount)}"
                grLoyaltyDiscount.visibility = order.loyalty?.tierDiscount.orNil.isNotZero().asVisibility()
                tvSubscriptionDiscount.setDrawableRightListener {
                    Utilities.showTooltip(
                        context = this@PaymentHistoryDetailsActivity,
                        anchor = tvSubscriptionDiscount,
                        text = getString(R.string.bukuwarung_plus_discount_message),
                        gravity = Gravity.BOTTOM,
                        width = 500f
                    )
                }
                tvSubscriptionDiscountValue.text = "-${Utility.formatAmount(order.loyalty?.subscriptionDiscount)}"
                grSubscriptionDiscount.visibility = order.loyalty?.subscriptionDiscount.orNil.isNotZero().asVisibility()
            }
            setStatusRelatedData(order.status, order, item)
            if (item?.fee.orNil > 0.0) {
                includePaymentTransactionDetail.tvTransactionFeeValue.text = Utility.formatAmount(item?.fee)
            } else {
                includePaymentTransactionDetail.tvTransactionFeeValue.text = getString(R.string.free).uppercase()
            }
            when {
                viewModel.isPaymentPpob() -> {
                    category = item?.beneficiary?.category.orEmpty()
                    includePaymentDetailTop.tvBillAmount.text = PaymentUtils.getPpobCategoryName(
                        this@PaymentHistoryDetailsActivity,
                        if (category == CATEGORY_LISTRIK && order.items?.firstOrNull()?.beneficiary?.code == CATEGORY_PLN_POSTPAID) {
                            CATEGORY_PLN_POSTPAID
                        } else {
                            order.items?.firstOrNull()?.beneficiary?.category
                        }
                    )
                    includePaymentDetailTop.apply {
                        tvCollectMoney.text = getString(R.string.produk)
                        tvCollectMoneyValue.text = item?.name
                        tvCollectMoney.visibility = item?.name.isNotNullOrBlank().asVisibility()
                        tvCollectMoneyValue.visibility = item?.name.isNotNullOrBlank().asVisibility()
                        tvReceiverValue.text = item?.beneficiary?.phoneNumber
                        tvReceiver.text = getString(R.string.customer_phone)
                        tvCustomerAccount.hideView()
                        tvCustomerAccountValue.hideView()
                    }
                    includePaymentTransactionDetail.apply {
                        tvTransactionFee.text = getString(R.string.customer_bill)
                        tvTransactionFeeValue.text = Utility.formatAmount(order.cost)
                        grAdminFee.showView()
                        setAdminFeeView(order.items?.firstOrNull()?.adminFee, order.items?.firstOrNull()?.discountedFee)
                        tvAdminFee.setDrawableRightListener {
                            AdminFeeBottomSheet.createInstance(viewModel.isTrainTicket()).show(
                                supportFragmentManager,
                                AdminFeeBottomSheet.TAG
                            )
                        }
                        tvTotalReceived.text = getString(R.string.discount)
                        tvTotalReceivedValue.text = if (order.totalDiscount.orNil != 0.0) {
                            "-${Utility.formatAmount(order.totalDiscount)}"
                        } else {
                            "-"
                        }
                        tvTotalReceivedValue.setTextColor(getColorCompat(R.color.blue_60))
                        if (order.totalBonus.isNotZero()) {
                            grSaldoReward.showView()
                            tvSaldoRewardValue.text = "-" + Utility.formatAmount(order.totalBonus)
                        }
                        tvNominalReceived.text = getString(R.string.label_total_payment)
                        tvNominalReceivedValue.text = Utility.formatAmount(order.amount)
                        tvNotes.text = getString(R.string.label_payment_method)
                        tvNotesValue.text = if (paymentMethodName.isNotNullOrBlank()) {
                            paymentMethodName
                        } else {
                            "-"
                        }
                        tvPaymentMethod.text = getString(R.string.selling_price)
                        tvPaymentMethodValue.text = Utility.formatAmount(
                            if (order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0) {
                                order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
                            } else if (item?.sellingPrice.orNil > 0.0) {
                                item?.sellingPrice
                            } else {
                                order.amount
                            }
                        )
                        renderPaymentMethodsDetail(order)
                    }
                    checkAndHideNotes()
                    setPpobCategoryLayout(item)
                }
                viewModel.isPaymentOutSubscription() -> {
                    includePaymentDetailTop.apply {
                        tvSaldoText.hideView()
                        tvBillAmount.text = getString(R.string.subscription)
                        tvCollectMoney.text = getString(R.string.produk)
                        tvCollectMoneyValue.text = getString(R.string.bukuwarung_plus)
                        tvTransactionCode.showView()
                        tvTransactionCodeValue.text = order.transactionId?:order.orderId
                        tvTransactionCodeValue.showView()
                        tvReceiver.hideView()
                        tvReceiverValue.hideView()
                        tvCustomerAccount.hideView()
                        tvCustomerAccountValue.hideView()
                    }
                    includePaymentReceipt.root.hideView()
                    includePaymentTransactionDetail.apply {
                        grTransactionFee.hideView()
                        grpTotalReceived.hideView()
                        grpNominalReceived.hideView()
                        grpNotes.hideView()
                    }
                }
                viewModel.isPaymentIn() || viewModel.isPaymentOut() || viewModel.isQrisPaymentIn() -> {
                    includePaymentDetailTop.apply {
                        includeDigitalReceipt.root.showView()
                        root.setPadding(0, 0, 0, 0)
                        tvCollectMoneyValue.text = viewModel.getCustomerName()
                        tvReceiverValue.text = item?.beneficiary?.name
                        tvCustomerAccountValue.text =
                            "${item?.beneficiary?.code}-${item?.beneficiary?.accountNumber}"
                    }
                    includePaymentTransactionDetail.apply {
                        if (viewModel.isPaymentIn()) {
                            tvDetailTransaction.text = getString(R.string.billing_detail)
                        }
                        tvNominalReceivedValue.text = Utility.formatAmount(item?.amount)
                        tvTotalReceivedValue.text =
                            Utility.formatAmount(PaymentUtils.getTotalReceivedAmount(order, item))
                        tvNotesValue.text = order.description
                        gpPaymentCategroy.showView()
                        tvPaymentCategoryValue.text = order.paymentCategory?.name ?: "-"
                        if (order.status != STATUS_PENDING)
                            ivChangePaymentCategory.hideView()
                    }
                    when {
                        viewModel.isPaymentIn() -> {
                            disbursalBankView.showView()
                            with(includePaymentTransactionDetail){
                                tvTotalReceived.text = getString(R.string.nominal_you_receive)
                                tvTotalReceivedValue.text = Utility.formatAmount(item?.amount)
                                tvNominalReceived.text = getString(R.string.total_received)
                                tvNominalReceivedValue.text = Utility.formatAmount(PaymentUtils.getTotalReceivedAmount(order, item))
                                grTransactionFee.hideView()
                                grAdminFee.showView()
                                setAdminFeeView(order.items?.firstOrNull()?.fee, order.items?.firstOrNull()?.discountedFee)
                                tvAdminFee.setDrawableRightListener {
                                    PlatformFeeBottomSheet.createInstance()
                                        .show(supportFragmentManager, PlatformFeeBottomSheet.TAG)
                                }
                            }
                            disbursalBankView.setView(order,
                                object : DisbursalBankView.Callback {
                                    override fun setDisbursalBank(bankIds: List<String>?) {
                                        openDisbursalBankList(
                                            bankIds, item?.beneficiary?.code,
                                            item?.beneficiary?.accountNumber
                                        )
                                    }
                                })
                        }
                        viewModel.isPaymentOut() -> {
                            includePaymentDetailTop.apply {
                                tvBillAmount.text = getString(R.string.payment_amount)
                                tvCollectMoney.text = getString(R.string.pay_money_to)
                                tvReceiverValue.hideView()
                                tvReceiver.hideView()
                            }
                            includePaymentTransactionDetail.apply {
                                tvTotalReceived.text = getString(R.string.total_received_by_customers)
                                tvTotalReceivedValue.text = Utility.formatAmount(PaymentUtils.getTotalReceivedAmount(order, item))
                                tvNominalReceived.text = getString(R.string.nominal_you_pay)
                                tvNominalReceivedValue.text = Utility.formatAmount(item?.amount)
                                grTransactionFee.hideView()
                                grAdminFee.showView()
                                setAdminFeeView(order.items?.firstOrNull()?.fee, order.items?.firstOrNull()?.discountedFee)
                                tvAdminFee.setDrawableRightListener {
                                    PlatformFeeBottomSheet.createInstance()
                                        .show(supportFragmentManager, PlatformFeeBottomSheet.TAG)
                                }
                                if (order.paymentCategory?.name.equals("Komisi Agen", true)) {
                                    grSaldoReward.showView()
                                    tvSaldoRewardValue.text = "-" + Utility.formatAmount(item?.amount)
                                    tvPaymentMethodValue.text = order.paymentCategory?.name
                                    gpPaymentCategroy.hideView()
                                    grpNotes.hideView()
                                }
                            }
                            renderPaymentMethodsDetail(order)
                        }
                        viewModel.isQrisPaymentIn() -> {
                            val qrisDetail = order.payments?.firstOrNull()?.paymentMethod?.detail
                            includePaymentDetailTop.apply {
                                root.setPadding(0, 0, 0, 16.dp)
                                includeDigitalReceipt.root.hideView()
                                tvCollectMoney.hideView()
                                tvCollectMoneyValue.hideView()
                                tvQrisRrn.showView()
                                tvQrisRrnValue.showView()
                                tvQrisRrnValue.text = qrisDetail?.rrn
                            }
                            if (RemoteConfigUtils.getPaymentConfigs().hideBiayaAdminForQris.isTrue) {
                                includePaymentTransactionDetail.grTransactionFee.hideView()
                            }
                            if (qrisDetail?.mdr?.greaterThan(0.0).isTrue) {
                                includePaymentTransactionDetail.tvQrisFee.text =
                                    getString(
                                        R.string.two_spaced_strings,
                                        RemoteConfigUtils.getAppText().qrisMDRLabel.orEmpty(),
                                        getString(
                                            R.string.x_of_bill_amount,
                                            "${Utility.formatCurrency(qrisDetail?.mdr)}%"
                                        )
                                    )
                            } else {
                                includePaymentTransactionDetail.tvQrisFee.text =
                                    RemoteConfigUtils.getAppText().qrisMDRLabel.orEmpty()
                            }
                            if (qrisDetail?.qrisFee?.greaterThan(0.0).isTrue) {
                                includePaymentTransactionDetail.tvQrisFeeValue.text =
                                    Utility.formatAmount(qrisDetail?.qrisFee)
                            } else {
                                includePaymentTransactionDetail.tvQrisFeeValue.text =
                                    getString(R.string.free).uppercase()
                            }
                            includePaymentTransactionDetail.tvQrisFee.setOnClickListener {
                                Utilities.showTooltip(
                                    this@PaymentHistoryDetailsActivity, it,
                                    RemoteConfigUtils.getAppText().qrisMDRInfoTooltip.orEmpty(),
                                    Gravity.BOTTOM
                                )
                            }
                            includePaymentTransactionDetail.grQrisFee.showView()
                            includePaymentTransactionDetail.gpPaymentCategroy.hideView()
                            includePaymentTransactionDetail.tvPaymentMethodValue.text =
                                when {
                                    order.customer?.customerName.isNotNullOrBlank() -> {
                                        order.customer?.customerName
                                    }
                                    paymentMethodName.isNotNullOrBlank() -> paymentMethodName
                                    else -> ""
                                }
                            btnCompletePayment.text = getString(R.string.retry)
                            btnCompletePayment.setSingleClickListener {
                                order.orderId?.let { viewModel.retryQrisPayment(it) }
                            }
                        }
                        else -> {
                            btnCompletePayment.text = getString(R.string.waiting_for_payment_in_continue)
                        }
                    }
                }
                viewModel.isPaymentSaldoIn() || viewModel.isPaymentSaldoOut() || viewModel.isSaldoRefund() -> {

                    if (viewModel.isPaymentSaldoIn() && order.payments?.firstOrNull()?.invoicePayment == true) {
                        includePaymentMethod.root.hideView()
                        includePaymentTransactionDetail.grpPaymentMethod.hideView()
                        includePaymentTransactionDetail.grpNominalReceived.hideView()
                    }

                    includePaymentDetailTop.apply {
                        tvCollectMoney.setText(R.string.produk)
                        tvReceiver.hideView()
                        tvReceiverValue.hideView()
                        tvCustomerAccount.hideView()
                        tvCustomerAccountValue.hideView()
                        when {
                            viewModel.isPaymentSaldoIn() -> {
                                tvCollectMoneyValue.setText(R.string.saldo_bukuwarung)
                                tvBillAmount.setText(R.string.topup_saldo)
                            }
                            viewModel.isSaldoRefund() -> {
                                tvCollectMoneyValue.setText(R.string.saldo_refund)
                                tvBillAmount.setText(R.string.money_refund)
                                includePaymentDetailTop.tvCollectMoney.text = getString(R.string.description)
                            }
                            else -> {
                                with(includePaymentDetailTop){
                                    tvCollectMoneyValue.text = item?.name
                                    tvCollectMoney.visibility = item?.name.isNotNullOrBlank().asVisibility()
                                    tvCollectMoneyValue.visibility = item?.name.isNotNullOrBlank().asVisibility()
                                }
                                tvBillAmount.setText(R.string.saldo_out)
                            }
                        }
                    }
                    includePaymentTransactionDetail.apply {
                        tvTotalReceived.setText(R.string.harga_modal)
                        tvTotalReceivedValue.text = Utility.formatAmount(item?.amount)
                        tvNominalReceived.setText(R.string.selling_price)
                        tvNominalReceivedValue.text = Utility.formatAmount(
                            if (order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0)
                                order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
                            else if (item?.sellingPrice != 0.0)
                                item?.sellingPrice
                            else
                                item.amount
                        )
                        if (viewModel.isPaymentSaldoOut() || viewModel.isSaldoRefund()) {
                            tvPaymentMethodValue.setText(R.string.saldo_bukuwarung)
                            includePaymentStatus.root.hideView()
                            includePaymentTransactionDetail.root.hideView()
                        }
                        if (viewModel.isPaymentSaldoIn()) {
                            grpNominalReceived.showView()
                            tvNominalReceived.text = getString(R.string.label_total_payment)
                            tvNominalReceivedValue.text = Utility.formatAmount(order.amount)
                            grpTotalReceived.hideView()
                            grpNotes.hideView()
                        }
                    }
                    includePaymentReceipt.root.hideView()
                }
                viewModel.isPaymentSaldoRedemption() -> {
                    includePaymentDetailTop.tvBillAmount.text = getString(R.string.total_saldo)
                    includePaymentDetailTop.tvCollectMoney.text = getString(R.string.description)
                    includePaymentDetailTop.tvCollectMoneyValue.text = order.description
                    includePaymentDetailTop.tvTransactionCode.text = getString(R.string.conversion_id)
                    includePaymentDetailTop.tvReceiver.hideView()
                    includePaymentDetailTop.tvReceiverValue.hideView()
                    includePaymentDetailTop.tvCustomerAccount.hideView()
                    includePaymentDetailTop.tvCustomerAccountValue.hideView()
                    includePaymentStatus.root.hideView()
                    includePaymentReceipt.root.hideView()
                    includePaymentTransactionDetail.root.hideView()
                    poweredByFooter.hideView()
                }
                viewModel.isPaymentSaldoCashback() -> {
                    with(includePaymentDetailTop) {
                        when {
                            PaymentConst.isCashbackIn(order) -> {
                                tvBillAmount.text = getString(R.string.saldo_bonus_in)
                                tvTransactionCode.text = getString(R.string.payment_code)
                                tvTransactionCodeValue.text = order.metadata?.transactionCode
                                order.metadata?.expiryDate?.let {
                                    tvExpirationDateValue.text =
                                        DateTimeUtilsKt.getFormattedDateFromString(
                                            it,
                                            DateTimeUtilsKt.YYYY_MM_DD,
                                            DateTimeUtilsKt.DD_MMM_YYYY
                                        )
                                    grpExpirationDate.showView()
                                }
                            }
                            PaymentConst.isCashbackOut(order) -> {
                                tvBillAmount.text = getString(R.string.saldo_bonus_out)
                                tvTransactionCode.text = getString(R.string.payment_code)
                                tvTransactionCodeValue.text = order.metadata?.transactionCode
                                order.metadata?.expiryDate?.let {
                                    tvExpirationDateValue.text =
                                        DateTimeUtilsKt.getFormattedDateFromString(
                                            it,
                                            DateTimeUtilsKt.YYYY_MM_DD,
                                            DateTimeUtilsKt.DD_MMM_YYYY
                                        )
                                    grpExpirationDate.showView()
                                }
                            }
                            else -> {
                                tvBillAmount.text = getString(R.string.cashback)
                                tvTransactionCode.text = getString(R.string.conversion_id)
                            }
                        }
                        includePaymentDetailTop.tvCollectMoney.text = getString(R.string.description)
                        tvCollectMoneyValue.text = order.metadata?.categoryDisplayName
                            ?: getString(R.string.label_payment_in)
                        tvReceiver.hideView()
                        tvReceiverValue.hideView()
                        tvCustomerAccount.hideView()
                        tvCustomerAccountValue.hideView()
                        viewModel.fetchTransactionDetails(
                            order.transactionId,
                            order.metadata?.aggregationData?.metadata?.firstOrNull()?.ledgerable?.type
                        )
                    }
                    includeCashTransaction.root.showView()
                    includeCashTransaction.tvCashTransaction.text =
                        getString(R.string.saldo_cashback_credit_info)
                    includePaymentStatus.root.showView()
                    includePaymentReceipt.root.hideView()
                    includePaymentTransactionDetail.root.hideView()
                    poweredByFooter.hideView()
                    grpSupport.hideView()
                    when (order.status) {
                        STATUS_PENDING -> {
                            includeCashTransaction.root.showView()
                            val formattedExpectedDate = DateTimeUtilsKt.getFormattedDateFromString(
                                order.metadata?.expectedDate,
                                DateTimeUtilsKt.YYYY_MM_DD,
                                DateTimeUtilsKt.DD_MMM_YYYY
                            )
                            formattedExpectedDate?.let {
                                includeCashTransaction.tvCashTransaction.text =
                                    getString(R.string.cashback_will_be_credited_on, formattedExpectedDate)
                            }
                        }
                        else -> includeCashTransaction.root.hideView()
                    }
                }
                else -> {}
            }
            viewModel.getTransactionDataByOrderId(orderId, viewModel.isPaymentPpob())
            if (PpobUtils.isBnpl(order)) {
                binding.includePaymentDetailTop.tvSaldoText.hideView()
                order.payments?.getOrNull(0)?.paymentMethod?.detail?.let { setBnplMessageLayout(it) }
                with(binding.includePaymentTransactionDetail) {
                    tvBnplAdminFee.setDrawableRightListener {
                        Utilities.showTooltip(
                            this@PaymentHistoryDetailsActivity,
                            tvBnplAdminFee,
                            getString(R.string.bnpl_fee_info, ""),
                            Gravity.BOTTOM
                        )
                    }
                    tvBnplAdminFeeValue.text =
                        Utility.formatAmount(order.items?.getOrNull(0)?.details?.bnplAdminFee?.orNil)
                    grBnplAdminFee.showView()
                }
            }
        }
        showVoucherDetailLink(order)
    }

    private fun renderPaymentMethodsDetail(order: FinproOrderResponse) {
        /**
         * If saldoLedgerAccountId is not null, saldo was used for payment
         * If cashbackLedgerAccountId is not null, Komisi Agen was used for payment
         */
        val saldoTrxDetail = order.payments?.firstOrNull()?.paymentMethod?.detail?.saldoTrx
        val saldoBonusTrxDetail =
            order.payments?.firstOrNull()?.paymentMethod?.detail?.saldoBonusTrx

        with(binding.includePaymentTransactionDetail) {
            /**
             * If any of these methods are used, we can hide tvNotes and tvNotesValue, since that is
             * used to show payment method for PPOB orders
             */
            if (saldoTrxDetail != null || saldoBonusTrxDetail != null) {
                if (viewModel.isPaymentPpob()) {
                    grpNotes.hideView()
                }
                if (viewModel.isPaymentOut()) {
                    grpPaymentMethod.hideView()
                }
                val ledgerTrxId =
                    order.payments.firstOrNull()?.paymentMethod?.detail?.ledgerTransactionId

                with(layoutPaymentMethodsDetail) {
                    clLayoutPaymentMethodsDetail.showView()
                    saldoTrxDetail?.let {
                        tvSaldoValue.apply {
                            text = Utility.formatAmount(it.amount)
                            showView()
                            tvSaldoTitle.showView()
                            setOnClickListener { _ ->
                                openOrderDetail(
                                    ledgerTrxId, it.ledgerAccountId, PaymentConst.TYPE_SALDO_OUT
                                )
                            }
                        }
                    }
                    saldoBonusTrxDetail?.let {
                        tvSaldoBonusValue.apply {
                            text = Utility.formatAmount(it.amount)
                            showView()
                            tvSaldoBonusTitle.showView()
                            setOnClickListener { _ ->
                                openOrderDetail(
                                    ledgerTrxId, it.ledgerAccountId, PaymentConst.TYPE_CASHBACK_OUT
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun openOrderDetail(
        orderId: String?, ledgerAccountId: String?, paymentType: String
    ) {
        startActivity(
            createIntent(
                context = this,
                orderId = orderId,
                paymentType = paymentType,
                fromMainActivity = false,
                entryPoint = AnalyticsConst.PAYMENTS_HISTORY,
                ledgerAccountId = ledgerAccountId
            )
        )
    }

    private fun showVoucherDetailLink(order: FinproOrderResponse) {
        // We need to show voucher redirection link only for successful saldo out orders
        if (
            order.metadata?.aggregationData?.metadata?.firstOrNull()?.ledgerable?.type == PaymentConst.CASHBACK_TYPE_VOUCHER
            && order.status == STATUS_COMPLETED
            && viewModel.isSaldoBonusOut()
        ) {
            Utilities.safeLet(
                order.transactionId,
                RemoteConfigUtils.getMxMwebConfigs().voucherHistoryWebUrl
            ) { trxId, webUrl ->
                binding.includePaymentDetailTop.tvCollectMoneyValue.setDrawable(
                    0, R.drawable.ic_external_link, 0, 0
                )
                binding.includePaymentDetailTop.tvCollectMoneyValue.setOnClickListener {
                    val url = "$webUrl$trxId?entryPoint=${PAYMENT_DETAILS}"
                    val intent = WebviewActivity.createIntent(this, url, AppConst.EMPTY_STRING)
                    val prop = PropBuilder().apply {
                        put(AnalyticsConst.ENTRY_POINT, PAYMENT_DETAILS)
                    }
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_LOYALTY_CHECK_UNLOCKED_VCOUPON_CODE,
                        prop
                    )
                    startActivity(intent)
                }
            }
        }
    }

    private fun setBnplMessageLayout(detail: FinProPaymentMethodDetail) {
        with(binding.baBnpl) {
            showView()
            setClickAction {
                redirect(detail.deeplink.orEmpty())
            }
            setBackgroundType("info")
            addText(detail.displayText)
            addActionText(detail.ctaText.orEmpty())
        }
    }

    private fun setAdminFeeView(adminFee: Double?, discountedFee: Double?) {
        with(binding.includePaymentTransactionDetail) {
            if (viewModel.isPaymentIn() || viewModel.isPaymentOut()) {
                tvAdminFee.text = getString(R.string.platform_fee)
            } else if(viewModel.isTrainTicket()){
                tvAdminFee.text = getString(R.string.convenience_fee)
            }
            when (adminFee) {
                0.0 -> {
                    tvAdminFeeValue.hideView()
                    tvDiscountedFee.text = getString(R.string.free_upper_case)
                }
                discountedFee -> {
                    tvAdminFeeValue.hideView()
                    val discountedFeeSign = if (viewModel.isPaymentIn()) "-" else ""
                    tvDiscountedFee.text = discountedFeeSign + Utility.formatAmount(adminFee)
                    tvDiscountedFee.text = discountedFeeSign + Utility.formatAmount(adminFee)
                }
                else -> {
                    tvAdminFeeValue.text = Utility.formatAmount(adminFee)
                    val discountedFeeSign = if (viewModel.isPaymentIn()) "-" else ""
                    tvDiscountedFee.text = discountedFeeSign + Utility.formatAmount(discountedFee)
                }
            }
        }
    }

    private fun setSaldoText() {
        if (order?.payments?.firstOrNull()?.paymentMethod?.groupCode.orEmpty() == PpobConst.SALDO_FAMILY_CODE) {
            val saldoCampaign =
                order?.campaigns?.filter { it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.SALDO_METHOD }
            binding.includePaymentDetailTop.tvSaldoText.text = when {
                order?.totalBonus.isNotZero() -> getString(R.string.pay_using_saldo_bonus)
                saldoCampaign.isNullOrEmpty() -> RemoteConfigUtils.getLightningFastSaldoText()
                else -> saldoCampaign.firstOrNull()?.displayText
                    ?: RemoteConfigUtils.getLightningFastSaldoText()
            }
            binding.includePaymentDetailTop.tvSaldoText.showView()
        }
    }

    private fun setPpobCategoryLayout(item: PpobProductDetail?) {
        with(binding) {
            when (item?.beneficiary?.category) {
                CATEGORY_PULSA_POSTPAID -> {
                    includePaymentDetailTop.tvCollectMoneyValue.text = item.details.productName
                    includePaymentDetailTop.tvCustomerAccount.showView()
                    includePaymentDetailTop.tvCustomerAccount.text = getString(R.string.period)
                    includePaymentDetailTop.tvCustomerAccountValue.showView()
                    includePaymentDetailTop.tvCustomerAccountValue.text = item.details.periode
                        ?: "-"
                }

                CATEGORY_LISTRIK -> {
                    includePaymentDetailTop.tvReceiver.text = getString(R.string.customer_id_message)
                    includePaymentDetailTop.tvReceiverValue.text = item.beneficiary.accountNumber
                    includePaymentDetailTop.tvCustomerAccount.showView()
                    includePaymentDetailTop.tvPhoneNumber.showView()
                    includePaymentDetailTop.tvPhoneNumberValue.showView()
                    includePaymentDetailTop.tvCustomerAccountValue.showView()
                    includePaymentDetailTop.tvCustomerAccount.text = getString(R.string.add_contact_hint)
                    includePaymentDetailTop.tvCustomerAccountValue.text = item.details.customerName
                    includePaymentDetailTop.tvPhoneNumber.text = getString(R.string.customer_phone)
                    includePaymentDetailTop.tvPhoneNumberValue.text = item.beneficiary.phoneNumber
                    if (item.beneficiary.code == CATEGORY_PLN_POSTPAID) {
                        includePaymentDetailTop.tvReceiver.hideView()
                        includePaymentDetailTop.tvReceiverValue.hideView()
                        includePaymentDetailTop.tvCollectMoney.text = getString(R.string.customer_id_message)
                        includePaymentDetailTop.tvCollectMoneyValue.text = item.beneficiary.accountNumber
                        includePaymentDetailTop.tvPhoneNumber.text = getString(R.string.period)
                        includePaymentDetailTop.tvPhoneNumberValue.text = item.details.periode
                    }
                }
                CATEGORY_EWALLET -> {
                    includePaymentDetailTop.tvBillAmount.text = getString(R.string.topup_ewallet)
                    includePaymentDetailTop.tvCustomerAccount.text = getString(R.string.customer_phone)
                    includePaymentDetailTop.tvCustomerAccount.showView()
                    includePaymentDetailTop.tvCustomerAccountValue.text = item.beneficiary.phoneNumber
                    includePaymentDetailTop.tvCustomerAccountValue.showView()
                    if (order?.status != STATUS_PENDING) {
                        includePaymentDetailTop.tvReceiverValue.text = item.beneficiary.accountNumber
                        includePaymentDetailTop.tvReceiver.text = getString(R.string.customer_id_message)
                    } else {
                        includePaymentDetailTop.tvReceiverValue.hideView()
                        includePaymentDetailTop.tvReceiver.hideView()
                    }
                }
                PpobConst.CATEGORY_VOUCHER_GAME -> {
                    with(includePaymentDetailTop) {
                        voucherGameGroup.showView()
                        tvCollectMoney.text =
                            getString(R.string.voucher_type)
                        tvCollectMoneyValue.text =
                            order?.metadata?.billerName ?: item.beneficiary.name
                        tvVoucherGameProductValue.text = item.name
                        tvReceiver.text = order?.metadata?.idFieldName
                        tvReceiverValue.text =
                            order?.metadata?.idFieldValue
                    }
                }

                PpobConst.CATEGORY_BPJS -> {
                    with(includePaymentDetailTop) {
                        tvCollectMoney.text =
                            getString(R.string.card_number)
                        tvCollectMoneyValue.text =
                            item.details.customerNumber
                        tvReceiver.text =
                            getString(R.string.input_customer_name)
                        tvReceiverValue.text = item.details.customerName
                        tvCustomerAccount.text = getString(R.string.period)
                        tvCustomerAccount.showView()
                        tvCustomerAccountValue.text = item.details.period
                        tvCustomerAccountValue.showView()
                    }

                }

                PpobConst.CATEGORY_PDAM -> {
                    with(includePaymentDetailTop) {
                        tvCollectMoney.text =
                            getString(R.string.customer_number)
                        tvCollectMoneyValue.text =
                            item.details.customerNumber
                        tvReceiver.text =
                            getString(R.string.input_customer_name)
                        tvReceiverValue.text = item.details.customerName
                        tvCustomerAccount.text = getString(R.string.period)
                        tvCustomerAccount.showView()
                        tvCustomerAccountValue.text = item.details.period
                        tvCustomerAccountValue.showView()
                    }
                }
                PpobConst.CATEGORY_MULTIFINANCE -> {
                    with(includePaymentDetailTop) {
                        tvReceiver.text = getString(R.string.input_customer_name)
                        tvReceiverValue.text = item.details.customerName
                        tvReceiver.visibility = item.details.customerName.isNotNullOrBlank().asVisibility()
                        tvReceiverValue.visibility = item.details.customerName.isNotNullOrBlank().asVisibility()

                        tvCustomerAccount.text = getString(R.string.period)
                        tvCustomerAccountValue.text = item.details.period
                        tvCustomerAccount.visibility = item.details.period.isNotNullOrBlank().asVisibility()
                        tvCustomerAccountValue.visibility = item.details.period.isNotNullOrBlank().asVisibility()
                    }
                }
                PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                    with(includePaymentDetailTop) {
                        tvReceiver.text = getString(R.string.nama_pelanggan)
                        tvReceiverValue.text = item.details.customerName
                        tvReceiver.visibility = item.details.customerName.isNotNullOrBlank().asVisibility()
                        tvReceiverValue.visibility = item.details.customerName.isNotNullOrBlank().asVisibility()

                        tvCustomerAccount.text = getString(R.string.customer_number)
                        tvCustomerAccountValue.text = item.details.customerNumber
                        tvCustomerAccount.visibility = item.details.customerNumber.isNotNullOrBlank().asVisibility()
                        tvCustomerAccountValue.visibility = item.details.customerNumber.isNotNullOrBlank().asVisibility()
                    }
                }
                PpobConst.CATEGORY_VEHICLE_TAX -> {
                    with(includePaymentDetailTop) {
                        tvReceiver.text = getString(R.string.nama_pelanggan)
                        tvReceiverValue.text = item.details.customerName
                        tvReceiver.visibility =
                            item.details.customerName?.isNotNullOrBlank().asVisibility()
                        tvReceiverValue.visibility =
                            item.details.customerName?.isNotNullOrBlank().asVisibility()
                        tvCustomerAccount.text = getString(R.string.policy_number)
                        tvCustomerAccountValue.text = item.details.policyNumber
                        tvCustomerAccount.visibility =
                            item.details.policyNumber?.isNotNullOrBlank().asVisibility()
                        tvCustomerAccountValue.visibility =
                            item.details.policyNumber?.isNotNullOrBlank().asVisibility()
                    }
                }
                PpobConst.CATEGORY_TRAIN_TICKET -> {
                    with(includePaymentDetailTop) {
                        tvCollectMoney.text = getString(R.string.route_label)
                        tvCollectMoneyValue.text = "${item.details.trainOriginStationCode} - ${item.details.trainDestinationStationCode}"
                        tvReceiver.text = getString(R.string.nama_pelanggan)
                        tvReceiverValue.text = item.details.customerName.orDash
                        tvCustomerAccount.text = getString(R.string.mobile_phone_label)
                        tvCustomerAccountValue.text = item.details.customerNumber.orDash
                        tvPhoneNumber.text = getString(R.string.email_label)
                        tvCustomerAccountValue.text = item.details.customerEmail
                        val pdfUrl = order?.items?.firstOrNull()?.details?.eTicket.orEmpty()
                        if (order?.status.equals(STATUS_COMPLETED) && pdfUrl.isNotNullOrBlank() && URLUtil.isValidUrl(pdfUrl))
                            bbDownloadPdf.showView()
                        bbDownloadPdf.setSingleClickListener {
                            val prop = PropBuilder().apply {
                                put(AnalyticsConst.TYPE, AnalyticsConst.TRAIN_TICKET)
                                put(AnalyticsConst.ORDER_ID, orderId.orEmpty())
                            }
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_DOWNLOAD_PPOB_RECEIPT,
                                prop
                            )
                            checkPermissionOrHandlePdfDownload()
                        }
                    }
                }
            }
        }
    }

    private fun checkPermissionOrHandlePdfDownload() {
        if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.WRITE_EXTERNAL_STORAGE
            )
        } else {
            downloadPdf()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    downloadPdf()
                } else {
                    NotificationUtils.alertToast(getString(R.string.write_storage_perm_denied_error))
                }
            }
        }
    }

    private fun downloadPdf() {
        val pdfUrl = order?.items?.firstOrNull()?.details?.eTicket.orEmpty()
        val request = DownloadManager.Request(Uri.parse(pdfUrl))
        val pdfName = URLUtil.guessFileName(pdfUrl, null, AppConst.PDF_MIME_TYPE)
        request.setTitle(pdfName)
        request.allowScanningByMediaScanner()
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, pdfName)
        request.setMimeType(AppConst.PDF_MIME_TYPE)
        downloadId = (getSystemService(DOWNLOAD_SERVICE) as DownloadManager).enqueue(request)
    }

    private val onDownloadComplete: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            //Fetching the download id received with the broadcast
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            //Checking if the received broadcast is for our enqueued download by matching download id
            if (downloadId == id) {
                showTicketDownloadSuccessSnackbar()
            }
        }
    }

    private fun showTicketDownloadSuccessSnackbar() {
        Snackbar.make(
            binding.vwInfo,
            getString(R.string.e_ticket_download_success), 5000
        ).setAction(R.string.open) {

            val intent = Intent(Intent.ACTION_VIEW).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)
                setDataAndType(
                    (getSystemService(DOWNLOAD_SERVICE) as DownloadManager).getUriForDownloadedFile(
                        downloadId
                    ), AppConst.PDF_MIME_TYPE
                )
            }
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            } else {
                NotificationUtils.alertToast(getString(R.string.download_valid_application))
            }
        }
            .setActionTextColor(getColorCompat(R.color.colorPrimary))
            .show()
    }

    private fun setStatusRelatedData(status: String?, order: FinproOrderResponse, item: PpobProductDetail?) {
        binding.apply {
            includePaymentPendingInfo.root.hideView()
            includePaymentMethod.root.hideView()
            baRefundInfo.hideView()
        }
        val payment = order.payments?.firstOrNull()
        when (status) {
            STATUS_PENDING -> {
                setPendingLayout(payment, item)
            }
            STATUS_PAID, STATUS_HOLD -> {
                setPaidLayout(order, order.status == STATUS_HOLD)
                when {
                    order.status == STATUS_HOLD -> setHoldMessageLayout(order.metadata)
                    viewModel.isPaymentPpob() && viewModel.isWebviewPpob() -> {
                        showRecentsAndFavourites()
                    }
                    viewModel.isPaymentOut() || viewModel.isPaymentIn() -> {
                        setDestinationBankInfo(payment?.destinationBankInformation)
                    }
                    else -> {}
                }
            }
            STATUS_COMPLETED -> {
                setCompleteLayout(order)
                if (viewModel.isPaymentPpob() && viewModel.isWebviewPpob() || viewModel.isPaymentOut())
                    showRecentsAndFavourites()
            }

            STATUS_EXPIRED -> {
                setExpiredLayout()
            }

            STATUS_FAILED -> {
                setFailedLayout(order)
            }
            // Rejected state is for Qris failed transaction that isn't retriable
            STATUS_REJECTED -> {
                setRejectedLayout()
            }
            STATUS_CANCELLED -> {
                setCancelledLayout()
            }
            else -> {

            }
        }
    }

    private fun setHoldMessageLayout(metadata: PpobMetaData?) {
        if (metadata == null) return
        if (shouldShowKybInfoBox()) {
            setKybHoldMessageLayout()
        } else {
            with(binding.baMsg) {
                showView()
                setClickAction {
                    val intent = WebviewActivity.createIntent(
                        this@PaymentHistoryDetailsActivity,
                        metadata.faq_link,
                        ""
                    )
                    startActivity(intent)
                }
                setBackgroundType("info")
                addText(metadata.infoboxText)
                addActionText(metadata.faq_cta.orEmpty())
            }
        }
    }

    private fun shouldShowKybInfoBox(): Boolean {
        if (viewModel.isQrisPaymentIn()
            && RemoteConfigUtils.getPaymentConfigs().kybMandatoryFromDate?.parseToZeroHourDate()
                ?.before(Calendar.getInstance()).isTrue
            && (order?.kybStatus == (VerificationStatus.UNVERIFIED.name)
                    || order?.kybStatus == (VerificationStatus.PENDING_VERIFICATION.name)
                    || order?.kybStatus == (VerificationStatus.FAILED_VERIFICATION.name))
        ) {
            return true
        }
        return false
    }

    private fun setKybHoldMessageLayout() {
        with(binding.baMsg) {
            showView()
            when (order?.kybStatus) {
                VerificationStatus.UNVERIFIED.name -> {
                    addText(getString(R.string.payment_after_verification))
                    addActionText(getString(R.string.take_photo))
                }
                VerificationStatus.PENDING_VERIFICATION.name -> {
                    addText(getString(R.string.photo_under_verification, RemoteConfigUtils.getPaymentConfigs().kybVerificationMaxDays))
                    addActionText(getString(R.string.more))
                }
                VerificationStatus.FAILED_VERIFICATION.name -> {
                    addText(getString(R.string.payment_hold_until_verification))
                    addActionText(getString(R.string.take_photo))
                }
            }
            setClickAction {
                startActivity(WebviewActivity.createIntent(context, PaymentUtils.getQrisWebUrl(), ""))
            }
            setBackgroundType("info")
        }
    }

    private fun setCancelledLayout() {
        with(binding) {
            includePaymentStatus.tvRefresh.showView()
            includePaymentDetailTop.tvBillAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                0,
                0,
                R.drawable.ic_progress_icon_failed,
                0
            )
            tvStatus.text = getString(R.string.cancelled_label)
            grpSupport.hideView()
            grpSupportTop.hideView()
            if (viewModel.isPaymentSaldoIn()) {
                btnCompletePayment.showView()
                btnCompletePayment.text = getString(R.string.top_up_saldo)
                btnCompletePayment.setSingleClickListener { topupSaldoInitiated() }
            } else {
                btnCompletePayment.hideView()
            }
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_failed_gray, 0, 0, 0)
            tvStatus.background =
                ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_grey)
        }
    }

    private fun setRejectedLayout() {
        with(binding) {
            tvStatus.text = getString(R.string.failed_status)
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.ic_progress_icon_failed, 0, 0, 0
            )
            tvStatus.background = ContextCompat.getDrawable(
                this@PaymentHistoryDetailsActivity,
                R.drawable.bg_status_red
            )
            tvError.text = getString(R.string.qris_account_locked_warning)
            tvErrorAction.setOnClickListener {
                openQrisBankList()
            }
            tvError.showView()
            tvErrorAction.showView()
        }
    }

    private fun openDisbursalBankList(
        problematicBankIds: List<String>? = null,
        selectedBankCode: String?, selectedAccountNumber: String?
    ) {
        val bankListBtSheet = BankAccountListBottomSheetFragment.createBankAccountInstance(
            PAYMENT_DETAILS,
            null,
            null,
            PaymentConst.TYPE_PAYMENT_IN,
            problematicBankIds as ArrayList<String>,
            selectedBankCode, selectedAccountNumber
        )
        bankListBtSheet.show(supportFragmentManager, BankAccountListBottomSheetFragment.TAG)
    }

    private fun openQrisBankList() {
        // Redirect to QRIS screen
        val sourceLink = SourceLink(
            context = this,
            QrisSignalHandler.getQrisLink(entryPoint = PAYMENT_DETAILS)
        )
        neuro.route(
            sourceLink,
            navigator = object : Navigator{
                override fun navigate(intent: Intent) {
                    startQrisForResult.launch(intent)
                }
            },
            onSuccess = {},
            onFailure = {
                PaymentUtils.handlePaymentsRedirection(
                    this, supportFragmentManager,
                    PAYMENT,
                    PaymentConst.QRIS_HANDLING_KEY,
                    AnalyticsConst.ADD_BANK_ACCOUNT
                )
                FirebaseCrashlytics.getInstance().recordException(it)
            }
        )
    }

    private fun setFailedLayout(order: FinproOrderResponse) {
        with(binding) {
            val refundBank = order.usedRefundBank
            includePaymentStatus.tvRefresh.showView()
            grpSupport.hideView()
            grpSupportTop.showView()
            btnCompletePayment.hideView()
            includePaymentDetailTop.tvBillAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                0,
                0,
                R.drawable.ic_progress_icon_failed,
                0
            )
            if (order.items?.firstOrNull()?.details?.responseCode == AppConst.KWH_LIMIT_RESPONSE_CODE) {
                tvError.showView()
            }
            when (order.progress?.lastOrNull()?.state) {
                STATUS_REFUNDING -> {
                    val refundInfo = when {
                        order.totalBonus.isNotZero() -> getString(
                            R.string.saldo_bonus_will_refund,
                            Utility.formatAmount(order.amount),
                            Utility.formatAmount(order.totalBonus)
                        )
                        PpobUtils.isBnpl(order) -> getString(
                            R.string.bnpl_will_refund,
                            Utility.formatAmount(order.amount)
                        )
                        PpobUtils.isSaldo(order) -> getString(
                            R.string.saldo_will_refund,
                            Utility.formatAmount(order.amount)
                        )
                        else -> ""
                    }
                    with(baRefundInfo){
                        visibility = refundInfo.isNotNullOrBlank().asVisibility()
                        setInfoViewStyle(
                            textColor = getColorCompat(R.color.red_80),
                            textStyle = R.style.SubHeading2,
                            backgroundType = "red_with_cross"
                        )
                        addText(refundInfo)
                        setClickAction { baRefundInfo.hideView() }
                    }
                    tvStatus.text = getString(R.string.refund_in_process)
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_processing_filled_circle,
                        0,
                        0,
                        0
                    )
                    tvStatus.background =
                        ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_yellow)
                    if (PpobUtils.isBnpl(order)) {
                        binding.includeAutoRefund.root.hideView()
                        return
                    }
                    binding.includeAutoRefund.clRefundLayout.background =
                        ContextCompat.getDrawable(
                            this@PaymentHistoryDetailsActivity,
                            R.drawable.bg_rounded_rectangle_blue_5
                        )
                    updateRefundBankInfo(refundBank)
                    includePaymentTransactionDetail.gpRefundBank.hideView()
                }

                STATUS_REFUNDED -> {
                    val refundInfo = when {
                        order.totalBonus.isNotZero() -> getString(
                            R.string.saldo_bonus_refunded,
                            Utility.formatAmount(order.amount),
                            Utility.formatAmount(order.totalBonus)
                        )
                        PpobUtils.isBnpl(order) -> getString(
                            R.string.bnpl_refunded,
                            Utility.formatAmount(order.amount)
                        )
                        PpobUtils.isSaldo(order) -> getString(
                            R.string.saldo_refunded,
                            Utility.formatAmount(order.amount)
                        )
                        else -> ""
                    }
                    with(baRefundInfo){
                        visibility = refundInfo.isNotNullOrBlank().asVisibility()
                        setInfoViewStyle(
                            textColor = getColorCompat(R.color.blue_80),
                            textStyle = R.style.SubHeading2,
                            backgroundType = "blue_with_cross"
                        )
                        addText(refundInfo)
                        setClickAction { baRefundInfo.hideView() }
                    }
                    tvStatus.text = getString(R.string.refund_successful)
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_progress_icon_checkwhite,
                        0,
                        0,
                        0
                    )
                    tvStatus.background =
                        ContextCompat.getDrawable(
                            this@PaymentHistoryDetailsActivity,
                            R.drawable.bg_status_green
                        )

                    if (PpobUtils.isBnpl(order)) {
                        includeAutoRefund.root.hideView()
                        includeRefundedSuccess.root.hideView()
                        return
                    }
                    with(includeAutoRefund) {
                        root.showView()
                        clRefundLayout.background = ContextCompat.getDrawable(
                            this@PaymentHistoryDetailsActivity,
                            R.drawable.bg_rounded_rectangle_green_5
                        )
                        tvSetRefund.text = getString(R.string.success_return)
                        tvSetRefund.setTextColor(getColorCompat(R.color.green_80))
                        tvSetReceiver.hideView()
                        includeBankLayout.root.showView()
                        includeBankLayout.tvBankName.text = refundBank?.bankCode
                        if (refundBank?.bankCode == PaymentConst.SALDO || refundBank?.bankCode == PaymentConst.BNPL) {
                            includeBankLayout.tvAccountNumber.text =
                                Utility.formatAmount(refundBank.balance)
                        } else {
                            includeBankLayout.tvAccountNumber.text = refundBank?.accountNumber
                        }
                        Glide.with(this@PaymentHistoryDetailsActivity)
                            .load(refundBank?.bankLogo)
                            .placeholder(R.drawable.ic_bank)
                            .error(R.drawable.ic_bank)
                            .into(includeBankLayout.ivBank)
                        includeBankLayout.tvEdit.hideView()
                    }
                    includeRefundedSuccess.root.showView()
                    includeRefundedSuccess.tvCashTransaction.text =
                        getString(R.string.automatically_registered)
                    // Show option to change refund bank
                    setupRefundBankChangeButton(refundBank)
                }
                STATUS_REFUNDING_FAILED -> {
                    tvStatus.text = getString(R.string.refund_failed_1)
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_progress_icon_failed,
                        0,
                        0,
                        0
                    )
                    tvStatus.background =
                        ContextCompat.getDrawable(
                            this@PaymentHistoryDetailsActivity,
                            R.drawable.bg_status_red
                        )
                    with(includeAutoRefund) {
                        root.showView()
                        if (refundBank?.isDisabled.isTrue) {
                            setRefundDisabledView(refundBank)
                            includeBankLayout.tvEdit.setSingleClickListener {
                                openRefundBanksBottomSheet()
                            }
                        } else {
                            clRefundLayout.background = ContextCompat.getDrawable(
                                this@PaymentHistoryDetailsActivity,
                                R.drawable.bg_rounded_rectangle_red_5
                            )
                            tvSetRefund.text = getString(R.string.refund_failed)
                            tvSetRefund.setTextColor(getColorCompat(R.color.red_80))
                            tvSetReceiver.text = getString(R.string.contact_manual_refund)
                            includeBankLayout.root.hideView()
                        }
                        // Show option to change refund bank
                        setupRefundBankChangeButton(refundBank)
                    }
                }
                else -> {
                    val refundInfo = when {
                        order.totalBonus.isNotZero() -> getString(
                            R.string.saldo_bonus_will_refund,
                            Utility.formatAmount(order.amount),
                            Utility.formatAmount(order.totalBonus)
                        )
                        PpobUtils.isBnpl(order) -> getString(
                            R.string.bnpl_will_refund,
                            Utility.formatAmount(order.amount)
                        )
                        PpobUtils.isSaldo(order) -> getString(
                            R.string.saldo_will_refund,
                            Utility.formatAmount(order.amount)
                        )
                        else -> ""
                    }
                    with(baRefundInfo){
                        visibility = refundInfo.isNotNullOrBlank().asVisibility()
                        setInfoViewStyle(
                            textColor = getColorCompat(R.color.red_80),
                            textStyle = R.style.SubHeading2,
                            backgroundType = "red_with_cross"
                        )
                        addText(refundInfo)
                        setClickAction { baRefundInfo.hideView() }
                    }
                    tvStatus.text = getString(R.string.failed_status)
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_progress_icon_failed,
                        0,
                        0,
                        0
                    )
                    tvStatus.background =
                        getDrawableCompat(R.drawable.bg_status_red)
                    if (PpobUtils.isBnpl(order)) {
                        binding.includeAutoRefund.root.hideView()
                        return
                    }
                    if (refundBank == null) {
                        if (!viewModel.isPaymentSaldoOut() && !viewModel.isPaymentIn() && !viewModel.isQrisPaymentIn() && !viewModel.isPaymentSaldoCashback() && !viewModel.isSaldoRefund()) includeAutoRefund.root.showView()
                        with(includeAutoRefund) {
                            tvSetRefund.text = getString(R.string.set_refund_method2)
                            tvSetRefund.setTextColor(getColorCompat(R.color.red_80))
                            tvSetReceiver.text = getString(R.string.choose_refund_method)
                            btnChooseRefundMethod.showView()
                            clRefundLayout.background =
                                getDrawableCompat(R.drawable.bg_fill_red_80_outline_solitude)
                            btnChooseRefundMethod.setSingleClickListener {
                                AppAnalytics.trackEvent(
                                    AnalyticsConst.EVENT_PAYMENT_SET_REFUND_BANK,
                                    PropBuilder().apply {
                                        put(
                                            AnalyticsConst.ENTRY_POINT,
                                            AnalyticsConst.PAYMENTS_DETAIL_FAILED_STATE
                                        )
                                        put(
                                            "payment_product",
                                            order.items?.firstOrNull()?.name
                                        )
                                        put(AnalyticsConst.TRANSACTION_ID, order.transactionId)
                                    }
                                )
                                val bookId = SessionManager.getInstance().businessId
                                val i = AddBankAccountActivity.createIntent(
                                    this@PaymentHistoryDetailsActivity,
                                    PaymentConst.TYPE_PPOB.toString(),
                                    bookId,
                                    AnalyticsConst.PAYMENT_DETAILS_CTA,
                                    hasBankAccount = "false",
                                    showTutorial = "false",
                                    amount = order.amount,
                                    product = order.items?.firstOrNull()?.name,
                                    transactionId = order.transactionId
                                )
                                startUpdateRefundBankForResult.launch(i)
                            }
                        }
                    } else {
                        if (refundBank.isDisabled.isTrue) {
                            binding.includeAutoRefund.root.showView()
                            setRefundDisabledView(refundBank)
                            binding.includeAutoRefund.includeBankLayout.tvEdit.setSingleClickListener {
                                openRefundBanksBottomSheet()
                            }
                        } else {
                            with(binding.includeAutoRefund) {
                                clRefundLayout.background =
                                    getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5)
                                tvSetRefund.text = getString(R.string.money_will_be_returned)
                                tvSetRefund.setTextColor(getColorCompat(R.color.black_80))
                                btnChooseRefundMethod.hideView()
                                tvSetReceiver.hideView()
                                includeBankLayout.root.showView()
                                includeBankLayout.tvBankName.text = refundBank?.bankCode
                                Glide.with(this@PaymentHistoryDetailsActivity)
                                    .load(refundBank?.bankLogo)
                                    .placeholder(R.drawable.ic_bank)
                                    .error(R.drawable.ic_bank)
                                    .into(includeBankLayout.ivBank)
                                if (refundBank?.bankCode != PaymentConst.SALDO) {
                                    includeBankLayout.tvAccountNumber.text = refundBank?.accountNumber
                                    includeBankLayout.tvEdit.showView()
                                    includeBankLayout.tvEdit.setSingleClickListener {
                                        openRefundBanksBottomSheet()
                                    }
                                } else {
                                    includeBankLayout.tvAccountNumber.text = Utility.formatAmount(refundBank?.balance)
                                    includeBankLayout.tvEdit.hideView()
                                }
                            }
                            updateRefundBankInfo(refundBank)
                            setupRefundBankChangeButton(refundBank)
                        }
                    }
                }
            }
        }
        if (viewModel.isQrisPaymentIn()) {
            setupFailedQris()
        }
    }

    private fun setRefundDisabledView(refundBank: RefundBankAccount?) {
        with(binding.includeAutoRefund) {
            includeBankLayout.root.showView()
            btnChooseRefundMethod.hideView()
            tvSetReceiver.hideView()
            includeBankLayout.tvBankName.text = refundBank?.bankCode
            if (refundBank?.bankCode != PaymentConst.SALDO)
                includeBankLayout.tvAccountNumber.text = refundBank?.accountNumber
            Glide.with(this@PaymentHistoryDetailsActivity)
                .load(refundBank?.bankLogo)
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(includeBankLayout.ivBank)
            tvSetRefund.text = getString(R.string.bank_disabled_for_refund)
            includeBankLayout.tvEdit.showView()
            includeBankLayout.tvEdit.typeface =
                Typeface.defaultFromStyle(Typeface.BOLD)
            with(
                ContextCompat.getColor(
                    this@PaymentHistoryDetailsActivity, R.color.red_80
                )
            ) {
                tvSetRefund.setTextColor(this)
                includeBankLayout.tvEdit.setTextColor(this)
            }
            clRefundLayout.background =
                getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
        }
    }

    private fun setExpiredLayout() {
        with(binding) {
            includePaymentStatus.tvRefresh.showView()
            includePaymentDetailTop.tvBillAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                0,
                0,
                R.drawable.ic_progress_icon_failed,
                0
            )
            tvStatus.text = getString(R.string.expired_status)
            grpSupport.hideView()
            grpSupportTop.hideView()
            if (viewModel.isPaymentSaldoIn()) {
                btnCompletePayment.showView()
                btnCompletePayment.text = getString(R.string.top_up_saldo)
                btnCompletePayment.setSingleClickListener { topupSaldoInitiated() }
            } else {
                btnCompletePayment.hideView()
            }
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_failed_gray, 0, 0, 0)
            tvStatus.background =
                ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_grey)
            if (viewModel.isPaymentIn()) {
                binding.includeWarningMid.root.showView()
                binding.includeWarningMid.tvWarning.text =
                    getString(R.string.money_in_expired_callback_info)
            }
        }
    }

    private fun topupSaldoInitiated() {
        val prop = PropBuilder().apply {
            put(AnalyticsConst.ENTRY_POINT, PAYMENT_DETAILS)
            put(AnalyticsConst.WALLET, AnalyticsConst.SALDO)
        }
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, prop)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, "payment_history_details")
        val sourceLink = SourceLink(
            context = this,
            "${SaldoSignalHandler.saldoLink}?${AnalyticsConst.ENTRY_POINT}=${AnalyticsConst.PAYMENT_DETAILS}"
        )
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_SALDO_IN)) {
                    PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.PAYMENT_DETAILS)
                } else {
                    startActivity(TopupSaldoActivity.createIntent(this))
                }
                FirebaseCrashlytics.getInstance().recordException(it)
            }
        )
    }

    private fun setCompleteLayout(order: FinproOrderResponse) {
        with(binding) {
            includePaymentStatus.tvRefresh.hideView()
            with(includePaymentDetailTop) {
                tvBillAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.ic_progress_icon_active,
                    0
                )
                tvBillAmountValue.setTextColor(getColorCompat(R.color.green_80))
                setSaldoText()
            }
            tvStatus.text = getString(R.string.payment_status_completed)
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_progress_icon_checkwhite, 0, 0, 0)
            tvStatus.background =
                ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_green)
            if (viewModel.isPaymentSaldoRedemption() || viewModel.isPaymentSaldoCashback()) {
                return
            }
            includePaymentReceipt.root.showView()
            showKirimButtonToolTip()
            grpSupport.hideView()
            btnCompletePayment.hideView()
            setupAdminFee(order)

            if (!viewModel.isPaymentPpob() && !order.agentFeeInfo?.updatedAt.isNullOrBlank()) {
                val amtFmt = String.format("%s %s", Utility.getCurrency(), Utility.formatCurrency(abs(order.amount ?: ZERO_DOUBLE)))
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_EDIT_INVOICE_SAVE, AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.ORDER_ID, order.orderId)
                        .put(AnalyticsConst.AMOUNT, amtFmt)
                        .put(AnalyticsConst.SERVICE_FEES, Utility.formatAmount(order.agentFeeInfo?.amount))
                        .put(AnalyticsConst.DESCRIPTION, order.description),
                    true, false, false
                )
                includePaymentReceipt.tvEdit.showView()
                showUpdateTimeInReceipt(order.agentFeeInfo?.updatedAt)
            }
            if (viewModel.isPaymentPpob()) {
                includePaymentReceipt.paymentReceipt.setup {
                    setFinproOrder { order }
                }
                ppobPaymentCollectionCheck(order)
                if (viewModel.isWebviewPpob()) {
                    with(order.items?.firstOrNull()?.beneficiary) {
                        btnCompletePayment.showView()
                        btnCompletePayment.text = getString(R.string.buy_again)
                        btnCompletePayment.setSingleClickListener {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_CLICK_REORDER,
                                AppAnalytics.PropBuilder()
                                    .put(
                                        AnalyticsConst.ENTRY_POINT,
                                        AnalyticsConst.PAYMENT_DETAILS
                                    ),
                                true, false, false
                            )
                            finish()
                            val beneficiary = order.items?.firstOrNull()?.beneficiary
                            PpobUtils.getPpobCategoryActivityIntent(
                                fragmentManager = supportFragmentManager,
                                context = this@PaymentHistoryDetailsActivity,
                                category = if (beneficiary?.category == CATEGORY_LISTRIK && beneficiary.code == CATEGORY_PLN_POSTPAID) {
                                    CATEGORY_LISTRIK_POSTPAID
                                } else {
                                    category
                                },
                                from = PAYMENT_DETAILS,
                                accountNumber = this?.accountNumber.orEmpty(),
                                phoneNumber = this?.phoneNumber.orEmpty(),
                                code = this?.code.orEmpty(),
                                layoutType = if (PpobConst.VEHICLE_TAX_TYPE1_BILLERS.contains(
                                        this?.code.orEmpty()
                                    )
                                ) VehicleTaxFragment.LAYOUT_TYPE_1 else VehicleTaxFragment.LAYOUT_TYPE_2,
                                machineNumber = order.items?.firstOrNull()?.details?.machineNumber.orEmpty(),
                                frameNumber = order.items?.firstOrNull()?.details?.nik.orEmpty()
                            )?.let { startActivity(it) }
                        }
                    }
                } else {
                    btnCompletePayment.hideView()
                }
            }
        }
    }

    private fun submitList() {
        binding.includePaymentStatus.rvTimeline.visibility = showStatus.asVisibility()
        adapter.setOrderStatus(
            order?.status,
            if (viewModel.isQrisPaymentIn()) PaymentConst.TYPE_QRIS.toString() else paymentType
        )
        adapter.setShowReceiptStatus(showReceipt)
        adapter.submitList(null)
        adapter.submitList(statusList)
    }

    private fun setPaidLayout(order: FinproOrderResponse, isOnHold: Boolean = false) {
        with(binding) {
            includePaymentPendingInfo.root.showView()
            includePaymentStatus.tvRefresh.showView()
            if (isOnHold) {
                tvStatus.text = getString(R.string.status_checking)
            } else {
                tvStatus.text = getString(R.string.in_process)
            }
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_processing_filled_circle, 0, 0, 0)
            includePaymentDetailTop.tvBillAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                0, 0, 0, 0
            )
            tvStatus.background =
                ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_yellow)
            if (isOnHold) grpSupport.showView() else grpSupport.hideView()
            grpSupportTop.hideView()
            btnCompletePayment.hideView()
            if (viewModel.isPaymentPpob()) {
                includePaymentReceipt.paymentReceipt.setup {
                    setFinproOrder { order }
                }
            }
            setupAdminFee(order)
            if (viewModel.isQrisPaymentIn()) {
                setNextRetryInfo(order)
                showQrisDisbursementInfo(order)
            }
        }
    }

    private fun showQrisDisbursementInfo(order: FinproOrderResponse) {
        order.payments?.firstOrNull()?.paymentMethod?.detail?.qrisScheduledDisbursementInfo?.let {
            binding.tvInfo.apply {
                text = it
                setTextColor(getColorCompat(R.color.blue_60))
                setDrawable(R.drawable.ic_time_running, 0, 0, 0)
                clearDrawableTint()
                showView()
            }
        }
    }

    private fun setupAdminFee(order: FinproOrderResponse) {
        if (!viewModel.isPaymentPpob() && order.transactionType == RECORD_IN_CASH) {
            ComponentUtil.setVisible(binding.includePaymentReceipt.tvEdit, true)
            val serviceFeeAmount = order.agentFeeInfo?.amount ?: 0.0
            binding.includePaymentReceipt.paymentReceipt.setServiceFee(serviceFeeAmount)

            val nominalAmount = if (viewModel.isPaymentIn() || viewModel.isQrisPaymentIn()) {
                order.amount.orNil.minus(order.agentFeeInfo?.amount.orNil)
            } else {
                order.amount.orNil.minus(order.fee.orNil).plus(order.agentFeeInfo?.amount.orNil)
            }
            binding.includePaymentReceipt.paymentReceipt.setNominalAmount(Utility.formatAmount(nominalAmount))
        }
    }

    private fun setDestinationBankInfo(destinationBankInformation: DestinationBankInformation?) {
        if (destinationBankInformation?.flag == BANK_DOWN_TIME && destinationBankInformation.message.isNotNullOrBlank()) {
            with(binding.baMsg) {
                showView()
                addText(destinationBankInformation.message)
                textColor(getColorCompat(R.color.black_60))
                setBackgroundType("warning")
                addTextStyle(R.style.Body3)
            }
            binding.includePaymentPendingInfo.root.hideView()
        }
    }

    private fun setPendingLayout(
        payment: FinproPayments?,
        item: PpobProductDetail?
    ) {
        with(binding) {
            setSaldoText()
            if (viewModel.isPaymentSaldoIn()) {
                includePaymentPendingInfo.root.hideView()
            } else {
                includePaymentPendingInfo.root.showView()
            }
            includePaymentStatus.tvRefresh.showView()
            if (viewModel.isPaymentIn()) {
                tvStatus.text = getString(R.string.waiting_for_payment_in_status)
            } else {
                tvStatus.text = getString(R.string.waiting_for_payment_status)
            }
            tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_processing_filled_circle, 0, 0, 0)
            tvStatus.background =
                ContextCompat.getDrawable(this@PaymentHistoryDetailsActivity, R.drawable.bg_status_yellow)
            grpSupport.showView()
            if (!viewModel.isQrisPaymentIn()) {
                btnCompletePayment.showView()
            }
            if (viewModel.isPaymentSaldoCashback()) {
                btnCompletePayment.hideView()
                includePaymentPendingInfo.root.hideView()
            }
            if (!viewModel.isPaymentIn() && !viewModel.isQrisPaymentIn() && !viewModel.isPaymentSaldoCashback() && !order?.flags?.get(UI_VARIANT_UNIVERSAL_CHECKOUT).isTrue) {
                if (payment?.paymentMethod?.groupCode == "BUKU") {
                    includePaymentMethod.root.hideView()
                } else {
                    includePaymentMethod.root.showView()
                }
                includePaymentMethod.tvDetailTransaction.text = payment?.paymentMethod?.name ?: "-"
                if (item?.fee != null && item.fee > 0.0) {
                    includePaymentMethod.tvTransactionFeeValue.text = Utility.formatAmount(item.fee)
                } else {
                    includePaymentMethod.tvTransactionFeeValue.text = getString(R.string.free).uppercase()
                    includePaymentMethod.tvTransactionFeeValue.setTextColor(getColorCompat(R.color.blue_60))
                }
                timer = object : CountDownTimer(getInvoiceExpiryTime(payment?.expiredAt), AppConst.ONE_SECOND) {
                    override fun onTick(millisUntilFinished: Long) {
                        includePaymentMethod.tvExpiredTime.text = DateTimeUtils.millisecondsToTime(millisUntilFinished)
                    }

                    override fun onFinish() {
                        btnCompletePayment.hideView()
                        showTimerExpireDialog()
                        timer?.cancel()
                    }
                }
                timer?.start()
                if (payment?.paymentMethod?.groupCode == "EWALLET" || payment?.paymentMethod?.groupCode == "QRIS" || payment?.paymentMethod?.groupCode == "RETAIL_OUTLET") {
                    val payVia =
                        getString(R.string.pay_via) + " ${payment.paymentMethod.groupName}-${payment.paymentMethod.name}"
                    includePaymentMethod.includePaymentBlock.tvBank.text = payVia
                    includePaymentMethod.includePaymentBlock.root.setSingleClickListener {
                        viewModel.onInstructionClicked()
                    }
                    includePaymentMethod.includePaymentBlock.tvBankSubtext.text = getString(R.string.bank_subtext)
                    includePaymentMethod.includePaymentBlock.ivArrow.setImageResource(R.drawable.ic_arrow_white)
                    includePaymentMethod.tvTitleInstruction.hideView()
                    includePaymentMethod.includeTwoInstruction.root.hideView()
                    includePaymentMethod.includeThreeInstruction.root.hideView()
                    includePaymentMethod.includeOneInstruction.root.showView()
                    includePaymentMethod.includeOneInstruction.tvInstruction.text = getString(R.string.payment_guide)
                    includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                    includePaymentMethod.includeOneInstruction.tvInstruction.setDrawableRightListener {
                        SurvicateAnalytics.invokeEventTracker(EVENT_TAP_PAYMENT_GUIDE, this@PaymentHistoryDetailsActivity)

                        viewModel.triggerEvent(
                            analyticsTypeValue,
                            PAYMENT_DETAILS,
                            order?.transactionId,
                            if (showInstructionOne) COLLAPSE else EXPAND,
                            eventName = EVENT_TAP_PAYMENT_GUIDE
                        )
                        if (showInstructionOne) {
                            includePaymentMethod.includeOneInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_down,
                                0
                            )
                            includePaymentMethod.includeOneInstruction.tvInstruction.text =
                                getString(R.string.payment_guide)
                            includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                            includePaymentMethod.includeOneInstruction.tvExpanded.hideView()
                        } else {
                            includePaymentMethod.includeOneInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_blue_up,
                                0
                            )
                            includePaymentMethod.includeOneInstruction.tvExpanded.showView()
                            includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.blue_60))
                            includePaymentMethod.includeOneInstruction.tvExpanded.text =
                                getText(viewModel.getEWalletPaymentMethodInstruction(payment.paymentMethod.code.orEmpty()))
                        }
                        showInstructionOne = !showInstructionOne
                        includePaymentMethod.includeOneInstruction.tvInstruction.text =
                            getString(R.string.payment_guide)
                    }

                } else {
                    val payVia = getString(R.string.pay_via) + " " + getString(R.string.label_virtual_account)
                    includePaymentMethod.includePaymentBlock.tvBank.text = payVia
                    includePaymentMethod.includePaymentBlock.ivArrow.setImageResource(R.drawable.ic_copy_white)
                    includePaymentMethod.includePaymentBlock.ivArrow.setSingleClickListener {
                        Utility.copyToClipboard(
                            payment?.paymentMethod?.virtualAccountNumber,
                            this@PaymentHistoryDetailsActivity,
                            getString(R.string.copy_text)
                        )
                    }
                    includePaymentMethod.includePaymentBlock.tvBankSubtext.text =
                        payment?.paymentMethod?.virtualAccountNumber ?: "-"
                    includePaymentMethod.tvTitleInstruction.showView()
                    includePaymentMethod.includeTwoInstruction.root.showView()
                    includePaymentMethod.includeThreeInstruction.root.showView()
                    includePaymentMethod.includeOneInstruction.root.showView()
                    includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                    includePaymentMethod.includeOneInstruction.tvInstruction.text =
                        getString(R.string.atm, payment?.paymentMethod?.code)
                    includePaymentMethod.includeOneInstruction.tvInstruction.setDrawableRightListener {
                        SurvicateAnalytics.invokeEventTracker(EVENT_TAP_PAYMENT_GUIDE, this@PaymentHistoryDetailsActivity)

                        viewModel.triggerEvent(
                            analyticsTypeValue,
                            PAYMENT_DETAILS,
                            order?.transactionId,
                            if (showInstructionOne) COLLAPSE else EXPAND,
                            eventName = EVENT_TAP_PAYMENT_GUIDE
                        )
                        if (showInstructionOne) {
                            includePaymentMethod.includeOneInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_down,
                                0
                            )
                            includePaymentMethod.includeOneInstruction.tvExpanded.hideView()
                            includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                        } else {
                            includePaymentMethod.includeOneInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_blue_up,
                                0
                            )
                            includePaymentMethod.includeOneInstruction.tvExpanded.showView()
                            includePaymentMethod.includeOneInstruction.tvInstruction.setTextColor(getColorCompat(R.color.blue_60))
                            includePaymentMethod.includeOneInstruction.tvExpanded.text =
                                payment?.paymentMethod?.code?.let { getText(viewModel.getATMPaymentMethodInstruction(it)) }
                        }
                        showInstructionOne = !showInstructionOne
                        includePaymentMethod.includeOneInstruction.tvInstruction.text =
                            getString(R.string.atm, payment?.paymentMethod?.code)
                    }
                    includePaymentMethod.includeTwoInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                    includePaymentMethod.includeTwoInstruction.tvInstruction.text =
                        getString(R.string.internet_banking, payment?.paymentMethod?.code)
                    includePaymentMethod.includeTwoInstruction.tvInstruction.setDrawableRightListener {
                        SurvicateAnalytics.invokeEventTracker(EVENT_TAP_PAYMENT_GUIDE, this@PaymentHistoryDetailsActivity)

                        viewModel.triggerEvent(
                            analyticsTypeValue,
                            PAYMENT_DETAILS,
                            order?.transactionId,
                            if (showInstructionTwo) COLLAPSE else EXPAND,
                            eventName = EVENT_TAP_PAYMENT_GUIDE
                        )
                        if (showInstructionTwo) {
                            includePaymentMethod.includeTwoInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_down,
                                0
                            )
                            includePaymentMethod.includeTwoInstruction.tvExpanded.hideView()
                            includePaymentMethod.includeTwoInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                        } else {
                            includePaymentMethod.includeTwoInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_blue_up,
                                0
                            )
                            includePaymentMethod.includeTwoInstruction.tvExpanded.showView()
                            includePaymentMethod.includeTwoInstruction.tvInstruction.setTextColor(getColorCompat(R.color.blue_60))
                            includePaymentMethod.includeTwoInstruction.tvExpanded.text =
                                payment?.paymentMethod?.code?.let {
                                    getText(
                                        viewModel.getIBankingPaymentMethodInstruction(it)
                                    )
                                }
                        }
                        showInstructionTwo = !showInstructionTwo
                        includePaymentMethod.includeTwoInstruction.tvInstruction.text =
                            getString(R.string.internet_banking, payment?.paymentMethod?.code)

                    }
                    includePaymentMethod.includeThreeInstruction.tvInstruction.text =
                        getString(R.string.m_banking, payment?.paymentMethod?.code)
                    includePaymentMethod.includeThreeInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                    includePaymentMethod.includeThreeInstruction.tvInstruction.setDrawableRightListener {
                        SurvicateAnalytics.invokeEventTracker(EVENT_TAP_PAYMENT_GUIDE, this@PaymentHistoryDetailsActivity)
                        viewModel.triggerEvent(
                            analyticsTypeValue,
                            PAYMENT_DETAILS,
                            order?.transactionId,
                            if (showInstructionThree) COLLAPSE else EXPAND,
                            eventName = EVENT_TAP_PAYMENT_GUIDE
                        )
                        if (showInstructionThree) {
                            includePaymentMethod.includeThreeInstruction.tvInstruction.setTextColor(getColorCompat(R.color.black_80))
                            includePaymentMethod.includeThreeInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_down,
                                0
                            )
                            includePaymentMethod.includeThreeInstruction.tvExpanded.hideView()
                        } else {
                            includePaymentMethod.includeThreeInstruction.tvInstruction.setCompoundDrawablesRelativeWithIntrinsicBounds(
                                0,
                                0,
                                R.drawable.ic_chevron_blue_up,
                                0
                            )
                            includePaymentMethod.includeThreeInstruction.tvExpanded.showView()
                            includePaymentMethod.includeThreeInstruction.tvInstruction.setTextColor(getColorCompat(R.color.blue_60))
                            includePaymentMethod.includeThreeInstruction.tvExpanded.text =
                                payment?.paymentMethod?.code?.let {
                                    getText(
                                        viewModel.getMBankingPaymentMethodInstruction(it)
                                    )
                                }
                        }
                        showInstructionThree = !showInstructionThree
                        includePaymentMethod.includeThreeInstruction.tvInstruction.text =
                            getString(R.string.m_banking, payment?.paymentMethod?.code)
                    }
                }
                Glide.with(this@PaymentHistoryDetailsActivity)
                    .load(payment?.paymentMethod?.logo)
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(includePaymentMethod.includePaymentBlock.ivBank)
            }
        }
    }

    private fun showUpdateTimeInReceipt(updatedAt: String?) {
        with(binding.includePaymentReceipt.tvEditInfo) {
            val dateInfo =
                DateTimeUtils.getFormattedLocalDateTimeForPayment(updatedAt)?.split(",")
            val date = dateInfo?.getOrNull(0)
            val time = dateInfo?.getOrNull(1)?.trim()
            val editInfo = getString(R.string.payment_updated_info, date, time)
            text = editInfo
            showView()
        }
    }

    private val startPinAddBankForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_ADD_REFUND_BANK,
                    PropBuilder().apply {
                        put(
                            AnalyticsConst.ENTRY_POINT,
                            if (isFromNotif) AnalyticsConst.PUSH_NOTIF else entryPointForRefundBank
                        )
                        put(
                            "entry_point_product_name",
                            order?.items?.firstOrNull()?.name
                        )
                        put(AnalyticsConst.TRANSACTION_ID, order?.transactionId)
                    }
                )
                val bookId = SessionManager.getInstance().businessId
                val i = AddBankAccountActivity.createIntent(
                    this@PaymentHistoryDetailsActivity,
                    PaymentConst.TYPE_PPOB.toString(),
                    bookId,
                    entryPointForRefundBank,
                    hasBankAccount = "true",
                    showTutorial = "false",
                    amount = order?.amount,
                    product = order?.items?.firstOrNull()?.name,
                    transactionId = order?.transactionId
                )
                startUpdateRefundBankForResult.launch(i)
            }
        }


    private val startPinSelectBankForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_SELECT_REFUND_BANK,
                    PropBuilder().apply {
                        put(
                            AnalyticsConst.ENTRY_POINT,
                            if (isFromNotif) AnalyticsConst.PUSH_NOTIF else entryPointForRefundBank
                        )
                        put(
                            AnalyticsConst.PRODUCT,
                            order?.items?.firstOrNull()?.name
                        )
                        put(AnalyticsConst.TRANSACTION_ID, order?.transactionId)
                        put(
                            AnalyticsConst.REFUND_BANK_SELECTED,
                            refundBankAccount?.bankCode
                        )
                    }
                )
                refundBankAccount?.let { viewModel.changeRefundBank(it) }
            }
        }

    private val startUpdateRefundBankForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                refreshScreen()//adding the bankaccount 1st time, will need to refresh. So updating this screen always after adding bank account
            }
        }

    private val startPaymentCategorySelectedForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getParcelableExtra<PaymentCategoryItem>(PAYMENT_CATEGORY)
                    ?.let {
                        binding.includePaymentTransactionDetail.tvPaymentCategoryValue.text =
                            it.name
                        binding.includePaymentTransactionDetail.ivChangePaymentCategory.hideView()
                        order?.paymentCategory = it
                        viewModel.updatePaymentCategory(it)
                    }
            }
        }

    private val startQrisForResult = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        refreshScreen()
    }

    private val startPaymentContactActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                Toast.makeText(
                    this@PaymentHistoryDetailsActivity,
                    result.data?.getStringExtra("message"),
                    Toast.LENGTH_LONG
                ).show()
                refreshScreen()
            }
        }

    private val startQrisBankAddForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                goToAddBank()
            }
        }

    private val startDisbursalBankAddForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                goToAddBank(true)
            }
        }

    private val startAddBankAccountActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                when {
                    viewModel.isQrisPaymentIn() -> {
                        result.data?.getParcelableExtra<BankAccount>(AddBankAccountActivity.BANK_ACCOUNT)
                            ?.let { changeQrisBank(it) }
                    }
                    viewModel.isPaymentIn() -> {
                        result.data?.getParcelableExtra<BankAccount>(AddBankAccountActivity.BANK_ACCOUNT)
                            ?.let { retryDisbursement(it) }
                    }
                    else -> {
                        binding.includePaymentCollection.root.hideView()
                        val bankAccount =
                            result.data?.getParcelableExtra(AddBankAccountActivity.BANK_ACCOUNT) as? BankAccount
                        viewModel.createInDisbursementAndShareTemplate(
                            bankAccount,
                            this@PaymentHistoryDetailsActivity
                        )// after adding bank account, directly create payment}
                    }
                }
            }
        }

    private val startPinAddBankPaymentCollectionsForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val bookId = SessionManager.getInstance().businessId
                startAddBankAccount(bookId)
            }
        }

    private fun handleQrisBankSuccess() {
        with(binding) {
            tvError.hideView()
            tvSuccess.text = getString(R.string.qris_bank_updated)
            tvSuccess.showView()
            /*
            Since QRIS transaction will have status as REJECTED, "Coba Lagi" will be hidden for
            non-retryable QRIS transactions, here we need to show the button to the user.
             */
            if (order?.amount.orNil >= RemoteConfigUtils.getPaymentConfigs().minQrisAmountForRetry.orNil) {
                btnCompletePayment.showView()
            }
        }
    }

    private fun showTimerExpireDialog() {
        if (!isFinishing) {
            val dialog = BukuDialog(
                this@PaymentHistoryDetailsActivity,
                getString(R.string.payment_expiration),
                getString(R.string.create_new_pay_to_continue),
                "",
                getString(R.string.make_new_payment),
                this@PaymentHistoryDetailsActivity,
                BukuDialog.UseCase.SINGLE_RIGHT_BUTTON
            )
            dialog.setUseFullWidth(false)
            dialog.show()
        }
    }

    private fun changeQrisBank(bankAccount: BankAccount) {
        viewModel.setQrisBankAccount(bankAccount)
    }

    private fun goToAddBank(paymentIn: Boolean = false) {
        val bankTypeQuery =
            if (viewModel.isQrisPaymentIn()) PaymentConst.QRIS_COMPATIBLE_BANKS_QUERY else null
        val bookId = SessionManager.getInstance().businessId
        val entryPoint = if (paymentIn) PAYMENT_DETAILS else AnalyticsConst.QRIS_TRANSACTION_DETAILS
        val intent = AddBankAccountActivity.createIntent(
            this, paymentType.toString(),
            bookId, entryPoint,
            customerId, true.toString(), false.toString(), paymentIn, bankType = bankTypeQuery
        )
        startAddBankAccountActivityForResult.launch(intent)
    }

    private fun updateRefundBankInfo(selectedRefundBank: RefundBankAccount?) {
        with(binding.includeAutoRefund) {
            if (!viewModel.isPaymentSaldoOut() && !viewModel.isPaymentIn()) root.showView()
            tvSetRefund.text = getString(R.string.money_will_be_returned)
            tvSetRefund.setTextColor(getColorCompat(R.color.black_80))
            btnChooseRefundMethod.hideView()
            tvSetReceiver.hideView()
            includeBankLayout.root.showView()
            when (order?.progress?.lastOrNull()?.state) {
                STATUS_REFUNDING -> {
                    includeBankLayout.tvBankName.text = selectedRefundBank?.bankCode
                    Glide.with(this@PaymentHistoryDetailsActivity)
                        .load(selectedRefundBank?.bankLogo)
                        .placeholder(R.drawable.ic_bank)
                        .error(R.drawable.ic_bank)
                        .into(includeBankLayout.ivBank)
                    if (selectedRefundBank?.bankCode != PaymentConst.SALDO) {
                        includeBankLayout.tvAccountNumber.text = selectedRefundBank?.accountNumber
                        includeBankLayout.tvEdit.showView()
                        includeBankLayout.tvEdit.setSingleClickListener {
                            openRefundBanksBottomSheet()
                        }
                    } else {
                        includeBankLayout.tvAccountNumber.text = Utility.formatAmount(selectedRefundBank?.balance)
                        includeBankLayout.tvEdit.hideView()
                    }
                }
                else -> {
                    if (!PaymentUtils.isSupportedForRefund(order?.amount, selectedRefundBank)) {
                        tvSetRefund.text = getString(R.string.bank_not_supported_for_refund)
                        includeBankLayout.tvEdit.showView()
                        includeBankLayout.tvEdit.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                        with(
                            ContextCompat.getColor(
                                this@PaymentHistoryDetailsActivity, R.color.red_80
                            )
                        ) {
                            tvSetRefund.setTextColor(this)
                            includeBankLayout.tvEdit.setTextColor(this)
                        }
                        clRefundLayout.background =
                            getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
                        includeBankLayout.tvEdit.setSingleClickListener {
                            openRefundBanksBottomSheet()
                        }
                    } else {
                        includeBankLayout.tvEdit.hideView()
                        setupRefundBankChangeButton(selectedRefundBank)
                    }
                }
            }
        }
    }

    private fun startPrinting() {
        paymentHistoryDetailViewModelViewState ?: return

        val textToPrint = if (RemoteConfigUtils.useNewOrderInvoice()) {
            binding.includePaymentReceipt.orderInvoice.getTextsForPrint()
        } else {
            // not-null assertion is ok since both objects have checked using elvis operator
            binding.includePaymentReceipt.paymentReceipt.getFormattedText()
        }

        val printer = BluetoothPrinter(this, PAYMENT)
        printingDialog = printer.printingDialog
        printer.printPrintables(textToPrint, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                printingDialog?.dismiss()
                permissionLauncher.launch(permissions)
            }
        })
    }

    private val permissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
            if (it.containsValue(false)) {
                Toast.makeText(this, R.string.location_permission_denied_message, Toast.LENGTH_SHORT)
                    .show()
            }
        }

    private fun openHelp(
        isPpob: Boolean,
        paymentAmount: Double,
        userId: String?,
        paymentId: String?,
        isFailed: Boolean,
        hasPaidStatus: Boolean,
        isPendingEta: Boolean = false
    ) {
        if (RemoteConfigUtils.showAssistPage()) {
            val prop = PropBuilder().apply {
                put(AnalyticsConst.TYPE, analyticsTypeValue)
                put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_DETAIL)
                put(AnalyticsConst.TRANSACTION_ID, paymentId)
                if (isPendingEta) put(AnalyticsConst.SOURCE, AnalyticsConst.ETA)
            }
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED,
                prop,
                false,
                false,
                false
            )

            // Note: If we have transactionDetails, that means it's a saldo out or cashback out
            // order for PPOB purchase, so we can redirect to PPOB order's assist page from here.
            viewModel.viewState.value?.transactionDetails?.let {
                val item = it.items?.firstOrNull()
                startActivity(
                    AssistPageActivity.createIntent(
                        this,
                        it,
                        item?.beneficiary?.category.orEmpty(),
                        item?.disbursableType,
                        item?.beneficiary?.name,
                        null,
                        ledgerAccountId = ledgerAccountId,
                        qrisOrderId = orderId
                    )
                )
            } ?: run {
                val item = order?.items?.firstOrNull()
                startActivity(
                    AssistPageActivity.createIntent(
                        this,
                        order,
                        paymentType,
                        item?.disbursableType,
                        if (viewModel.isQrisPaymentIn()) item?.beneficiary?.name else viewModel.getCustomerName(),
                        if (viewModel.isQrisPaymentIn()) PaymentUtils.getQrisPaymentMethod(order) else null,
                        ledgerAccountId = ledgerAccountId,
                        qrisOrderId = orderId
                    )
                )
            }
        } else {
            SurvicateAnalytics.invokeEventTracker(EVENT_CUSTOMER_SUPPORT_REQUESTED, this@PaymentHistoryDetailsActivity)
            HelpDialog(this).show()
        }
    }

    private fun continuePayment(
        url: String?,
        prop: AppAnalytics.PropBuilder?,
        paymentMethod: String?,
        customerNumber: String?
    ) {
        // Note: continuePayment is used for saldo in payments as well. This event should be
        // logged for ppob payments only.
        if (viewModel.isPaymentPpob()) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_CREATED, prop?.apply {
                prop.put("ppob_type", analyticsTypeValue)
                prop.put(AnalyticsConst.PAYMENT_STATUS, AnalyticsConst.INCOMPLETE)
                prop.put(AnalyticsConst.PPOB_STATUS, AnalyticsConst.INCOMPLETE)
                prop.put(AnalyticsConst.CUSTOMER_PHONE, customerNumber)
                prop.put("saldo_bonus_enabled", order?.totalBonus.isNotZero())
                prop.put(AnalyticsConst.NOMINAL_SALDO_BONUS_USED, order?.totalBonus.orNil)
                prop.put(
                    AnalyticsConst.ENTRY_POINT,
                    if (AppConfigManager.getInstance().getEntryPointForId(AnalyticsConst.EVENT_PPOB_CREATED)) {
                        "homepage"
                    } else {
                        ""
                    }
                )
            })
            Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_PPOB_CREATED, "payment_history_details")
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PPOB_CREATED, this@PaymentHistoryDetailsActivity)
        }

        val userAgent = if (PaymentConst.DANA == paymentMethod) BaseWebviewActivity.MOBILE_DEVICE else null
        // Add ?status=true query to the saldo payment URLs, required by the web frontend for event tracking
        if (viewModel.isPaymentSaldoIn()) {
            startActivity(WebviewActivity.createIntent(this, "$url?status=true&redirect_id=$orderId", "", userAgent))
        } else {
            startActivity(WebviewActivity.createIntent(this, url, "", userAgent))
        }
    }

    private fun populatePaymentReceipt(state: PaymentHistoryDetailViewModel.ViewState) {
        val sellingPrice = when {
            state.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> state.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            state.ppobItem?.sellingPrice.orNil > 0.0 -> state.ppobItem?.sellingPrice
            viewModel.isPaymentOut() -> state.amount.minus(state.ppobItem?.discountedFee.orNil).plus(order?.loyalty?.tierDiscount.orNil).plus(order?.loyalty?.subscriptionDiscount.orNil)
            else -> state.amount
        }
        with(binding.includePaymentReceipt) {
            if (RemoteConfigUtils.useNewOrderInvoice()) {
                orderInvoice.apply {
                    makeInvoice(
                        bookEntity = state.bookEntity,
                        orderResponse = order,
                        paymentType = paymentType,
                        customerEntity = viewModel.customer,
                        userProfileEntity = viewModel.getUserProfile(User.getUserId())
                    )
                    showView()
                }
                paymentReceipt.hideView()
                if (viewModel.isPaymentPpob()) {
                    btnShare.hideView()
                    btnPrint.hideView()
                    includePaymentReceiptShareView.apply {
                        root.showView()
                        btnShare.setSingleClickListener { shareReceipt(state.disburseId) }
                        btnPrint.setSingleClickListener { checkPrintRequirement(state.disburseId) }
                        btnSend.setSingleClickListener { openSharingOptionsBottomSheet() }
                    }
                }
            } else {
                paymentReceipt.setup {
                    setBook { state.bookEntity }
                    setCustomer { viewModel.customer }
                    setReceiverBank { state.receiverBank }
                    setDto(
                        {
                            PaymentReceiptDto(
                                paymentChannel = state.paymentChannel,
                                transactionId = state.transactionId,
                                completedStatusDate = state.completedStatusDate,
                                isPPOB = !viewModel.isPaymentIn() && !viewModel.isPaymentOut() && !viewModel.isQrisPaymentIn(),
                                amount = sellingPrice ?: 0.0,
                                paymentFreeChargeStatus = AppConfigManager.getInstance().paymentFreeChargeStatus,
                                isPaymentIn = viewModel.isPaymentIn(),
                                isQrisPaymentIn = viewModel.isQrisPaymentIn(),
                                note = state.transactionNote,
                                ppobCategory = state.ppobCategory,
                                ppobItem = state.ppobItem,
                                agentFeeInfo = state.agentFeeInfo,
                                categoryCode = state.ppobItem?.beneficiary?.code
                            )
                        }, viewModel.getUserProfile(User.getUserId())
                    )
                }
                orderInvoice.hideView()
            }
            btnPrint.setSingleClickListener { checkPrintRequirement(state.disburseId) }
            btnShare.setSingleClickListener { shareReceipt(state.disburseId) }
        }
    }

    private fun shareReceipt(disburseId: String?) {
        if (RemoteConfigUtils.useNewOrderInvoice()) {
            generateAndShareViewImage(this, "com.whatsapp", binding.includePaymentReceipt.orderInvoice, "", false, disburseId)
        } else {
            generateAndShareViewImage(this, "com.whatsapp", binding.includePaymentReceipt.paymentReceipt, "", false, disburseId)
        }
    }

    private fun generateAndShareViewImage(
        context: Context?,
        packageNm: String?,
        receiptLayout: View?,
        mobile: String?,
        useWA: Boolean,
        disburseId: String?
    ) {
        try {
            AppAnalytics.trackEvent(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, getShareOrPrintTrxAnalytics(disburseId, AnalyticsConst.CUSTOM), true, false, false)
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, this@PaymentHistoryDetailsActivity)
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> =
                ImageUtils.saveLayoutConvertedImage(receiptLayout, false)
            val shareLayoutImage =
                ShareLayoutImage(ShareUtils.getReceiptSharingText(context, "", 0, category), context, packageNm, mobile, useWA, false)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun checkPrintRequirement(disburseId: String?) {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRINT_PAYMENT_TRANSACTION, getShareOrPrintTrxAnalytics(disburseId), true, false, false)
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PRINT_PAYMENT_TRANSACTION, this)
        when (val printers: List<PrinterDataHolder>? = PrinterPrefManager(this).installedPrinters) {
            null -> {
                val dialog = OpenSetupPrinterDialog(this) {
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                        AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, PAYMENT)
                    )
                    val intent = Intent(this, SetupPrinterActivity::class.java).putExtra(
                        AnalyticsConst.ENTRY_POINT,
                        PAYMENT
                    )
                    startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                }

                dialog.show()
            }
            printers -> {
                when {
                    printers.isEmpty() -> {
                        val dialog = OpenSetupPrinterDialog(this) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, PAYMENT)
                            )
                            val intent = Intent(this, SetupPrinterActivity::class.java).putExtra(
                                AnalyticsConst.ENTRY_POINT,
                                PAYMENT
                            )
                            startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                        }

                        dialog.show()
                    }
                    printers.isNotEmpty() -> {
                        startPrinting()
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        timer?.cancel()
        printingDialog?.let {
            it.dismiss()
            printingDialog = null
        }
        handler.removeCallbacksAndMessages(null)
        Utilities.removeSpan(binding.includeAutorecordedInfo.tvCashTransaction)
        Utilities.removeSpan(binding.includeCashTransaction.tvCashTransaction)
        Utilities.removeSpan(binding.includePaymentTransactionDetail.tvRefundBankValue)
        unregisterReceiver(onDownloadComplete)
        super.onDestroy()
    }

    private fun updateServiceFeeInReceipt(agentFeeInfo: AgentFeeInfo) {
        order?.agentFeeInfo = agentFeeInfo
        binding.includePaymentReceipt.paymentReceipt.setServiceFee(agentFeeInfo.amount)

        val nominalAmount = if (viewModel.isPaymentIn() || viewModel.isQrisPaymentIn()) {
            order?.amount?.minus(agentFeeInfo.amount)
        } else {
            order?.amount?.minus(order?.fee.orNil)?.plus(agentFeeInfo.amount)
        }
        binding.includePaymentReceipt.paymentReceipt.setNominalAmount(Utility.formatAmount(nominalAmount))
        showUpdateTimeInReceipt(agentFeeInfo.updatedAt)
        serviceFee = agentFeeInfo.amount
    }

    private fun checkAndHideNotes() {
        if (viewModel.isPaymentSaldoIn() || viewModel.isPaymentSaldoOut()) {
            with(binding.includePaymentTransactionDetail) {
                vwPaymentMethod.hideView()
                tvNotes.hideView()
                tvNotesValue.hideView()
            }
        }
    }

    private fun updateTheTimerMessage(pendingTime: String) {
        binding.includePaymentPendingInfo.apply {
            pendingInfoGroup.showView()
            helpGroup.hideView()
            tvInfo.text = getString(R.string.pending_process_info, pendingTime)
        }
    }

    private fun showPendingTrxHelpOption() {
        with(binding.includePaymentPendingInfo) {
            pendingInfoGroup.hideView()
            helpGroup.showView()
            tvHelpInfo.text = getString(
                R.string.pending_trx_time_message,
                RemoteConfigUtils.getPaymentPendingTimeInMinutes().toString()
            )
            btnHelp.setSingleClickListener {
                storeInfoAndRefreshScreen(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_DETAILS_PAGE_INFO,
                    AnalyticsConst.YELLOW
                )
            }
        }
    }

    private fun showPendingTrxHelpOptionInRed() {
        isPendingTrxTimeExceed = true
        with(binding.includePaymentPendingInfo) {
            pendingInfoGroup.hideView()
            helpGroup.showView()
            tvHelpInfo.text = getString(R.string.longer_pending_trx_message)
            tvHelpInfo.setTextColor(getColorCompat(R.color.red_80))
            btnHelp.backgroundTintList = ColorStateList.valueOf(getColorCompat(R.color.red_80))
            btnHelp.setTextColor(getColorCompat(R.color.white))
            btnHelp.setSingleClickListener {
                storeInfoAndRefreshScreen(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_DETAILS_PAGE_INFO,
                    AnalyticsConst.RED
                )
            }
            clPendingTrxInfo.background = getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
        }
    }

    private fun showDialog() {
        (intent?.getStringExtra(SUCCESS))?.run {
            if (!this.toBoolean()) return@run
            if (category.isNotBlank()) {
                val stringAmount = try {
                    if (amount != null)
                        Utility.formatAmount(abs(amount!!.toDouble()))
                    else amount
                } catch (e: Exception) {
                    amount
                }
                val title = if (PaymentHistory.TYPE_PAYMENT_IN == paymentType) {
                    getString(R.string.payment_finished_title, viewModel.getCustomerName())
                } else {
                    getString(R.string.payment_finished_title, getString(R.string.you))
                }
                val dialog = PaymentFinishedDialog(this@PaymentHistoryDetailsActivity, stringAmount, title, null)
                dialog.show()
            } else {
                showPpobSuccessDialog()
            }
        }
    }

    private fun showPpobSuccessDialog() {
        val type = PpobConst.CATEGORY_ANALYTICS_MAP[(paymentType.orEmpty()).lowercase()].orEmpty()
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PAYMENT_PPOB_SUCCESS_PUSH,
            PropBuilder().put(AnalyticsConst.TYPE, type),
            false,
            false,
            false
        )
        val title = getString(R.string.popup_success_ppob_title, name ?: "")
        val subtitle = getString(R.string.popup_success_ppob_subtitle)
        val dialog = PaymentFinishedDialog(this@PaymentHistoryDetailsActivity, null, title, subtitle)
        dialog.show()
    }

    override fun onUpdateSellingPrice(sellingPrice: Long) {
        showSellingPriceUpdatedToast()
        TransactionRepository.getInstance(this).updateCashTransaction(cashTransactionId, "", sellingPrice.toDouble(), 2, "", "")
        refreshScreen()
    }

    private fun ppobPaymentCollectionCheck(order: FinproOrderResponse) {
        showAutoRecordedInfo(order)
        if (order.purchaseType.isNotNullOrEmpty() && order.purchaseType == "SALES") {
            cashTransactionId = order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.transactionId ?: ""
            val sellingPrice = when {
                order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
                order.items?.firstOrNull()?.sellingPrice != 0.0 -> order.items?.firstOrNull()?.sellingPrice
                else -> (order.amount ?: ZERO_DOUBLE) + (order.fee ?: ZERO_DOUBLE)
            } ?: 0.0

            val sellingPriceStr = Utility.formatAmount(sellingPrice)
            when (order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.status ?: "") {
                PENDING -> showPpobPaymentCollectionsPendingStatusUi(sellingPriceStr, order)
                UNPAID -> showPpobPaymentCollectionsUnpaidStatusUi(sellingPrice, order)
                PAID -> showPpobPaymentCollectionsPaidStatusUi(sellingPriceStr, order)
            }
        }
    }

    private fun showPpobPaymentCollectionsPendingStatusUi(sellingPrice: String, order: FinproOrderResponse) {
        binding.includePaymentCollection.root.hideView()
        binding.includePaymentCollectionPending.apply {
            root.showView()
            ivEdit.visibility =
                (order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount ?: order.items?.firstOrNull()?.sellingPrice ?: 0.0 <= (order.amount ?: ZERO_DOUBLE) + (order.fee ?: ZERO_DOUBLE)).asVisibility()
            tvSellingPriceValue.text = sellingPrice
            btnMarkUnpaid.setSingleClickListener {
                loadUserContactFragment()
            }
            ivEdit.setSingleClickListener {
                PpobChangeSellingPriceBottomSheet.createInstance(sellingPrice).show(
                    supportFragmentManager,
                    PpobChangeSellingPriceBottomSheet.TAG
                )
            }
        }
    }

    private fun showPpobPaymentCollectionsUnpaidStatusUi(sellingPrice: Double, order: FinproOrderResponse) {
        viewModel.getBankAccounts()
        binding.includePaymentCollection.apply {
            root.showView()
            tvSellingPriceValue.text = Utility.formatAmount(sellingPrice)
            btnMarkPaid.setSingleClickListener {
                root.hideView()
                TransactionRepository.getInstance(this@PaymentHistoryDetailsActivity)
                    .updateCashTransaction(cashTransactionId, "", null, 1, "", "")
                refreshScreen()
            }
        }
        if (order.paymentCollectionInfo?.paymentCollection == null) {
            binding.includePaymentCollection.includeSharePaymentLink.root.hideView()
            binding.includePaymentCollection.includeCreatePaymentLink.apply {
                root.showView()
                binding.includePaymentCollection.vwDivider1.showView()
                tvMobileNumber.text = viewModel.customer?.name.takeIf { name -> name.isNotNullOrBlank() }
                    ?: viewModel.customer?.phone.takeIf { phone -> phone.isNotNullOrBlank() }
                            ?: order.items?.firstOrNull()?.beneficiary?.phoneNumber ?: ""
                btnCreatePaymentLink.setSingleClickListener {
                    if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PAYMENT_IN)) {
                        PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.PAYMENT_DETAILS)
                    } else {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_PAYMENT_TRANSACTION_TAGIH_INWARDS,
                            PropBuilder().put(
                                AnalyticsConst.ENTRY_POINT,
                                AnalyticsConst.PAYMENT_DETAILS
                            )
                        )
                        if (paymentInBanks.isEmpty()) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK,
                                PropBuilder().put(
                                    AnalyticsConst.ENTRY_POINT,
                                    AnalyticsConst.PAYMENT_DETAILS
                                )
                            )
                            startAddBankAccount(SessionManager.getInstance().businessId)
                        } else {
                            val bankListBtSheet =
                                BankAccountListBottomSheetFragment.createBankAccountInstance(
                                    AnalyticsConst.PAYMENT_DETAILS,
                                    false
                                )
                            bankListBtSheet.show(supportFragmentManager, "bankListBtSheet")
                        }
                    }
                }
            }
        } else {
            binding.includePaymentCollection.includeCreatePaymentLink.root.hideView()
            binding.includePaymentCollection.includeSharePaymentLink.apply {
                root.showView()
                linkEt.setText(order.paymentCollectionInfo.paymentCollection.invoiceUrl.orEmpty())
                tvMyAccountValue.text = getString(
                    R.string.bank_code_and_account_number,
                    order.paymentCollectionInfo.paymentCollection.receiverBank?.bankCode.orEmpty(),
                    order.paymentCollectionInfo.paymentCollection.receiverBank?.accountNumber.orEmpty()
                )
                tvAccountHolderNameValue.text =
                    order.paymentCollectionInfo.paymentCollection.receiverBank?.accountHolderName
                tvMobileNumberValue.text = viewModel.customer?.name.takeIf { name -> name.isNotNullOrBlank() }
                    ?: viewModel.customer?.phone.takeIf { phone -> phone.isNotNullOrBlank() }
                            ?: order.items?.firstOrNull()?.beneficiary?.phoneNumber ?: ""
            }
            val expiredAt = order.paymentCollectionInfo.paymentCollection.expiredAt.orEmpty()
            val expiryDate = DateTimeUtils.getDateFromPaymentUTC(expiredAt)
            if (expiryDate < Date()) {
                binding.includePaymentCollection.includeSharePaymentLink.apply {
                    sharePaymentBtn.setSingleClickListener {
                        viewModel.createInDisbursementAndShareTemplate(paymentInBanks.find {
                            it.isSelected == 1
                        }, this@PaymentHistoryDetailsActivity)
                    }
                    tvPaymentExpiryInfo.text = getString(R.string.share_new_link)
                }
            } else {
                val expiryString = DateTimeUtils.getFormattedLocalDateTimeWitDayForPayment(expiredAt)
                binding.includePaymentCollection.includeSharePaymentLink.apply {
                    sharePaymentBtn.setSingleClickListener {
                        viewModel.shareRequestPaymentTemplate()
                    }
                    tvPaymentExpiryInfo.text = getString(R.string.active_link_until_new, expiryString)
                }
            }
        }
    }

    private fun showPpobPaymentCollectionsPaidStatusUi(sellingPrice: String, order: FinproOrderResponse) {
        binding.includePaymentCollection.apply {
            root.showView()
            includeCreatePaymentLink.root.hideView()
            includeSharePaymentLink.root.hideView()
            tvSellingPriceValue.text = sellingPrice
            tvUnpaidPayment.setBackgroundColor(getColorCompat(R.color.green_5))
            tvUnpaidPayment.setTextColor(getColorCompat(R.color.green_80))
            grpUnpaid.hideView()
            if (order.paymentCollectionInfo?.paymentCollection == null) {
                vwDivider1.hideView()
                tvUnpaidPayment.text = getString(R.string.payment_paid_via_cash)
            } else {
                tvUnpaidPayment.text = getString(R.string.payment_paid_via_bank)
                includePaymentComplete.apply {
                    root.showView()
                    tvMyAccountValue.text = getString(
                        R.string.bank_code_and_account_number,
                        order.paymentCollectionInfo.paymentCollection.receiverBank?.bankCode.orEmpty(),
                        order.paymentCollectionInfo.paymentCollection.receiverBank?.accountNumber.orEmpty()
                    )
                    tvAccountHolderNameValue.text =
                        order.paymentCollectionInfo.paymentCollection.receiverBank?.accountHolderName
                }
            }
        }
    }

    private fun showAutoRecordedInfo(order: FinproOrderResponse) {
        order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.transactionId?.let {
            binding.includeAutorecordedInfo.root.showView()
            binding.includeAutorecordedInfo.tvCashTransaction.text =
                Utilities.makeSectionOfTextClickable(
                    getString(R.string.cash_transaction_text),
                    getString(R.string.cash_transaction_text_bold),
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            finish()
                            val intent = CashTransactionDetailActivity.getNewIntent(
                                this@PaymentHistoryDetailsActivity,
                                it, true
                            )
                            startActivity(intent)
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.isUnderlineText = false
                            ds.color = getColorCompat(R.color.blue_60)
                        }
                    })
            binding.includeAutorecordedInfo.tvCashTransaction.movementMethod =
                LinkMovementMethod.getInstance()
        } ?: kotlin.run {
            binding.includeAutorecordedInfo.root.hideView()
        }
    }

    fun showSellingPriceUpdatedToast() {
        val view =
            layoutInflater.inflate(R.layout.layout_selling_price_updated, findViewById(R.id.tv_selling_price_updated))
        val toast = Toast(this)
        toast.view = view
        toast.setGravity(Gravity.TOP or Gravity.FILL_HORIZONTAL, 0, 0)
        toast.show()
    }

    private fun refreshScreen() {
        viewModel.init(customerId, orderId, paymentType, ledgerAccountId = ledgerAccountId)
    }

    private fun startAddBankAccount(bookId: String?) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK,
            AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION),
            false,
            false,
            false
        )
        startAddBankAccountActivityForResult.launch(
            AddBankAccountActivity.createIntent(
                this,
                PaymentConst.TYPE_PAYMENT_IN.toString(),
                bookId,
                "",
                hasBankAccount = "true",
                paymentIn = true
            )
        )
    }

    private fun loadUserContactFragment() {
        userContactFragment =
            UserContactFragment.getInstance(getString(R.string.add_customer), AppConst.CREDIT)
        supportFragmentManager.beginTransaction()
            .add(binding.contactFragmentContainer.id, userContactFragment!!).commit()
    }

    override fun onBackPressed() {
        when {
            userContactFragment != null -> removeUserFragment()
            else -> super.onBackPressed()
        }
    }

    private fun removeUserFragment() {
        if (userContactFragment != null) {
            supportFragmentManager.beginTransaction().remove(userContactFragment!!).commit()
            userContactFragment = null
        }
    }

    override fun onCustomerSelected(contact: Contact?, contactSource: String) {
        NotificationUtils.alertDarkToastOnTop(getString(R.string.mobile_added_message), this)
        removeUserFragment()
        contact?.run {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER)
            binding.includePaymentCollection.includeCreatePaymentLink.tvMobileNumber.text =
                if (mobile.isNotNullOrBlank()) mobile else name
            binding.includePaymentCollectionPending.root.hideView()
            // contact may have null customerId so we create an entry in customers table if required and then we proceed.
            if (this.customerId.isNullOrBlank()) {
                this.customerId = viewModel.createContact(this.name, this.mobile)
            }
            TransactionRepository.getInstance(this@PaymentHistoryDetailsActivity).updateCashTransaction(cashTransactionId, this.mobile, null, 0, this.name, this.customerId)
            viewModel.onContactSelected(this)
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_SELECT_CUSTOMER,
                PropBuilder()
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_DETAILS)
            )
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet =
            PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, "PaymentDownBottomSheet")
    }

    private fun openRefundBanksBottomSheet(entryPoint: String = PAYMENT_DETAILS) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PAYMENT_EDIT_REFUND_BANK,
            PropBuilder().apply {
                put(
                    AnalyticsConst.ENTRY_POINT,
                    if (isFromNotif) AnalyticsConst.PUSH_NOTIF else entryPoint
                )
                put(AnalyticsConst.PRODUCT, order?.items?.firstOrNull()?.name)
                put(AnalyticsConst.TRANSACTION_ID, order?.transactionId)
            }
        )
        val bankListBtSheet = BankAccountListBottomSheetFragment.createBankAccountInstance(
            entryPoint,
            true,
            order?.amount
        )

        bankListBtSheet.show(supportFragmentManager, "refundBankListBtSheet")
    }

    private fun setupRefundBankChangeButton(refundBank: RefundBankAccount?) {
        if (viewModel.isPaymentSaldoOut() || viewModel.isPaymentIn() || viewModel.isPaymentOutSaldo()) return
        with(binding.includePaymentTransactionDetail) {
            if (refundBank == null) {
                gpRefundBank.hideView()
            } else {
                gpRefundBank.showView()

                if (refundBank.bankCode == PaymentConst.SALDO) {
                    tvRefundBankValue.text = refundBank.bankCode
                } else {
                    val refundBankInfo =
                        "${refundBank.bankCode} - ${getString(R.string.change)}\n${refundBank.accountHolderName}"
                    tvRefundBankValue.text = Utilities.makeSectionOfTextClickable(
                        refundBankInfo,
                        getString(R.string.change),
                        object : ClickableSpan() {
                            override fun onClick(widget: View) {
                                openRefundBanksBottomSheet(AnalyticsConst.REFUND_CTA)
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                super.updateDrawState(ds)
                                ds.isUnderlineText = false
                                ds.color = getColorCompat(R.color.colorPrimary)
                            }
                        })
                    tvRefundBankValue.movementMethod = LinkMovementMethod.getInstance()
                }
            }
        }
    }

    private fun setupFailedQris() {
        with(binding) {
            // Hide refund bank account in "Detail Transaksi"
            includePaymentTransactionDetail.gpRefundBank.hideView()
            includeAutoRefund.root.hideView()
            tvError.hideView()
            tvErrorAction.hideView()

            /*
             Since QRIS transactions will be batch settled, user can only try failed transaction
             once before the batch is processed, once user marks transaction for retry,
             they can retry again once the subsequent batch settlement is done.
             One user retries, status of QRIS transaction will change, so we can always show
             btnCompletePayment.
             */
            if (order?.amount.orNil >= RemoteConfigUtils.getPaymentConfigs().minQrisAmountForRetry.orNil) {
                binding.btnCompletePayment.showView()
            }
            /*
             https://bukuwarung.atlassian.net/browse/PC-2249
             Show Hubungi CS button only when max retry attempts are exhausted.
             */
            val attempts = order?.payments?.firstOrNull()?.paymentMethod?.detail?.retryAttempts.orNil
            if (attempts >= RemoteConfigUtils.getQrisRetryConfig().maxAttempts) {
                grpSupportTop.showView()
            } else {
                grpSupport.hideView()
                grpSupportTop.hideView()
            }
        }
    }

    /**
     * We show next retry info only when transaction is failed at least once and user has retried
     * and changed the status to PAID. For this we check for disbursement_retry_number
     */
    private fun setNextRetryInfo(order: FinproOrderResponse) {
        val attempts = order.payments?.firstOrNull()?.paymentMethod?.detail?.retryAttempts
        if (attempts.orNil == 0) return
        val serverTS = order.payments?.firstOrNull()?.paymentMethod?.detail?.currentTimestamp
        val disbursementTime = RemoteConfigUtils.getPaymentConfigs().qrisBatchDisbursementTime
        with(binding) {
            includePaymentPendingInfo.helpGroup.hideView()
            Utilities.safeLet(serverTS, disbursementTime)
            { serverTS, disbursementTime ->
                val nextRetryTime =
                    PaymentUtils.getNextRetryAvailableTime(serverTS, disbursementTime)
                tvInfo.showView()
                tvInfo.text =
                    getString(R.string.please_try_again_after, disbursementTime, nextRetryTime)
                tvInfo.setDrawableRightListener { tvInfo.hideView() }
            } ?: run { tvInfo.hideView() }
        }
    }

    private fun retryDisbursement(bankAccount: BankAccount) {
        binding.tvInfo.apply {
            text = getString(R.string.refund_process_success)
            showView()
            setDrawableRightListener { this.hideView() }
        }
        viewModel.retryDisbursal(bankAccount)
    }

    override fun onBankAccountSelected(bankAccount: BankAccount?) {
        when {
            viewModel.isQrisPaymentIn() -> {
                // handling for qris transactions only
                bankAccount?.let { changeQrisBank(it) }
            }
            viewModel.isPaymentIn() -> {
                // Handling for disbursal bank account
                bankAccount?.let { retryDisbursement(it) }
            }
            else -> { // handling for ppob payment collections
                viewModel.createInDisbursementAndShareTemplate(bankAccount, this@PaymentHistoryDetailsActivity)// after adding bank account, directly create payment}
            }
        }
    }

    override fun addNewBankAccount() {
        when {
            viewModel.isQrisPaymentIn() -> {
                // handling for qris transactions only
                startQrisBankAddForResult.launch(
                    NewPaymentPinActivity.createIntent(
                        this,
                        PinType.PIN_CONFIRM.toString()
                    )
                )
            }
            viewModel.isPaymentIn() -> {
                // Handling for disbursal bank account
                startDisbursalBankAddForResult.launch(
                    NewPaymentPinActivity.createIntent(
                        this,
                        PinType.PIN_CONFIRM.toString()
                    )
                )
            }
            else -> { // handling for ppob payment collections
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK,
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION),
                    false,
                    false,
                    false
                )
                startPinAddBankPaymentCollectionsForResult.launch(
                    NewPaymentPinActivity.createIntent(
                        this,
                        PinType.PIN_CONFIRM.toString()
                    )
                )
            }
        }
    }

    private fun loadUserContactActivity() {
        startPaymentContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                this,
                orderId,
                AnalyticsConst.PAYMENT_DETAILS
            )
        )
    }

    private fun showDialog(customerProfile: CustomerProfile?) {
        GenericConfirmationDialog.create(this) {
            titleRes = R.string.remove_favourite
            bodyRes = R.string.remove_favourite_subtitle
            btnLeftRes = R.string.delete
            btnRightRes = R.string.batal
            rightBtnCallback = { }
            leftBtnCallback = {
                viewModel.removeFavourite(customerProfile, this@PaymentHistoryDetailsActivity)
            }
        }.show()
    }

    private fun showRecentsAndFavourites() {
        val destinationBank = order?.items?.firstOrNull()?.beneficiary
        with(binding) {
            if (order?.customerProfile?.isFavorite == true || destinationBank?.flag?.favourite.isTrue) {
                includeFavourite.root.hideView()
                if (viewModel.isPaymentOut()) {
                    includePaymentDetailTop.tvCustomerAccountValue.setDrawable(right = R.drawable.ic_favourite_fill)
                    includePaymentDetailTop.tvCustomerAccountValue.setDrawableRightListener {
                        viewModel.deleteFavourite(destinationBank?.id.orEmpty())
                    }
                } else {
                    includePaymentDetailTop.tvFavourite.showView()
                    includePaymentDetailTop.tvFavouriteValue.showView()
                    includePaymentDetailTop.tvFavouriteValue.setSingleClickListener {
                        showDialog(order?.customerProfile)
                    }
                    includePaymentDetailTop.tvFavouriteValue.text =
                        order?.customerProfile?.favouriteDetails?.alias
                }
            } else {
                if (order?.paymentCategory?.name.equals("Komisi Agen", true)) {
                    includeFavourite.root.hideView()
                } else {
                    includeFavourite.root.showView()
                }
                includeFavourite.root.setSingleClickListener {
                    if (viewModel.isPaymentOut()) {
                        viewModel.addFavourite(destinationBank?.id.orEmpty())
                    } else {
                        loadUserContactActivity()
                    }
                }
                includePaymentDetailTop.tvFavourite.hideView()
                includePaymentDetailTop.tvFavouriteValue.hideView()
                includePaymentDetailTop.tvCustomerAccountValue.setDrawable(right = 0)
            }
        }
    }

    private fun loaderView(isLoading: Boolean) {
        binding.pbProgress.visibility = isLoading.asVisibility()
        binding.svView.visibility = (!isLoading).asVisibility()
        binding.btnCompletePayment.isEnabled = !isLoading
        if (isLoading) {
            binding.btnCompletePayment.hideView()
        }
    }

    override fun leftBukuDialogBtnListener(useCase: BukuDialog.UseCase, requestCode: Int) {

    }

    override fun rightBukuDialogBtnListener(useCase: BukuDialog.UseCase, requestCode: Int) {
        finish()
    }

    private fun showAddFavDialog() {
        val dialog = AddFavouriteDialog(this, {
            if (it) {
                loadUserContactActivity()
                trackAddFavDialogAnalytics(AnalyticsConst.SAVE_FAVOURITE)
            } else {
                viewModel.disableFavFlagOnMerchantLevel()
                trackAddFavDialogAnalytics(AnalyticsConst.NEVER_SHOW_AGAIN)
            }
        }, {
            viewModel.disableFavFlagOnTransactionLevel()
            trackAddFavDialogAnalytics(AnalyticsConst.DISMISS)
        }
        )
        dialog.show()
    }

    private fun checkStatusAndRefreshScreen() {
        checkTrxInfo = false
        val prop = PropBuilder().apply {
            put(AnalyticsConst.TYPE, analyticsTypeValue)
            put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_DETAIL)
            put(AnalyticsConst.TRANSACTION_ID, order?.transactionId ?: "")
            if (refreshScreenState.isNotBlank()) put(AnalyticsConst.STATE, refreshScreenState)
        }
        if (previousStatus == STATUS_PAID && previousStatus == order?.status ?: "") {
            showPendingTrxInfoBottomSheet()
            prop.put(AnalyticsConst.PENDING_TIME, viewModel.getTrxOnPendingTimeInMins())
        } else {
            NotificationUtils.alertToast(getString(R.string.status_updated_message))
        }
        AppAnalytics.trackEvent(
            refreshScreenEventName,
            prop,
            false,
            false,
            false
        )
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            refreshScreen()
        }

        override fun messageClicked() {}

    }

    override fun showAssistScreen() = viewModel.onHelpClicked()

    private fun showPendingTrxInfoBottomSheet() =
        PaymentPendingInfoBottomsheet.createInstance(isPendingTrxTimeExceed)
            .show(supportFragmentManager, "payment_pending_trx_info")

    private fun trackAddFavDialogAnalytics(action: String) {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FAVOURITES_POPUP_ACTION, PropBuilder().put(AnalyticsConst.ACTION, action), false, false, false)
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    override fun openPlatformFeeAndCashbackInfo() {
        startActivity(
            WebviewActivity.createIntent(this, PaymentConst.ABOUT_PAYMENT_CHARGING_URL, "")
        )
    }

    private fun redirect(redirection: String) {
        val sourceLink = SourceLink(context = this, link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "${AppConst.DEEPLINK_INTERNAL_URL}?type=act&data=$redirection&from=home&launch=1"
        val sourceLink = SourceLink(context = this, link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun openSharingOptionsBottomSheet() {
        val phoneNumber = order?.items?.firstOrNull()?.beneficiary?.phoneNumber.orEmpty()
        PpobSendMessageBottomSheet.createInstance(phoneNumber)
            .show(supportFragmentManager, PpobSendMessageBottomSheet.TAG)
    }

    override fun shareOnWA(phoneNumber: String) {
        val disburseId = order?.payments?.firstOrNull()?.paymentId
        AppAnalytics.trackEvent(
            AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT,
            getShareOrPrintTrxAnalytics(disburseId, AnalyticsConst.ANDROID, AnalyticsConst.WHATSAPP),
            true,
            false,
            false
        )
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, this@PaymentHistoryDetailsActivity)
        ShareUtilities.sendMessageOnWhatsapp(
            context = this@PaymentHistoryDetailsActivity,
            message = PpobUtils.getPpobSuccessMessage(this@PaymentHistoryDetailsActivity, order),
            phoneNumber = phoneNumber
        )
    }

    override fun shareOnSms(phoneNumber: String) {
        val disburseId = order?.payments?.firstOrNull()?.paymentId
        AppAnalytics.trackEvent(
            AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT,
            getShareOrPrintTrxAnalytics(disburseId, AnalyticsConst.ANDROID, AnalyticsConst.MESSAGE),
            true,
            false,
            false
        )
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, this@PaymentHistoryDetailsActivity)
        ShareUtilities.shareOnSms(
            context = this@PaymentHistoryDetailsActivity,
            phoneNumber = phoneNumber,
            message = PpobUtils.getPpobSuccessMessage(this@PaymentHistoryDetailsActivity, order)
        )
    }

    private fun showKirimButtonToolTip() {
        if (viewModel.isPaymentPpob()) {
            binding.svView.smoothScrollTo(0, binding.includePaymentStatus.root.top)
            handler.postDelayed({
                Utilities.showTooltip(
                    context = this@PaymentHistoryDetailsActivity,
                    anchor = binding.includePaymentReceipt.includePaymentReceiptShareView.btnSend,
                    text = getString(R.string.sms_stop_message),
                    gravity = Gravity.START,
                    width = 500f
                )
            }, 1000)
        }
    }

    private fun getShareOrPrintTrxAnalytics(disburseId: String?, drawer: String? = null, shareMedium: String? = null): PropBuilder{
        val bookEntity: BookEntity =
            BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
        return AppAnalytics.PropBuilder()
            .put(AnalyticsConst.PRODUCT_TYPE, analyticsTypeValue)
            .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.PAYMENT_DETAIL)
            .put(AnalyticsConst.PAYMENT_DISBURSABLE_ID, disburseId)
            .put(AnalyticsConst.AMOUNT, paymentHistoryDetailViewModelViewState?.amount)
            .put(AnalyticsConst.DISCOUNT, paymentHistoryDetailViewModelViewState?.discount)
            .put("cogs_modal_price", paymentHistoryDetailViewModelViewState?.buyingPrice)
            .put(AnalyticsConst.BUSINESS_NAME, bookEntity.businessName)
            .put(AnalyticsConst.ORDER_ID, paymentHistoryDetailViewModelViewState?.orderId)
            .put(AnalyticsConst.DRAWER, drawer)
            .put(AnalyticsConst.SHARE_MEDIUM, shareMedium)
    }

}