
package com.bukuwarung.payments.checkout

import android.app.ActionBar
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.view.WindowManager
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.profile.businessprofile.CreateBusinessProfileActivity
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.main_referral.ReferralSharingReceiver
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.base_android.extensions.shareText
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.BANK_DOWN_TIME
import com.bukuwarung.constants.PaymentConst.SALDO
import com.bukuwarung.constants.PaymentConst.TYPE_PAY_OUT
import com.bukuwarung.contact.ui.ContactSearchResultsFragment
import com.bukuwarung.contact.ui.CustomerSearchUseCase
import com.bukuwarung.contact.ui.UserContactFragment
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.UrlType
import com.bukuwarung.databinding.ActivityPaymentCheckoutBinding

import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.payments.bottomsheet.BankAccountListBottomSheetFragment
import com.bukuwarung.payments.bottomsheet.LoyaltyTierDiscountsBottomSheet
import com.bukuwarung.payments.bottomsheet.PaymentLimitsBottomSheet
import com.bukuwarung.payments.bottomsheet.PlatformFeeBottomSheet
import com.bukuwarung.payments.constants.KycTier
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.data.model.DestinationBankInformation
import com.bukuwarung.payments.data.model.FinproPaymentMethod
import com.bukuwarung.payments.data.model.LoyaltyDiscount
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.payments.data.model.PaymentMethod
import com.bukuwarung.payments.deeplink.handler.PaymentHistoryDetailsSignalHandler
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.ppob.confirmation.view.PaymentMethodBottomSheet
import com.bukuwarung.payments.ppob.confirmation.viewmodel.PaymentMethodViewModel
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.saldo.TopupSaldoActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.ui.BukuDialog
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.SharingUtilReceiver
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.isNotZero
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setDrawableRightListener
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.setStyleButtonColorFill
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.toBoolean
import com.bumptech.glide.Glide
import com.google.android.material.chip.Chip
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.snackbar.SnackbarContentLayout
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.math.BigDecimal
import java.util.Locale
import javax.inject.Inject
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentCheckoutActivity : BaseActivity(),
    OnboardingWidget.OnboardingWidgetListener,
    ContactSearchResultsFragment.OnCustomerSelectedCallback,
    PaymentDownBottomSheet.PaymentDownBsListener,
    BankAccountListBottomSheetFragment.BtSheetBankAccountListener,
    PaymentLimitsBottomSheet.Callback, PlatformFeeBottomSheet.Callback, BukuDialog.Callback, PaymentMethodBottomSheet.PpobBankAccountsBsListener,
    Navigator {

    // region inject dependencies
    private val viewModel: PaymentCheckoutViewModel by viewModels()
    @Inject
    lateinit var neuro: Neuro
    // endregion
    private val paymentMethodViewModel: PaymentMethodViewModel by viewModels()
    private val customerBalance by lazy { intent?.getDoubleExtra(CUSTOMER_BALANCE, 0.0) }
    // region static members
    companion object {
        private const val discountFee = "{discount_fee}"
        private const val isRedirectFromPaymentsToProfile = "isRedirectFromPaymentsToProfile"
        const val PAYMENT_TYPE = "paymentType"
        private const val CUSTOMER_ID = "customerId"
        const val ENTRY_POINT = "entryPoint"
        const val FROM = "from"
        private const val BOOK_ID = "bookId"
        private const val PIN_RC = 98
        private const val PIN_RC_WITH_TUTORIAL = 97
        private const val START_BUSINESS_PROFILE_RC = 96
        private const val SELECT_BANK_ACCOUNT_RC = 95
        private const val PAYMENT_CATEGORY_SELECTED = 94
        private const val REQUEST_BUSINESS_NAME_CHANGE = 93
        private const val RC_BOOK_NAME_CHANGE = 92
        private const val RC_CONTINUE_WITH_WARNING = 91
        const val PAYMENT_CATEGORY = "payment_category"
        private const val CUSTOMER_BALANCE = "customerBalance"

        fun createIntent(
            context: Context, paymentType: String,
            customerId: String? = null, entryPoint: String, bookId: String? = null, from: String? = null,
            category: String? = null, customerBalance: Double? = null
        ): Intent {
            val i = Intent(context, PaymentCheckoutActivity::class.java)
            i.putExtra(PAYMENT_TYPE, paymentType)
            i.putExtra(CUSTOMER_ID, customerId)
            i.putExtra(ENTRY_POINT, entryPoint)
            i.putExtra(BOOK_ID, bookId)
            i.putExtra(FROM, from)
            i.putExtra(PAYMENT_CATEGORY, category)
            i.putExtra(CUSTOMER_BALANCE, customerBalance)
            return i
        }
    }
    // endregion

    // region private members
    private val scope = MainScope()
    private var from: String? = null
    private lateinit var binding: ActivityPaymentCheckoutBinding
    private var onboardingWidget: OnboardingWidget? = null
    private var isInputBankCoachmarkShown = false
    private var userContactFragment: UserContactFragment? = null
    private var entryPoint = ""
    private var transactionType = AppConst.CASH
    private val bookId by lazy { intent?.getStringExtra(BOOK_ID) }
    private val preSelectedCategory by lazy { intent?.getStringExtra(PAYMENT_CATEGORY) }
    private var paymentDownBottomSheet: PaymentDownBottomSheet? = null
    private var selectedPaymentMethod: PaymentMethod? = null
    private var hideEditButton: Boolean = false
    // endregion

    // region extended overridden methods
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_BUSINESS_NAME_CHANGE
            && resultCode != Activity.RESULT_OK
        ) {
            viewModel.runValidations()
        }
        if (resultCode != Activity.RESULT_OK && requestCode == START_BUSINESS_PROFILE_RC) finish()
        if (requestCode == PAYMENT_CATEGORY_SELECTED) {
            data?.getParcelableExtra<PaymentCategoryItem>(PAYMENT_CATEGORY)?.let {
                viewModel.setPaymentCategory(it)
                binding.tvCategoryErrorMessage.hideView()
            }
            viewModel.getSelectedCategory()?.let { setSelectedCategory(it) }
        }
        if (resultCode != Activity.RESULT_OK) return
        when (requestCode) {
            PIN_RC -> goToAddBankAccount(false)
            PIN_RC_WITH_TUTORIAL -> goToAddBankAccount(true)
            START_BUSINESS_PROFILE_RC -> viewModel.checkProfileCompletion()
            SELECT_BANK_ACCOUNT_RC -> viewModel.setSelectedBankAccount(
                data?.getParcelableExtra(BankAccountListActivity.BANK_ACCOUNT) as? BankAccount
            )
        }
    }

    override fun onBackPressed() {
        if (userContactFragment != null) {
            removeUserFragment()
        } else if (onboardingWidget != null && onboardingWidget!!.isShown) {
            onboardingWidget!!.dismiss(
                isFromButton = false, isFromCloseButton = false, isFromOutside = true
            )
        } else {
            viewModel.onBackPressed()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                InputUtils.hideKeyboard(this)
                onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun setViewBinding() {
        binding = ActivityPaymentCheckoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        binding.rgTrxType.visibility = RemoteConfigUtils.showOldPaymentOutUi().asVisibility()
        val isFromNotif = intent.getStringExtra(AppConst.IS_FROM_NOTIF).toBoolean()
        entryPoint = if (isFromNotif) AnalyticsConst.PUSH_NOTIF
        else intent?.getStringExtra(ENTRY_POINT) ?: ""
        if (entryPoint.isBlank() || entryPoint == "customer" || entryPoint == AnalyticsConst.PUSH_NOTIF)
            transactionType = AppConst.DEBT
        if (isFromNotif && intent.getStringExtra(AppConst.PN_ID) != null) {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_NOTIFICATION_CLICKED,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.ID, intent.getStringExtra(AppConst.PN_ID))
            )
        } else if (intent.getStringExtra(ENTRY_POINT) == AnalyticsConst.PUSH_NOTIF && intent.getStringExtra(
                PAYMENT_TYPE
            ) == PaymentConst.TYPE_PAYMENT_OUT.toString()
        ) {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_NOTIFICATION_CLICKED,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.ID, AnalyticsConst.PENDING_PAYMENT_OUT)
            )
        }
        if (intent.hasExtra(FROM)) {
            from = intent.getStringExtra(FROM)
            AppConfigManager.getInstance()
                .setEntryPointForId(AnalyticsConst.HomePage.HOMEPAGE, true);
        } else {
            AppConfigManager.getInstance()
                .setEntryPointForId(AnalyticsConst.HomePage.HOMEPAGE, false);
        }

        val paymentTypeString = intent.getStringExtra(PAYMENT_TYPE)
        val customerId = intent?.getStringExtra(CUSTOMER_ID)
        val paymentType = paymentTypeString?.toInt()
            ?: intent.getIntExtra(PAYMENT_TYPE, PaymentConst.TYPE_PAYMENT_IN)
        FeaturePrefManager.getInstance().setCountPaymentPushNotification(paymentType, 1)
        viewModel.init(
            paymentType, entryPoint, transactionType, customerId, bookId, from,
            preSelectedCategory
        )

        if (paymentType == PaymentConst.TYPE_PAYMENT_IN) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_TAGIH_INWARDS)
        } else {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_BAYAR_OUTWARDS)
        }
        setupToolbar()
        reinitView(false)
        initListener()
        binding.tvAdminFeeTitleTxt.setOnClickListener {
            PlatformFeeBottomSheet.createInstance().show(supportFragmentManager, PlatformFeeBottomSheet.TAG)
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_FEE_INFO_ICON_CLICK,
                PropBuilder().put(
                    AnalyticsConst.ENTRY_POINT,
                    if (viewModel.isPaymentIn()) AnalyticsConst.PAYMENT_REQUEST else AnalyticsConst.PAYMENT_SEND
                )
            )
        }
        binding.includePaymentCategories.chipAllCategories.setOnClickListener {
            binding.includePaymentCategories.chipAllCategories.isChecked = false
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CLICK_PAYMENT_CATEGORY_FIELD,
                PropBuilder()
                    .put(
                        AnalyticsConst.TYPE,
                        if (viewModel.getPaymentType() == PaymentConst.TYPE_PAYMENT_OUT) AnalyticsConst.PAYMENT_OUT else AnalyticsConst.PAYMENT_IN
                    )
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN)
            )
            startActivityForResult(
                PaymentCategoryActivity.createIntent(
                    this@PaymentCheckoutActivity,
                    if (viewModel.getPaymentType() == PaymentConst.TYPE_PAYMENT_OUT) PaymentConst.DisbursementRequest else PaymentConst.PaymentRequest,
                    viewModel.getSelectedCategory()?.paymentCategoryId
                ), PAYMENT_CATEGORY_SELECTED
            )
            InputUtils.hideKeyboard(this)
        }
        binding.includePaymentMethod.progressBar.hideView()
        binding.includePaymentMethod.btnCompletePayment.isEnabled = false
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentCheckoutViewModel.Event.ShowInitialData -> initView(it.noteList)
                is PaymentCheckoutViewModel.Event.ShowSummaryData -> handleSummaryData(it.formattedAmountReceived, it.formattedTotal, it.destinationBankInformation)
                is PaymentCheckoutViewModel.Event.ApiError -> handleApiError(
                    it.isServerError, it.message, it.errors
                )
                is PaymentCheckoutViewModel.Event.OnSubmitPaymentInSuccess -> handlePaymentInSuccess(
                    it.template
                )
                is PaymentCheckoutViewModel.Event.OpenWebView -> handlePaymentOutSuccess(
                    it.url, it.paymentTabEnabled
                )
                is PaymentCheckoutViewModel.Event.OpenPaymentDetail -> handlePaymentHistoryDetail(
                    it.orderId, it.customerId
                )
                is PaymentCheckoutViewModel.Event.RedirectToMainActivity -> redirectToMainActivity(
                    it.paymentTabEnabled
                )
                is PaymentCheckoutViewModel.Event.BookValidationError -> showBookValidationError(it.bookName)
                PaymentCheckoutViewModel.Event.OnNormalBackPressed -> showExitDialog()
                PaymentCheckoutViewModel.Event.ShowSnackbarError -> showSnackBar()
                PaymentCheckoutViewModel.Event.PaymentTypeChanged -> reinitView(true)
                is PaymentCheckoutViewModel.Event.ShowCategories -> showCategories(it.categories)
                is PaymentCheckoutViewModel.Event.SetSelectedCategory -> setSelectedCategory(it.category)
                PaymentCheckoutViewModel.Event.PaymentLimitsReceived -> {
                    if (binding.etInputNominal.getDigitsString().isNotNullOrEmpty()) {
                        val amount = binding.etInputNominal.getNumberValue()
                        if (viewModel.isAboveThreshold(amount)) {
                            performMaxLimitChecks(amount)
                        }
                    }
                }
            }
        }
        viewModel.observePaymentMethodEvent.observe(this) {
            when (it) {
                is PaymentCheckoutViewModel.PaymentMethodEvent.ShowVaBankCodeData -> handlePaymentMethodData(
                    it.paymentMethod
                )
            }
        }
        viewModel.profileIncompleteEvent.observe(this) {
            when (it) {
                is PaymentCheckoutViewModel.ProfileIncompleteEvent.ShowProfileDialog -> showProfileDialog()
            }
        }

        viewModel.viewState.observe(this) {
            if (it.showLoading || it.showError || it.showBankLoading) {
                if (it.showLoading) {
                    binding.loadingOverlay.root.showView()
                    return@observe
                }
                binding.loadingOverlay.root.hideView()
                if (it.showBankLoading) {
                    binding.shimmerLayout.showView()
                    binding.addBankLayout.hideView()
                    binding.bankAccountLayout.hideView()
                    return@observe
                }
            }
            binding.shimmerLayout.hideView()
            binding.loadingOverlay.root.hideView()
            handlePartialVisibility(it.customerName, viewModel.getSelectedBankAccount())
            if (!it.hasShownBankListCoachmark && binding.addBankLayout.visibility == View.VISIBLE) {
                showInputBankAccountCoachmark()
            }
            if (it.showNominalError) {
                val amount = binding.etInputNominal.getNumberValue()
                if (viewModel.isBelowThreshold(amount)) {
                    binding.tvNominalErrorTxt.showView()
                    binding.tvNominalErrorTxt.text = getString(
                        R.string.error_minimum_payment_limit,
                        Utility.formatAmount(viewModel.viewState.value?.minimumAmountThreshold)
                    )
                    binding.vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
                    binding.tvKycUpgradeInfo.hideView()
                } else {
                    performMaxLimitChecks(amount)
                }
            } else {
                binding.tvKycUpgradeInfo.hideView()
                binding.tvNominalErrorTxt.hideView()
                binding.vwDivider.setBackgroundColor(getColorCompat(R.color.black_10))
            }
            if (it.customerName.isNullOrBlank() && viewModel.isPaymentIn() || viewModel.getSelectedBankAccount() == null) return@observe
            when (it.healthState) {
                PaymentCheckoutViewModel.HEALTH_OK -> {
                    binding.warningLayout.hideView()
                }
                PaymentCheckoutViewModel.HEALTH_WARNING -> {
                    binding.warningLayout.showView()
                    binding.ivWarningImg.setImageResource(R.drawable.ic_warning_yellow)
                    binding.warningLayout.background =
                        getDrawableCompat(R.drawable.bg_warning_yellow)
                    binding.tvWarningTxt.text =
                        getString(R.string.payment_outside_operational_msg)
                }
                PaymentCheckoutViewModel.HEALTH_ERROR -> {
                    binding.warningLayout.showView()
                    binding.warningLayout.background =
                        getDrawableCompat(R.drawable.bg_warning_red)
                    binding.ivWarningImg.setImageResource(R.drawable.ic_warning_red)
                    binding.tvWarningTxt.text = it.healthMessage
                }
            }
            if (!viewModel.isPaymentIn()) {
                if (it.hasPaymentMethod) {
                    binding.includePaymentMethod.gpChangePaymentMethod.showView()
                    binding.includePaymentMethod.root.showView()
                    binding.includePaymentMethod.includeDefaultPaymentMethod.root.hideView()
                } else {
                    binding.includePaymentMethod.gpChangePaymentMethod.hideView()
                    binding.includePaymentMethod.root.showView()
                    binding.includePaymentMethod.includeDefaultPaymentMethod.root.showView()
                }
                hideEditButton = it.hideEditButton
                showOtherView(viewModel.getSelectedBankAccount())
            }
            enableSubmitButton(it)
            if (it.hasSubmit) {
                binding.includePaymentMethod.btnCompletePayment.visibility = View.INVISIBLE
                binding.btnShare.showView()
            } else {
                binding.includePaymentMethod.btnCompletePayment.showView()
                binding.btnShare.hideView()
            }
            if (it.hasSubmit) {
                binding.etInputNote.isEnabled = false
                binding.etInputNominal.isEnabled = false
                binding.tvButtonChangeAccount.hideView()
                binding.contactLayout.setOnClickListener(null)
                binding.rbIn.setOnClickListener(null)
                binding.rbOut.setOnClickListener(null)
                binding.rgTrxType.hideView()
                binding.includePaymentMethod.tvCashbackAmount.hideView()
                binding.grCategoriesInput.hideView()
            }
        }
    }
    //endregion

    // region private methods
    private fun enableSubmitButton(viewState: PaymentCheckoutViewModel.ViewState) {
        val activeAccountSelected = viewModel.getSelectedBankAccount()?.getActiveAccount().isTrue
        binding.includePaymentMethod.btnCompletePayment.isEnabled = !viewState.showNominalError
                && viewState.healthState != PaymentCheckoutViewModel.HEALTH_ERROR
                && binding.etInputNominal.getNumberValue() > 0
                && viewModel.getCustomerId().isNotNullOrBlank()
                && activeAccountSelected
                && hasPaymentMethod() && viewState.enableButton
    }

    private fun hasPaymentMethod(): Boolean {
        return if (viewModel.isPaymentIn()) {
            true
        } else {
            viewModel.vaBankCode.isNotNullOrEmpty()
        }
    }

    private fun goToAddBankAccount(showTutorial: Boolean) {
        val i = AddBankAccountActivity.createIntent(
            this,
            viewModel.getPaymentType().toString(),
            viewModel.bookId,
            AnalyticsConst.PEMBAYARAN,
            viewModel.getCustomerId(),
            "false",
            showTutorial.toString()
        )
        startActivityForResult(i, SELECT_BANK_ACCOUNT_RC)
    }

    private fun handlePartialVisibility(name: String?, bankAccount: BankAccount?) {
        with(binding) {
            tvNameTxt.text = name
            if (name.isNotNullOrBlank()) {
                tvNameTxt.showView()
                tvNameHintTxt.hideView()
                showOtherView(bankAccount)
            } else {
                tvNameTxt.hideView()
                tvNameHintTxt.showView()
                addBankLayout.hideView()
                detailGroup.hideView()
                includePaymentMethod.gpChangePaymentMethod.hideView()
                includePaymentMethod.includeDefaultPaymentMethod.root.hideView()
                tvRemainingFreeCount.hideView()
                infoRecordPaymentInTrx.hideView()
                bankAccountLayout.hideView()
                tilLayoutInputNote.hideView()
            }
        }
    }

    // To show bank account and overview API (admin fee)related views
    private fun showOtherView(bankAccount: BankAccount?){
        with(binding) {
            addBankLayout.showView()
            contactLayout.showView()
            tilLayoutInputNote.showView()
            if (bankAccount != null) {
                addBankLayout.hideView()
                bankAccountLayout.showView()
                tvButtonChangeAccount.visibility = (!hideEditButton).asVisibility()
                showBankAccount(bankAccount, bookId)
                showSummaryIfHaveRequiredData()
            } else {
                addBankLayout.showView()
                bankAccountLayout.hideView()
                detailGroup.showView()
                viewModel.refreshAdminAndDiscountFee()
                setAdminFeeViews(viewModel.adminFee, viewModel.discount, viewModel.loyaltyDiscount)
                tvRemainingFreeCount.hideView()
                safeAndFreeTxt.hideView()
                infoRecordPaymentInTrx.showView()
                includePaymentMethod.gpChangePaymentMethod.hideView()
                includePaymentMethod.includeDefaultPaymentMethod.root.hideView()
                includePaymentMethod.tvCashbackAmount.hideView()
                tvNominalReceiveMessageTxt.hideView()
                tvAmountReceivedTxt.hideView()
            }
        }
    }

    private fun handlePaymentMethodData(paymentMethod: PaymentMethod) {
        with(binding.includePaymentMethod) {
            tvPaymentMethodName.text = paymentMethod.name
            Glide.with(this@PaymentCheckoutActivity)
                .load(paymentMethod.logo)
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(ivPayMethodIcon)
            if (paymentMethod.saldoBalance!= null && paymentMethod.saldoBalance >= 0.0) {
                tvPaymentAmount.showView()
                includeLayout.root.showView()
                includeLayout.clLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_green_5)
                includeLayout.tvMessage.text = getString(R.string.saldo_selected_advertisement)
                includeLayout.tvMessage.setTextColor(getColorCompat(R.color.green_80))
                includeLayout.tvMessage.setDrawable(left = 0)
                includeLayout.btnCheck.hideView()
                tvPaymentAmount.text = " - ${Utility.formatAmount(paymentMethod.saldoBalance)}"
            } else {
                tvPaymentAmount.text = ""
                tvPaymentAmount.hideView()
                includeLayout.root.hideView()
            }
        }
    }

    private fun handlePaymentInSuccess(message: String) {
        binding.btnShare.setOnClickListener { shareLink(message) }
        shareLink(message)
    }

    private fun handlePaymentOutSuccess(url: String, paymentTabEnabled: Boolean) {
        redirectToMainActivity(paymentTabEnabled)
        startActivity(WebviewActivity.createIntent(this, url, ""))
    }

    private fun handleSummaryData(
        formattedAmountReceived: String?,
        formattedTotal: String?,
        destinationBankInformation: DestinationBankInformation?
    ) {
        if (viewModel.getSelectedBankAccount()?.getActiveAccount().isTrue) {
            if (destinationBankInformation?.flag == BANK_DOWN_TIME && destinationBankInformation.message.isNotNullOrBlank()) {
                setDownTimeBank(destinationBankInformation.message.orEmpty())
            }
        }
        if (viewModel.isPaymentIn()) {
            binding.tvAmountReceivedTxt.text = formattedTotal
            binding.tvNominalReceiveMessageTxt.text = getString(R.string.nominal_you_receive)
            binding.tvAmountTotalAllTxt.text = formattedAmountReceived
            binding.tvTotalAllTxt.text = getString(R.string.total_received)

        } else {
            binding.tvAmountReceivedTxt.text = formattedAmountReceived
            binding.tvNominalReceiveMessageTxt.text =
                getString(R.string.total_received_by_customers)
            binding.tvAmountTotalAllTxt.text = formattedTotal
            binding.tvTotalAllTxt.text = getString(R.string.nominal_you_pay)
        }
        showSummaryIfHaveRequiredData()
        if (viewModel.selectedPaymentMethod?.code == SALDO) {
            val saldoDisabled =
                viewModel.checkDailySaldoLimit() || viewModel.checkMonthlySaldoLimit()
                        || viewModel.selectedPaymentMethod?.saldoBalance.orNil < (viewModel.amount.toDouble() + viewModel.adminFee.orNil - viewModel.discount.orNil - viewModel.loyaltyDiscount?.tierDiscount.orNil)
            binding.includePaymentMethod.btnCompletePayment.isEnabled = !saldoDisabled
        }
    }

    private fun initListener() {
        binding.rbIn.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_TAGIH_INWARDS)
            viewModel.changePaymentType(PaymentConst.TYPE_PAYMENT_IN)
            viewModel.calculateSummary()
            viewModel.getSelectedCategory()?.let { it1 -> setSelectedCategory(it1) }
        }
        binding.rbOut.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_BAYAR_OUTWARDS)
            viewModel.changePaymentType(PaymentConst.TYPE_PAYMENT_OUT)
            viewModel.calculateSummary()
            viewModel.getSelectedCategory()?.let { it1 -> setSelectedCategory(it1) }
        }
        binding.etInputNominal.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) binding.etInputNominal.setSelection(binding.etInputNominal.length())
        }
        binding.etInputNominal.afterTextChanged {
            val amount = binding.etInputNominal.getNumberValue()
            viewModel.onAmountChanged(amount)
            if (!viewModel.isPaymentIn()) {
                showOtherView(viewModel.getSelectedBankAccount())
            }
            binding.etInputNominal.setSelection(binding.etInputNominal.length())
        }
        if (customerBalance.orNil != 0.0) {
            binding.etInputNominal.setAmountInEditText(customerBalance.orNil.toLong())
        }
        binding.addBankLayout.setOnClickListener {
            AppAnalytics.trackEvent(
                if (viewModel.isPaymentIn()) AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK else AnalyticsConst.EVENT_PAYMENT_ADD_RECIPIENT_BANK,
                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
            )
            onClickAddBankAccount(false)
        }

        binding.includePaymentMethod.btnCompletePayment.setSingleClickListener {
            showConfirmationAndProceed()
        }

        binding.contactLayout.setOnClickListener { loadUserContactFragment() }
        binding.includePaymentMethod.includeDefaultPaymentMethod.tvSelect.setOnClickListener {
            openPaymentMethodBottomSheet()
        }
        binding.includePaymentMethod.tvChange.setOnClickListener {
            openPaymentMethodBottomSheet()
        }
    }

    private fun openPaymentMethodBottomSheet() {
        val bs = PaymentMethodBottomSheet.createInstance(
            type = TYPE_PAY_OUT,
            bankCode = viewModel.getSelectedBankAccount()?.bankCode,
            amount = viewModel.amount.toDouble() + viewModel.adminFee.orNil - viewModel.discount.orNil - viewModel.loyaltyDiscount?.tierDiscount.orNil,
            selectedPaymentMethodCode = viewModel.selectedPaymentMethod?.code
        )
        bs.show(supportFragmentManager, PaymentMethodBottomSheet.TAG)
    }

    private fun showConfirmationAndProceed() {
        val showDialog = RemoteConfigUtils.shouldShowDisbursementConfirmationDialog()
        if (showDialog) {
            val selectBankAccount = viewModel.getSelectedBankAccount()
            selectBankAccount?.let {
                val accountDetails = String.format(
                    getString(R.string.payment_confirm_detail),
                    it.accountHolderName,
                    it.bankCode,
                    it.accountNumber
                )
                val receivedAmount = if (viewModel.isPaymentIn()) {
                    viewModel.amount.toDouble().minus(viewModel.adminFee.orNil)
                } else {
                    viewModel.amount.toDouble()
                }
                val warning = PaymentUtils.getWarningForReceiver(
                    it.bankCode.orEmpty(),
                    PaymentUtils.getPaymentTypeMapping(viewModel.getPaymentType()),
                    receivedAmount
                )
                Utilities.safeLet(warning?.title, warning?.message) { title, message ->
                    val parseMessage =
                        PaymentUtils.parseWarningMessage(message, viewModel.amount.toDouble())
                    val dialog = BukuDialog(
                        this, title,
                        parseMessage,
                        getString(R.string.cancel),
                        getString(R.string.yes_continue),
                        this, BukuDialog.UseCase.TWO_BUTTONS,
                        RC_CONTINUE_WITH_WARNING
                    )
                    dialog.setUseFullWidth(false)
                    dialog.show()
                } ?: run {
                    GenericConfirmationDialog.create(this) {
                        titleRes = R.string.data_rekening_penerima_sudah_sesuai
                        bodyRes = R.string.payment_confirm_detail
                        customBody = Utilities.makeSectionOfTextBold(
                            accountDetails,
                            it.accountHolderName ?: EMPTY_STRING,
                            object : ClickableSpan() {
                                override fun onClick(widget: View) {
                                    //if click requires
                                }

                                override fun updateDrawState(ds: TextPaint) {
                                    super.updateDrawState(ds)
                                    ds.isUnderlineText = false
                                    ds.typeface = Typeface.DEFAULT_BOLD
                                    ds.color = context.getColorCompat(R.color.black_80)
                                }
                            })
                        btnLeftRes = R.string.batal
                        btnRightRes = R.string.ya_sesuai
                        rightBtnCallback = {
                            logConfirmationDialogEvent(AnalyticsConst.PROCEED)
                            if (selectedPaymentMethod?.code == SALDO) {
                                startPaymentPinActivityForResult.launch(
                                    NewPaymentPinActivity.createIntent(
                                        this@PaymentCheckoutActivity,
                                        PinType.PIN_CONFIRM.toString()
                                    )
                                )
                            } else {
                                proceedToDisburse()
                            }
                        }
                        leftBtnCallback = { logConfirmationDialogEvent(AnalyticsConst.CANCEL) }
                    }.show()
                }
            }
        } else {
            proceedToDisburse()
        }
    }

    private fun logConfirmationDialogEvent(btnAction: String) {
        val builder = PropBuilder()
        builder.put(
            AnalyticsConst.TYPE,
            if (viewModel.isPaymentIn()) AnalyticsConst.PAYMENT_IN else AnalyticsConst.PAYMENT_OUT
        )
        builder.put(AnalyticsConst.ACTION, btnAction)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_CONFIRM_POPUP, builder)
    }

    private fun initNoteAdapter(noteList: List<String>) {
        val recommendationAdapter: ArrayAdapter<String> =
            ArrayAdapter<String>(this, android.R.layout.simple_list_item_1).apply {
                addAll(noteList)
                notifyDataSetChanged()
                filter.filter("")
            }
        binding.etInputNote.apply {
            setAdapter(recommendationAdapter)
            setOnFocusChangeListener { _, isFocused -> // to show the popup even user hasn't type
                // We need to use post to avoid BadTokenException
                this.post {
                    if (isFocused && !<EMAIL>) {
                        try {
                            showDropDown()
                        } catch (ex: WindowManager.BadTokenException) {
                            FirebaseCrashlytics.getInstance().recordException(ex)
                        }
                    }
                }
            }
            addTextChangedListener {
                val text = it.toString()
                if (text.isNotEmpty()) { // to dismiss the popup after user type the 2nd char
                    dismissDropDown()
                }
                viewModel.onNoteChanged(text)
            }
            setOnItemClickListener { _, _, position, _ ->
                val prop = AppAnalytics.PropBuilder().put(
                    AnalyticsConst.ID, recommendationAdapter.getItem(position)
                        ?: ""
                )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_FORM_AUTOCOMPLETE, prop)
            }
        }
    }

    private fun initPaymentInView() {
        binding.contactLayout.showView()
        binding.tvNameHintTxt.text = getString(R.string.type_customer_name)
        binding.tvAmountTitleTxt.text = getString(R.string.enter_amount_in_message)
        binding.ivTypeImg.setImageResource(R.drawable.ic_utang_credit_icon)
        binding.ivContactImg.setImageResource(R.drawable.ic_utang_contact_icon_green)
        binding.etInputNominal.setTextColor(getColorCompat(R.color.green_80))
        binding.tvTotalAllTxt.text = getString(R.string.nominal_you_receive)
        binding.tvModalMainTitle1.setText(R.string.collect_money_from)
        binding.rbIn.isChecked = true
        binding.rbOut.isChecked = false
        binding.includePaymentMethod.gpChangePaymentMethod.hideView()
        binding.includePaymentMethod.includeDefaultPaymentMethod.root.hideView()
        binding.tvAddBankSubtitleTxt.text = getString(R.string.add_bank_account_subtitle_in)
        viewModel.healthCheck()
    }

    private fun initPaymentOutView() {
        binding.contactLayout.hideView()
        binding.tvNameHintTxt.text = getString(R.string.optional)
        binding.tvAmountTitleTxt.text = getString(R.string.enter_amount_out_message)
        binding.ivTypeImg.setImageResource(R.drawable.ic_harga_model)
        binding.ivContactImg.setImageResource(R.drawable.ic_utang_contact_icon_red)
        binding.etInputNominal.setTextColor(getColorCompat(R.color.red_80))
        binding.tvTotalAllTxt.text = getString(R.string.nominal_you_pay)
        binding.tvModalMainTitle1.setText(R.string.give_money_to)
        binding.rbOut.isChecked = true
        binding.rbIn.isChecked = false
        binding.includePaymentMethod.gpChangePaymentMethod.hideView()
        binding.includePaymentMethod.btnCompletePayment.text = getString(R.string.bayar)
        binding.includePaymentMethod.includeDefaultPaymentMethod.root.hideView()
        binding.tvAddBankSubtitleTxt.text = getString(R.string.add_bank_account_subtitle_out)
        viewModel.healthCheck()
        if (binding.etInputNominal.getNumberValue() > 0) {
            showOtherView(viewModel.getSelectedBankAccount())
        }
    }

    private fun initView(noteList: List<String>) {
        initNoteAdapter(noteList)
    }

    private fun loadUserContactFragment() {
        val debitCredit =
            if (viewModel.getPaymentType() == PaymentConst.TYPE_PAYMENT_OUT) AppConst.DEBIT else AppConst.CREDIT
        userContactFragment =
            UserContactFragment.getInstance(
                getString(R.string.create_payment), debitCredit,
                if (viewModel.isPaymentIn()) CustomerSearchUseCase.ACCOUNTING else CustomerSearchUseCase.PAYMENT
            )
        supportFragmentManager.beginTransaction()
            .add(R.id.contact_fragment_container, userContactFragment!!).commit()
    }

    private fun onClickAddBankAccount(showTutorial: Boolean) {
        val paymentType = viewModel.getPaymentType()
        if (paymentType == PaymentConst.TYPE_PAYMENT_IN) {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_PAYMENT_SET_USER_BANK,
                AppAnalytics.PropBuilder().put("entryPoint", entryPoint)
            )
            startActivityForResult(
                NewPaymentPinActivity.createIntent(
                    this,
                    PinType.PIN_CONFIRM.toString()
                ), if (showTutorial) PIN_RC_WITH_TUTORIAL else PIN_RC
            )
        } else {
            AppAnalytics.trackEvent(
                "payment_recipient_bank_set_clicked",
                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
            )
            goToAddBankAccount(showTutorial)
        }
    }

    private fun openHelp() {
        AppAnalytics.trackEvent("open_payment_tutorial_screen")
        Utilities.launchBrowser(
            this,
            RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
        )
    }

    /**
     * Performs following checks on the entered amount
     * 1. Max Daily transaction limit check
     * 2. Max Per transaction limit check
     */
    private fun performMaxLimitChecks(amount: Long) {
        val limits =
            if (viewModel.isPaymentIn()) viewModel.paymentInLimits else viewModel.paymentOutLimits
        var isWhitelistLimitError = false
        limits?.let {
            val infoText = when (PaymentPrefManager.getInstance().getKycTier()) {
                KycTier.NON_KYC -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(R.string.must_verify_account_for_payments)
                        }
                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(R.string.must_verify_account_for_payments)
                        }
                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }
                KycTier.ADVANCED -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(
                                R.string.upgrade_kyc_to_increase_daily_limit,
                                Utility.formatAmount(it.dailyTrxLimit)
                            )
                        }
                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(
                                R.string.upgrade_kyc_to_increase_trx_limit,
                                Utility.formatAmount(it.perTrxLimit)
                            )
                        }
                        amount > it.whitelistLimits?.remainingTrxAmountLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.remaining_limit_x,
                                it.whitelistLimits?.remainingTrxAmountLimit
                            )
                            isWhitelistLimitError = true
                            getString(
                                R.string.whitelisted_user_limit_reached,
                                Utility.formatAmount(it.whitelistLimits?.maxTrxAmountLimit)
                            )
                        }
                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }
                KycTier.SUPREME -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(
                                R.string.upgrade_kyc_to_increase_daily_limit_supreme,
                                Utility.formatAmount(it.dailyTrxLimit)
                            )
                        }
                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(
                                R.string.upgrade_kyc_to_increase_trx_limit_supreme,
                                Utility.formatAmount(it.perTrxLimit)
                            )
                        }
                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }
            }
            binding.tvKycUpgradeInfo.apply {
                text = Utilities.makeSectionOfTextClickable(
                    infoText,
                    if (isWhitelistLimitError) getString(R.string.to_learn) else getString(R.string.learn_more),
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            if (isWhitelistLimitError) {
                                InputUtils.hideKeyBoardWithCheck(this@PaymentCheckoutActivity)
                                PaymentUtils.showKycKybStatusBottomSheet(
                                    supportFragmentManager, AnalyticsConst.PAYMENT_CHECKOUT
                                )
                            } else {
                                Utilities.launchBrowser(
                                    this@PaymentCheckoutActivity,
                                    RemoteConfigUtils.getPaymentConfigs().kycTierInfoUrl
                                )
                            }
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.color = getColorCompat(R.color.blue_60)
                        }
                    })
                movementMethod = LinkMovementMethod.getInstance()
                visibility = infoText.isNotNullOrBlank().asVisibility()
            }
        } ?: run {
            setMaximumAmountLimitError(null, null)
            binding.tvKycUpgradeInfo.hideView()
        }
    }

    private fun setMaximumAmountLimitError(messageRes: Int?, maxLimit: Double?) {
        messageRes?.let {
            binding.tvNominalErrorTxt.showView()
            binding.tvNominalErrorTxt.text = getString(
                it,
                Utility.formatAmount(maxLimit)
            )
            binding.vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
        } ?: run {
            binding.tvNominalErrorTxt.hideView()
            binding.tvNominalErrorTxt.text = ""
            binding.vwDivider.setBackgroundColor(getColorCompat(R.color.black_10))
        }
    }

    private fun redirectToMainActivity(paymentTabEnabled: Boolean) {
        MainActivity.startActivitySingleTopToTab(
            this,
            if (paymentTabEnabled) TabName.PAYMENT else TabName.OTHERS
        )
    }

    private fun reinitView(paymentTypeChanged: Boolean) {
        if (viewModel.isPaymentIn()) {
            initPaymentInView()
            binding.tvInfoRecordPaymentInTrx.text =
                getString(R.string.billing_will_be_recorded_after_success)
        } else {
            initPaymentOutView()
            binding.tvInfoRecordPaymentInTrx.text =
                getString(R.string.trx_will_be_recorded_after_success)
        }
        binding.etInputNominal.requestFocus()
        val amount = binding.etInputNominal.getNumberValue()
        if (paymentTypeChanged) {
            viewModel.onAmountChanged(amount)
        }
    }

    private fun showCategories(categories: List<PaymentCategoryItem>) {
        binding.includePaymentCategories.cgPaymentCategories.removeAllViews()
        val visibleCategoryCount = RemoteConfigUtils.getPaymentConfigs().visibleCategoryCount
        for (i in 0 until visibleCategoryCount) {
            if (i >= categories.size) return
            val chip = layoutInflater.inflate(
                R.layout.payment_category_chip,
                binding.includePaymentCategories.cgPaymentCategories,
                false
            ) as Chip
            chip.apply {
                text = categories[i].name
                id = i
                isCloseIconVisible = false
                isCheckedIconVisible = false
            }
            binding.includePaymentCategories.cgPaymentCategories.addView(chip)
        }
        binding.includePaymentCategories.cgPaymentCategories.setOnCheckedChangeListener { group, chipId ->
            if (chipId == -1) return@setOnCheckedChangeListener
            viewModel.setPaymentCategory(categories[chipId])
            group.children.forEach {
                (it as Chip?)?.typeface = Typeface.DEFAULT
            }
            val chip = group.getChildAt(group.checkedChipId) as Chip?
            chip?.typeface = Typeface.DEFAULT_BOLD
            binding.includePaymentCategories.chipAllCategories.isChecked = false
            binding.includePaymentCategories.chipAllCategories.typeface = Typeface.DEFAULT
        }
    }

    /**
     * Makes selected category chip to be active. In case it's not in the chip group,
     * then "other" categories chip is made active.
     * It loops through the Chips in the chip group and finds the name of the chip that
     * matches the selected category. Checking through name is fine because this is a in-memory data
     * and we can expect it to always be there.
     */
    private fun setSelectedCategory(category: PaymentCategoryItem) {
        var chipFound = false
        with(binding.includePaymentCategories) {
            cgPaymentCategories.children.forEach {
                (it as Chip?)?.let { chip ->
                    if (chip.text == category.name) {
                        chip.isChecked = true
                        chip.typeface = Typeface.DEFAULT_BOLD
                        chipFound = true
                    } else {
                        chip.isChecked = false
                        chip.typeface = Typeface.DEFAULT
                    }
                }
            }
            chipAllCategories.apply {
                isChecked = !chipFound
                typeface = if (!chipFound) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
            }
        }
    }

    private fun removeUserFragment() {
        if (userContactFragment != null) {
            supportFragmentManager.beginTransaction().remove(userContactFragment!!).commit()
            userContactFragment = null
        }
    }

    private fun setAdminFeeViews(
        fee: Double?,
        discount: Double?,
        loyaltyDiscount: LoyaltyDiscount?
    ) {
        val discountedFee = fee.orNil - discount.orNil
        with(binding) {
            tvLoyaltyTier.visibility = (loyaltyDiscount?.tierDiscount.orNil.isNotZero()).asVisibility()
            tvLoyaltyTier.text = getString(R.string.discount_level, loyaltyDiscount?.tierName.orEmpty())
            tvLoyaltyTier.setDrawableRightListener {
                LoyaltyTierDiscountsBottomSheet.createInstance(if (viewModel.isPaymentIn()) PaymentConst.Feature.PAYMENT_IN else PaymentConst.Feature.PAYMENT_OUT)
                    .show(supportFragmentManager, LoyaltyTierDiscountsBottomSheet.TAG)
            }
            tvLoyaltyDiscount.visibility = loyaltyDiscount?.tierDiscount.orNil.isNotZero().asVisibility()
            val loyaltyDiscountSign = if (viewModel.isPaymentIn()) "+" else "-"
            tvLoyaltyDiscount.text =
                "${loyaltyDiscountSign}${Utility.formatAmount(loyaltyDiscount?.tierDiscount)}"
            when (fee) {
                null -> {
                    tvDiscountedFee.text = "-"
                    tvDiscountedFee.setTextColor(getColorCompat(R.color.black_80))
                    tvDiscountedFee.showView()
                    tvAdminFeeTxt.hideView()
                    tvNominalReceiveMessageTxt.hideView()
                    tvAmountReceivedTxt.hideView()
                    safeAndFreeTxt.hideView()
                }
                0.0 -> {
                    val freeRemainingCount = viewModel.viewState.value?.remainingFreeQuota
                    if (freeRemainingCount != null && freeRemainingCount > 0) {
                        tvRemainingFreeCount.text =
                            getString(R.string.x_left, freeRemainingCount)
                        tvRemainingFreeCount.showView()
                    } else {
                        tvRemainingFreeCount.hideView()
                    }
                    tvDiscountedFee.text = getString(R.string.free).uppercase()
                    tvDiscountedFee.setTextColor(getColorCompat(R.color.black_80))
                    tvDiscountedFee.showView()
                    tvNominalReceiveMessageTxt.hideView()
                    tvAmountReceivedTxt.hideView()
                    safeAndFreeTxt.showView()
                    tvAdminFeeTxt.hideView()
                }
                discountedFee -> {
                    val discountedFeeSign = if (viewModel.isPaymentIn()) "-" else ""
                    tvDiscountedFee.text = discountedFeeSign + Utility.formatAmount(discountedFee)
                    tvDiscountedFee.setTextColor(getColorCompat(R.color.black_80))
                    tvDiscountedFee.showView()
                    tvNominalReceiveMessageTxt.showView()
                    tvAmountReceivedTxt.showView()
                    safeAndFreeTxt.hideView()
                    tvAdminFeeTxt.hideView()
                }
                else -> {
                    tvAdminFeeTxt.text = Utility.formatAmount(fee.orNil)
                    val discountedFeeSign = if (viewModel.isPaymentIn()) "-" else ""
                    tvDiscountedFee.text = discountedFeeSign + Utility.formatAmount(discountedFee)
                    tvDiscountedFee.setTextColor(getColorCompat(R.color.blue_60))
                    tvDiscountedFee.showView()
                    tvAdminFeeTxt.showView()
                    tvNominalReceiveMessageTxt.showView()
                    tvAmountReceivedTxt.showView()
                    safeAndFreeTxt.hideView()
                }
            }
            showDiscountIfApplicable()
        }
    }

    private fun setBankStatus(bankAccount: BankAccount) {
        when {
            bankAccount.getActiveAccount() -> {
                setActiveBankAccount()
            }
            bankAccount.isMatchingFailure() -> {
                setMatchingFailureBank(
                    bankAccount.message ?: getString(R.string.bank_name_is_not_matching_ktp)
                )
            }
            bankAccount.isManualMatchingInProgress() -> {
                setMatchingProcessBank(bankAccount)
            }
            bankAccount.getInActiveAccount() -> {
                setBlockAndUsedBank(
                    bankAccount.message ?: getString(R.string.used_info_text),
                    isUsedAccount = true
                )
            }
            bankAccount.getNotSupportedAccount() -> {
                setNotSupportedBank(
                    bankAccount.message
                        ?: getString(R.string.not_supported_info_text)
                )
            }
            else -> {
                setBlockAndUsedBank(
                    bankAccount.message
                        ?: getString(R.string.blocked_info_text), isUsedAccount = false
                )
            }
        }
    }

    private fun setActiveBankAccount() {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.solitude_blue))
            tvLabelAccountTxt.text = getString(R.string.label_customer_account)
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_safe)
            baInfo.hideView()
            tvButtonChangeAccount.text = getString(R.string.label_change)
            tvButtonChangeAccount.setTextColor(getColorCompat(R.color.blue_60))
        }
    }

    private fun setNotSupportedBank(infoText: String) {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.yellow5))
            tvLabelAccountTxt.text = getString(R.string.label_customer_account)
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_warning_triangle)
            tvButtonChangeAccount.text = getString(R.string.change)
            tvButtonChangeAccount.setTextColor(getColorCompat(R.color.blue_60))
            baInfo.addText(infoText)
            baInfo.setInfoViewStyle(
                textColor = getColorCompat(R.color.red_80),
                textStyle = R.style.Body3,
                backgroundType = "error_without_drawable"
            )
            baInfo.showView()
        }
    }

    private fun setBlockAndUsedBank(
        infoText: String,
        isUsedAccount: Boolean
    ) {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.red_5))
            tvLabelAccountTxt.text = getString(R.string.locked_account)
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_blocked_icon)
            tvButtonChangeAccount.text = getString(R.string.try_other_account)
            tvButtonChangeAccount.setTextColor(getColorCompat(R.color.red_80))
            baInfo.setInfoViewStyle(
                textColor = getColorCompat(R.color.red_80),
                textStyle = R.style.Body3,
                backgroundType = "error_without_drawable"
            )
            baInfo.addText(Utilities.makeSectionOfTextClickable(
                infoText + " " + getString(R.string.bold_text),
                getString(R.string.bold_text),
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        val intent = WebviewActivity.createIntent(
                            this@PaymentCheckoutActivity, getFaqUrl(isUsedAccount), ""
                        )
                        startActivity(intent)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.color = getColorCompat(R.color.red_80)
                    }
                }))
            baInfo.showView()
        }
    }

    private fun setDownTimeBank(infoText: String) {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.yellow5))
            tvLabelAccountTxt.text = getString(R.string.label_customer_account)
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_warning_triangle)
            tvButtonChangeAccount.text = getString(R.string.change)
            tvButtonChangeAccount.setTextColor(getColorCompat(R.color.blue_60))
            baInfo.addText(infoText)
            baInfo.setInfoViewStyle(
                textColor = getColorCompat(R.color.black_60),
                textStyle = R.style.Body3,
                backgroundType = "warning_without_drawable"
            )
            baInfo.showView()
        }
    }

    private fun setMatchingFailureBank(infoText: String) {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.yellow_5))
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_warning_triangle)
            baInfo.setInfoViewStyle(
                textColor = getColorCompat(R.color.red_80),
                textStyle = R.style.Body3,
                backgroundType = "error_without_drawable"
            )
            baInfo.addText(infoText)
            baInfo.showView()
            tvTxtBankTitle.setTextColor(getColorCompat(R.color.black_60))
            tvTxtAccountNumber.setTextColor(getColorCompat(R.color.black_40))
        }
    }

    private fun setMatchingProcessBank(bankAccount: BankAccount) {
        with(binding) {
            bankAccountLayout.setBackgroundColor(getColorCompat(R.color.yellow_5))
            ivSafeBankAccountImg.setImageResource(R.drawable.ic_warning_triangle)
            baInfo.setInfoViewStyle(
                textColor = getColorCompat(R.color.black_80),
                textStyle = R.style.Body3,
                backgroundType = "warning"
            )
            setInfoText(
                bankAccount.message ?: getString(R.string.name_matching_in_progress),
                getString(R.string.to_learn),
                UrlType.MATCHING_INFO,
                bankAccount
            )
            tvTxtBankTitle.setTextColor(getColorCompat(R.color.black_60))
            tvTxtAccountNumber.setTextColor(getColorCompat(R.color.black_40))
        }
    }

    private fun setInfoText(
        text: String, clickableText: String, urlType: UrlType,
        bankAccount: BankAccount,
    ) {
        with(binding) {
            tvInfo.showView()
            tvInfo.text =
                Utilities.makeSectionOfTextClickable(
                    text,
                    clickableText,
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            openWeb(urlType, Gson().toJson(bankAccount))
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.isUnderlineText = true
                        }
                    })
            tvInfo.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    private fun openWeb(urlType: UrlType, bankAccount: String? = null) {
        startActivity(
            PaymentUtils.getBankAccountRedirectionIntent(
                context = this,
                urlType = urlType,
                bankAccount = bankAccount,
                paymentType = PaymentConst.PAYMENT_IN,
                entryPoint = entryPoint
            )
        )
    }

    private fun getFaqUrl(isUsedAccount: Boolean) = if (isUsedAccount) {
        AppConst.FAQ_USED_ACCOUNT_BW_URL
    } else {
        AppConst.FAQ_BLOCKED_ACCOUNT_BW_URL
    }

    private fun setSubmitButtonText(showAmount: Boolean = false) {
        binding.includePaymentMethod.btnCompletePayment.text = getSubmitButtonText(showAmount)
    }

    private fun getSubmitButtonText(showAmount: Boolean): String {
        return if (viewModel.isPaymentIn()) {
            if (viewModel.amount > BigDecimal(0) && showAmount) {
                getString(
                    R.string.send_invoice_with_amount,
                    Utility.formatAmount(viewModel.amount.toDouble())
                )
            } else {
                getString(R.string.send_invoice)
            }
        } else {
            if (viewModel.amount > BigDecimal(0) && showAmount) {
                getString(
                    R.string.pay_with_amount,
                    Utility.formatAmount(viewModel.getTotalAmountForMoneyOut())
                )
            } else {
                getString(R.string.pay)
            }
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.tvHelpIcon.setOnClickListener { openHelp() }
    }

    private fun shareLink(message: String) {
        val receiverIntent = Intent(this, ReferralSharingReceiver::class.java)
        receiverIntent.putExtra(SharingUtilReceiver.EVENT_NAME, "payment_request_share")
        shareText(message, receiverIntent = receiverIntent)
    }

    private fun showBankAccount(bankAccount: BankAccount?, bookId: String?) {
        if (bankAccount != null) {
            binding.addBankLayout.hideView()
            binding.bankAccountLayout.showView()
            binding.tvTxtBankTitle.text =
                Utility.dashDividedString(bankAccount.bankCode, bankAccount.accountHolderName)
            binding.tvTxtAccountNumber.text = bankAccount.accountNumber
            Glide.with(this)
                .load(bankAccount.getBankLogoIfAvailable())
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(binding.ivImageBank)
            binding.tvButtonChangeAccount.setOnClickListener {
                val eventName =
                    if (viewModel.getPaymentType() == PaymentConst.TYPE_PAYMENT_IN) AnalyticsConst.EVENT_PAYMENT_EDIT_USER_BANK else AnalyticsConst.EVENT_PAYMENT_EDIT_RECIPIENT_BANK
                AppAnalytics.trackEvent(
                    eventName,
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
                )
                val bankListBtSheet = BankAccountListBottomSheetFragment.createBankAccountInstance(
                    AnalyticsConst.CASH_TRANSACTION,
                    if (viewModel.isPaymentIn()) null else viewModel.getCustomerId(),
                    bankAccount.bankAccountId,
                    viewModel.getPaymentType()
                )
                bankListBtSheet.show(supportFragmentManager, "bankListBtSheet")
            }
            setBankStatus(bankAccount)
        } else {
            binding.addBankLayout.showView()
            binding.bankAccountLayout.hideView()
        }
    }

    private fun showExitDialog() {
        GenericConfirmationDialog.create(this) {
            titleRes = R.string.payment_exit_confirmation_title
            bodyRes = R.string.payment_exit_confirmation_subtitle
            btnLeftRes = R.string.sign_out
            btnRightRes = R.string.next
            leftBtnCallback = { finish() }
        }.show()
    }

    private fun showBookValidationError(bookName: String?) {
        // NOTE: Dismiss any existing payment down bottom sheet. This is a temporary solution,
        // proper solution will be to have a unified error handling.
        paymentDownBottomSheet?.dismiss()
        val dialog = BukuDialog(
            this, getString(R.string.change_name_of_business),
            getString(
                R.string.change_name_of_business_msg,
                bookName?.uppercase(Locale.getDefault())?.trim()
            ),
            getString(R.string.cancel),
            getString(R.string.change),
            this, BukuDialog.UseCase.TWO_BUTTONS, RC_BOOK_NAME_CHANGE
        )
        dialog.setUseFullWidth(false)
        dialog.show()
    }

    private fun showInputBankAccountCoachmark() {
        if (!isInputBankCoachmarkShown) {
            isInputBankCoachmarkShown = true
            viewModel.setHasShownInputBankCoachmark()
            onboardingWidget = OnboardingWidget.createInstance(
                this,
                this,
                OnboardingPrefManager.TUTOR_INPUT_BANK_ACCOUNT,
                binding.addBankLayout,
                R.drawable.onboarding_attention,
                "",
                getString(R.string.onboarding_input_bank_account_step1),
                getString(R.string.next),
                FocusGravity.CENTER,
                ShapeType.RECTANGLE_FULL,
                1,
                2,
                true,
                true,
                0
            )
        }
    }

    private fun handleApiError(
        isServiceDown: Boolean, message: String? = null, errors: JSONObject?
    ) {
        if (errors != null) {
            showPaymentLimitsBottomSheet(errors.toString())
        } else {
            showPaymentDownBottomSheet(isServiceDown, message)
        }
    }

    private fun proceedToDisburse() {
        if (RemoteConfigUtils.isPaymentCategoryMandatory() && viewModel.getSelectedCategory() == null)
            showCategoryErrorMessage()
        else {
            viewModel.createDisbursement(from)
        }
    }

    private fun showCategoryErrorMessage() {
        binding.tvCategoryErrorMessage.showView()
    }

    private fun showPaymentLimitsBottomSheet(errors: String) {
        val limitBottomSheet = PaymentLimitsBottomSheet.createInstance(errors, AnalyticsConst.PAYMENT_CHECKOUT)
        limitBottomSheet.show(supportFragmentManager, PaymentLimitsBottomSheet.TAG)
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        paymentDownBottomSheet =
            PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet?.show(supportFragmentManager, "PaymentDownBottomSheet")
    }

    private fun showProfileDialog() {
        val dialog = NullProfileReferralDialog(
            this,
            R.string.null_profile_payment_content,
            hideBtn = true
        ) {}
        dialog.show()
        scope.launch {
            delay(1000)
            if (isFinishing || isDestroyed) return@launch
            dialog.dismiss()
            val intent = BusinessProfileFormActivity.getIntent(this@PaymentCheckoutActivity)
            intent.putExtra(isRedirectFromPaymentsToProfile, true)
            startActivityForResult(intent, START_BUSINESS_PROFILE_RC)
        }
    }

    private fun showSnackBar() {
        val snackBar = Snackbar.make(binding.root, R.string.payment_snackbar, Snackbar.LENGTH_SHORT)
        snackBar.setTextColor(ContextCompat.getColor(this, R.color.red_80))
        val imgClose = ImageView(this)
        imgClose.scaleType = ImageView.ScaleType.CENTER_INSIDE
        val layImageParams = ViewGroup.LayoutParams(WRAP_CONTENT, MATCH_PARENT)
        imgClose.setImageResource(R.drawable.ic_close_button_red)
        val textViewAction = snackBar.view.findViewById(R.id.snackbar_action) as TextView
        (textViewAction.parent as SnackbarContentLayout).addView(imgClose, layImageParams)
        imgClose.setOnClickListener { snackBar.dismiss() }
        snackBar.setBackgroundTint(ContextCompat.getColor(this, R.color.red_5))
        val layoutParams = ActionBar.LayoutParams(snackBar.view.layoutParams)
        layoutParams.gravity = Gravity.TOP
        snackBar.view.layoutParams = layoutParams
        snackBar.show()
    }

    /**
     * Checks if we have the amount entered and customer selected, then only show the summary.
     */
    private fun showSummaryIfHaveRequiredData() {
        val minimumAmountThreshold = viewModel.viewState.value?.minimumAmountThreshold ?: 0.1
        if (binding.etInputNominal.getNumberValue() >= minimumAmountThreshold
            && viewModel.getCustomerId().isNotNullOrBlank()
        ) {
            binding.detailGroup.showView()
            setAdminFeeViews(viewModel.adminFee, viewModel.discount, viewModel.loyaltyDiscount)
            binding.infoRecordPaymentInTrx.showView()
            if (!viewModel.isPaymentIn()) {
                setSaldoErrorCases()
            }
            setSubmitButtonText(true)
        } else {
            binding.detailGroup.hideView()
            binding.infoRecordPaymentInTrx.hideView()
            binding.includePaymentMethod.clCashBack.hideView()
            binding.tvRemainingFreeCount.hideView()
            binding.includePaymentMethod.includeLayout.root.hideView()
            setSubmitButtonText(false)
        }
    }

    private fun showDiscountIfApplicable() {
        val discountValue = viewModel.discount.orNil + viewModel.loyaltyDiscount?.tierDiscount.orNil
        val discountFeeText = viewModel.viewState.value?.discountFeeText.orEmpty()
        if (discountValue > 0.0 && discountFeeText.isNotEmpty()) {
            binding.includePaymentMethod.tvCashbackAmount.showView()
            binding.includePaymentMethod.tvCashbackAmount.setDrawable(0)
            binding.includePaymentMethod.clCashBack.showView()
            binding.includePaymentMethod.tvCashbackAmount.text = discountFeeText.replace(discountFee, Utility.formatAmount(discountValue))
        } else {
            binding.includePaymentMethod.clCashBack.hideView()
        }
    }
    // endregion

    // region implemented overridden methods
    override fun onButtonClicked() {
        viewModel.reloadData()
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        // do nothing
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        when (id) {
            OnboardingPrefManager.TUTOR_INPUT_BANK_ACCOUNT -> onClickAddBankAccount(true)
        }
    }

    override fun onCustomerSelected(contact: Contact?, contactSource: String) {
        removeUserFragment()
        contact?.run {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER)
            viewModel.clearSelectedPaymentMethod()
            viewModel.refreshAdminAndDiscountFee()
            viewModel.onContactSelected(this)
        }
    }

    override fun onBankAccountSelected(bankAccount: BankAccount?) {
        AppAnalytics.trackEvent(
            if (viewModel.isPaymentIn()) AnalyticsConst.EVENT_PAYMENT_SWITCH_USER_BANK else AnalyticsConst.EVENT_PAYMENT_SWITCH_RECIPIENT_BANK,
            AppAnalytics.PropBuilder()
                .put(AnalyticsConst.ENTRY_POINT, entryPoint)
                .put(
                    if (viewModel.isPaymentIn()) AnalyticsConst.USER_BANK else AnalyticsConst.RECIPIENT_BANK,
                    bankAccount?.bankCode
                )
        )
        viewModel.setSelectedBankAccount(bankAccount)
    }

    override fun addNewBankAccount() {
        if (viewModel.isPaymentIn()) {
            startActivityForResult(
                NewPaymentPinActivity.createIntent(
                    this,
                    PinType.PIN_CONFIRM.toString()
                ), PIN_RC
            )
        } else goToAddBankAccount(false)
    }

    override fun limitBsDismissCallback() {
        showConfirmationAndProceed()
    }

    override fun leftBukuDialogBtnListener(useCase: BukuDialog.UseCase, requestCode: Int) {
        when (requestCode) {
            RC_BOOK_NAME_CHANGE -> {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
    }

    override fun rightBukuDialogBtnListener(useCase: BukuDialog.UseCase, requestCode: Int) {
        when (requestCode) {
            RC_BOOK_NAME_CHANGE -> {
                // Start business name change activity for result
                val intent = CreateBusinessProfileActivity.createIntent(
                    this, User.getBusinessId(), true,
                    CreateBusinessProfileActivity.UseCase.PAYMENT_CHECKOUT
                )
                startActivityForResult(intent, REQUEST_BUSINESS_NAME_CHANGE)
            }
            RC_CONTINUE_WITH_WARNING -> {
                proceedToDisburse()
            }
        }
    }

    override fun openPlatformFeeAndCashbackInfo() {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_FEE_INFO_PAGE_CLICK,
            PropBuilder().apply {
                put(
                    AnalyticsConst.ENTRY_POINT,
                    if (viewModel.isPaymentIn()) AnalyticsConst.PAYMENT_REQUEST else AnalyticsConst.PAYMENT_SEND
                )
                put(
                    AnalyticsConst.TYPE,
                    AnalyticsConst.MORE_INFO
                )
            }
        )
        startActivity(
            WebviewActivity.createIntent(this, PaymentConst.ABOUT_PAYMENT_CHARGING_URL, "")
        )
    }

    override fun changePaymentMethod(
        finproPaymentMethod: FinproPaymentMethod,
        dailySaldoLimit: Double?,
        monthlySaldoLimit: Double?,
        eventProperty: PropBuilder?
    ) {
        selectedPaymentMethod = PaymentMethod(
            name = finproPaymentMethod.name,
            code = finproPaymentMethod.code,
            logo = finproPaymentMethod.logo,
            saldoBalance = finproPaymentMethod.details?.saldoBalance,
            dailySaldoLimit = dailySaldoLimit,
            monthlySaldoLimit = monthlySaldoLimit
        ).also {
            viewModel.setVaBankCode(it)
        }
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SEND_SELECT_PAYMENT_METHOD, eventProperty)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_PAYMENT_SEND_SELECT_PAYMENT_METHOD, "payment_checkout_history")
    }

    private fun topupSaldoInitiated() {
        val sourceLink = SourceLink(
            context = this,
            "${SaldoSignalHandler.saldoLink}?${AnalyticsConst.ENTRY_POINT}=${AnalyticsConst.PPOB}"
        )
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_SALDO_IN)) {
                    PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.PPOB)
                } else {
                    startActivity(TopupSaldoActivity.createIntent(this))
                }
                FirebaseCrashlytics.getInstance().recordException(it)
            }
        )
    }

    override fun understoodFeeInfo() {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_FEE_INFO_PAGE_CLICK,
            PropBuilder().apply {
                put(
                    AnalyticsConst.ENTRY_POINT,
                    if (viewModel.isPaymentIn()) AnalyticsConst.PAYMENT_REQUEST else AnalyticsConst.PAYMENT_SEND
                )
                put(
                    AnalyticsConst.TYPE,
                    AnalyticsConst.UNDERSTAND
                )
            }
        )
    }

    //endregion

    override fun onDestroy() {
        Utilities.removeSpan(binding.tvKycUpgradeInfo)
        super.onDestroy()
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    private val startPaymentPinActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                // open payment order detail
                proceedToDisburse()

            }
        }

    private fun handlePaymentHistoryDetail(orderId: String?, customerId: String?) {
        val sourceLink = SourceLink(
            context = this,
            PaymentHistoryDetailsSignalHandler.getPaymentHistoryLink(
                orderId,
                TYPE_PAY_OUT,
                customerId
            )
        )
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = { finish() },
            onFailure = {
                finish()
            }
        )
    }

    private fun setSaldoErrorCases() {
        with(binding.includePaymentMethod) {
            if (viewModel.selectedPaymentMethod?.code == SALDO) {
                if (viewModel.checkDailySaldoLimit() || viewModel.checkMonthlySaldoLimit()) {
                    includeLayout.root.showView()
                    includeLayout.clLayout.background =
                        getDrawableCompat(R.drawable.white_background_radius_8)
                    tvPaymentAmount.setTextColor(getColorCompat(R.color.black_60))
                    includeLayout.tvMessage.text =
                        if (viewModel.checkDailySaldoLimit()) getString(R.string.saldo_daily_limit_reached)
                        else getString(R.string.saldo_monthly_limit_reached)
                    includeLayout.tvMessage.setDrawable(left = R.drawable.ic_error_triangle)
                    includeLayout.btnCheck.hideView()
                    includeLayout.tvMessage.setTextAppearance(this@PaymentCheckoutActivity, R.style.Body3)
                    includeLayout.tvMessage.setTextColor(getColorCompat(R.color.black_60))
                    return
                }
                if (viewModel.selectedPaymentMethod?.saldoBalance.orNil < (viewModel.amount.toDouble() + viewModel.adminFee.orNil - viewModel.discount.orNil - viewModel.loyaltyDiscount?.tierDiscount.orNil)) {
                    includeLayout.root.showView()
                    includeLayout.clLayout.background =
                        getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
                    tvPaymentAmount.setTextColor(getColorCompat(R.color.red_60))
                    includeLayout.tvMessage.text = getString(R.string.saldo_balance_error)
                    includeLayout.tvMessage.setDrawable(left = 0)
                    includeLayout.btnCheck.showView()
                    includeLayout.btnCheck.text = getString(R.string.topup_saldo)
                    includeLayout.tvMessage.setTextAppearance(this@PaymentCheckoutActivity,R.style.SubHeading2)
                    includeLayout.tvMessage.setTextColor(getColorCompat(R.color.red_60))
                    includeLayout.btnCheck.setStyleButtonColorFill(
                        this@PaymentCheckoutActivity,
                        R.color.red_80,
                        R.color.white
                    )
                    includeLayout.btnCheck.setOnClickListener { topupSaldoInitiated() }
                }
            } else {
                includeLayout.root.hideView()
            }
        }
    }

    override fun changeUseSaldoReward(useSaldoReward: Boolean) {
    }
}
