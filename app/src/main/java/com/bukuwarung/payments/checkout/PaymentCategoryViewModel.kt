package com.bukuwarung.payments.checkout

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.model.PaymentCategory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class PaymentCategoryViewModel @Inject constructor(private val paymentUseCase: PaymentUseCase) :
    BaseViewModel() {

    sealed class Event {
        data class ShowCategoryList(val list: List<PaymentCategory>) : Event()
        data class ShowLoader(val showLoader: Boolean) : Event()
        object ShowServerDown : Event()
    }

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    fun getPaymentCategoryList(disbursableType: String) = viewModelScope.launch {
        eventStatus.value = Event.ShowLoader(true)
        withContext(Dispatchers.IO) {
            when (val result = paymentUseCase.getPaymentCategoryList(disbursableType)) {
                is ApiSuccessResponse -> {
                    withContext(Dispatchers.Main) {
                        eventStatus.value = Event.ShowLoader(false)
                        eventStatus.value = Event.ShowCategoryList(result.body)
                    }
                }
                is ApiErrorResponse, is ApiEmptyResponse -> {
                    withContext(Dispatchers.Main) {
                        eventStatus.value = Event.ShowLoader(false)
                        eventStatus.value = Event.ShowServerDown
                    }
                }
            }
        }
    }
}