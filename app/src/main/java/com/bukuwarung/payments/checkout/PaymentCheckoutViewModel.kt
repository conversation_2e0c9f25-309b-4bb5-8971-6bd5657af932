package com.bukuwarung.payments.checkout

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.SALDO
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.domain.payments.RiskUseCase
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentNotePreference
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.math.BigDecimal
import java.util.*
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class PaymentCheckoutViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val riskUseCase: RiskUseCase,
    private val customerUseCase: CustomerUseCase,
    private val businessUseCase: BusinessUseCase,
    private val paymentNotePreference: PaymentNotePreference,
    private val sessionManager: SessionManager,
    private val featurePrefManager: FeaturePrefManager,
    private val onboardingPrefManager: OnboardingPrefManager
) : BaseViewModel() {
    companion object {
        // constant for last failed api call for retry purpose
        private const val API_BANK_ACCOUNTS = 0
        private const val API_PAYMENT_OVERVIEW = 1
        private const val API_CREATE_PAYMENT = 2
        private const val API_FETCH_CATEGORIES = 3
        const val HEALTH_OK = 0
        const val HEALTH_WARNING = 1
        const val HEALTH_ERROR = 2
    }

    sealed class Event {
        data class ShowInitialData(val customerName: String?, val noteList: List<String>) : Event()
        data class ShowSummaryData(
            val formattedAmountReceived: String?,
            val formattedTotal: String?,
            val destinationBankInformation: DestinationBankInformation? = null
        ) : Event()

        data class OnSubmitPaymentInSuccess(val template: String) : Event()
        data class ApiError(
            val isServerError: Boolean, val message: String, val errors: JSONObject?
        ) : Event()
        data class RedirectToMainActivity(val paymentTabEnabled: Boolean) : Event()
        object OnNormalBackPressed : Event()
        object ShowSnackbarError : Event()
        object PaymentTypeChanged : Event()
        data class BookValidationError(val bookName: String?) : Event()
        data class OpenWebView(val url: String, val paymentTabEnabled: Boolean) : Event()
        data class OpenPaymentDetail(val orderId: String, val customerId: String) : Event()
        data class ShowCategories(val categories: List<PaymentCategoryItem>) : Event()
        data class SetSelectedCategory(val category: PaymentCategoryItem) : Event()
        object PaymentLimitsReceived : Event()
    }

    sealed class PaymentMethodEvent {
        data class ShowVaBankCodeData(val paymentMethod: PaymentMethod) : PaymentMethodEvent()
    }

    sealed class ProfileIncompleteEvent {
        object ShowProfileDialog : ProfileIncompleteEvent()
    }

    private val paymentPrefManager = PaymentPrefManager.getInstance()
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private val paymentMethodEventStatus = MutableLiveData<PaymentMethodEvent>()
    val observePaymentMethodEvent: LiveData<PaymentMethodEvent> = paymentMethodEventStatus
    val profileIncompleteEvent: MutableLiveData<ProfileIncompleteEvent> = MutableLiveData()
    private var failedApiState = -1
    private var selectedBankAccount: BankAccount? = null
    private var selectedBankAccountCustomer: BankAccount? = null
    var bookId: String = sessionManager.businessId
    var amount = BigDecimal(0)
        private set
    private var bookEntity: BookEntity? = null
    private var note = "-"
    private var hasSubmit = false
    var vaBankCode = ""
    var loyaltyDiscount: LoyaltyDiscount? = null
        private set
    var receivedAmount: BigDecimal? = null
        private set
    var totalTransfer: BigDecimal? = null
        private set
    var discount: Double? = null
        private set
    var adminFee: Double? = null
        private set
    private var entryPoint = ""
    private var transactionType = ""
    private var merchantBankAccount: List<BankAccount>? = null
    private var from: String? = null
    var paymentInCategory: PaymentCategoryItem? = null
        private set
    var paymentOutCategory: PaymentCategoryItem? = null
        private set
    var paymentInCategories: List<PaymentCategoryItem>? = null
    var paymentOutCategories: List<PaymentCategoryItem>? = null
    var paymentInLimits: PaymentTransactionLimits? = null
        private set
    var paymentOutLimits: PaymentTransactionLimits? = null
        private set

    data class ViewState(
        val showBankLoading: Boolean = false,
        val showLoading: Boolean = false,
        val hasPaymentMethod: Boolean = false,
        val showNominalError: Boolean = false,
        val showUpdateAmountButton: Boolean = false,
        val showSummary: Boolean = false,
        val showError: Boolean = false,
        val hasSubmit: Boolean = false,
        val hasShownBankListCoachmark: Boolean = true,
        val healthState: Int = HEALTH_OK,
        val healthMessage: String? = null,
        val paymentMethodError: Boolean = false,
        val customerName: String? = null,
        val bankAccount: BankAccount? = null,
        val bankAccountCustomer: BankAccount? = null,
        val bookId: String? = null,
        val minimumAmountThreshold: Double = 0.0,
        val discountFeeText: String = "",
        val remainingFreeQuota: Int? = 0,
        val isAboveThresholdReached: Boolean = false,
        val enableButton: Boolean = true,
        val hideEditButton: Boolean = false
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    private var customer: CustomerEntity? = null
    private var customerId: String? = null
    var selectedPaymentMethod: PaymentMethod? = null
    private var paymentType: Int = 0
    private var hideEditButton: Boolean = false

    fun getCustomerId(): String? {
        if (customerId.isNullOrBlank()) {
            customerId = if (currentViewState().customerName.isNotNullOrBlank()) {
                customerUseCase.saveContactAsCustomer(
                    Contact(
                        currentViewState().customerName,
                        "",
                        "",
                        ""
                    )
                )
            } else {
                if (!isPaymentIn()) {
                    Utility.uuid()
                } else {
                    null
                }
            }
        }
        return customerId
    }

    fun getPaymentType() = paymentType

    fun refreshAdminAndDiscountFee() {
        adminFee = null
        discount = null
        loyaltyDiscount = null
    }

    fun init(
        paymentType: Int,
        entryPoint: String,
        transactionType: String,
        cstId: String?,
        bookId: String?,
        from: String?,
        preSelectedCategory: String? = null
    ) = viewModelScope.launch {
        sessionManager.currentBankAccount = null
        <EMAIL> = entryPoint
        <EMAIL> = transactionType
        <EMAIL> = paymentType
        val minAmountThreshold = if (isPaymentIn()) {
            RemoteConfigUtils.getMinimumPaymentAmount()
        } else {
            RemoteConfigUtils.getMinimumPaymentOutAmount()
        }
        viewState.value = currentViewState().copy(minimumAmountThreshold = minAmountThreshold)
        customerId = cstId
        setExitWithoutCompletingPayment()
        customer = customerUseCase.getCustomerById(customerId)
        if (customer != null) setViewState(currentViewState().copy(customerName = customer?.name))
        if (bookId != null) {
            <EMAIL> = bookId
            viewState.value = currentViewState().copy(bookId = bookId)
        }
        setEventStatus(
            Event.ShowInitialData(
                currentViewState().customerName,
                paymentNotePreference.noteList
            )
        )
        checkProfileCompletion()
        if (!isPaymentIn()) {
            selectedPaymentMethod?.run {
                setVaBankCode(this)
            }
        }
        fetchLimits()
        runValidations()
        fetchCategories(preSelectedCategory)
    }

    private fun fetchLimits() = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            if (isPaymentIn()) {
                if (paymentInLimits != null) {
                    eventStatus.postValue(Event.PaymentLimitsReceived)
                    return@withContext
                }
                val result = paymentUseCase.getPaymentInLimits(bookId, customerId.orDefault("customer_id"))
                if (result is ApiSuccessResponse) {
                    paymentInLimits = result.body
                    eventStatus.postValue(Event.PaymentLimitsReceived)
                }
            } else {
                if (paymentOutLimits != null) {
                    eventStatus.postValue(Event.PaymentLimitsReceived)
                    return@withContext
                }
                val result = paymentUseCase.getPaymentOutLimits(bookId, customerId.orDefault("customer_id"))
                if (result is ApiSuccessResponse) {
                    paymentOutLimits = result.body
                    eventStatus.postValue(Event.PaymentLimitsReceived)
                }
            }
        }
    }

    /**
     * Runs 1 validations.
     * This can be used to prioritise handling of validation errors.
     * It should not happen that both errors show at the same time and more important error gets lost
     * in the handling on UI side.
     * Error handling is triggered once all validation responses are received.
     *
     * 1. Checks for valid business name
     */
    fun runValidations(validateBookName: Boolean = true) =
        viewModelScope.launch {
            withContext(Dispatchers.Main) {
                var bookValidation: ApiResponse<Any>? = null
                val bookName = getBook()?.bookName

                coroutineScope {
                    if (validateBookName) {
                        bookName?.let {
                            launch {
                                bookValidation =
                                    riskUseCase.validateBookName(BookValidationRequest(it))
                            }
                        }
                    }
                }
                // We give priority to book validation
                bookValidation?.let { handleBookValidation(it, bookName) }
            }
        }

    private fun fetchCategories(preSelectedCategory: String? = null) = viewModelScope.launch {
        if (isPaymentIn() && paymentInCategories != null) {
            eventStatus.value = Event.ShowCategories(paymentInCategories!!)
        } else if (!isPaymentIn() && paymentOutCategories != null) {
            eventStatus.value = Event.ShowCategories(paymentOutCategories!!)
        } else {
            withContext(Dispatchers.IO) {
                val disbursableType =
                    if (isPaymentIn()) PaymentConst.PaymentRequest else PaymentConst.DisbursementRequest
                when (val result = paymentUseCase.getPaymentCategoryList(disbursableType)) {
                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            val categoryListWithUniqueInfo = mutableListOf<PaymentCategoryItem>()
                            result.body.forEach { category ->
                                category.categoryList.forEach { item ->
                                    if (categoryListWithUniqueInfo.firstOrNull { it.name == item.name } == null) {
                                        categoryListWithUniqueInfo += item
                                    }
                                }
                            }
                            categoryListWithUniqueInfo.sortBy { it.priority }
                            if (isPaymentIn()) {
                                paymentInCategories = categoryListWithUniqueInfo
                            } else {
                                paymentOutCategories = categoryListWithUniqueInfo
                            }
                            eventStatus.value = Event.ShowCategories(categoryListWithUniqueInfo)
                            autoAssignCategory(categoryListWithUniqueInfo, preSelectedCategory)
                        }
                    }
                    is ApiErrorResponse -> handleErrorApi(API_FETCH_CATEGORIES, result.errorMessage)
                    else -> handleErrorApi(API_FETCH_CATEGORIES)
                }
            }
        }
    }

    private fun handleBookValidation(validationResponse: ApiResponse<Any>, bookName: String?) =
        viewModelScope.launch {
            when (validationResponse) {
                is ApiErrorResponse -> {
                    if (validationResponse.statusCode == 422) {
                        val blockedBook = PaymentUtils.parseBlacklistedBookName(validationResponse.errorMessage)
                        eventStatus.value = Event.BookValidationError(blockedBook ?: bookName)
                        viewState.value = currentViewState().copy(
                            showBankLoading = false, showLoading = false
                        )
                    }
                }
                else -> {
                }
            }
        }

    fun getBook() = businessUseCase.getBusinessById(SessionManager.getInstance().businessId)

    private fun setExitWithoutCompletingPayment() {
        if (entryPoint != AnalyticsConst.PUSH_NOTIF) {
            featurePrefManager.setExitWithoutCompletingPayment(
                true,
                paymentType,
                PaymentExitIntentData(null, customerId, bookId)
            )
            val otherPaymentType =
                if (paymentType == PaymentConst.TYPE_PAYMENT_IN) PaymentConst.TYPE_PAYMENT_OUT else PaymentConst.TYPE_PAYMENT_IN
            featurePrefManager.setExitWithoutCompletingPayment(false, otherPaymentType)
        }
    }

    fun changePaymentType(type: Int) {
        paymentType = type
        eventStatus.value = Event.PaymentTypeChanged
        checkProfileCompletion()
        setExitWithoutCompletingPayment()
        if (!isPaymentIn()) {
            selectedPaymentMethod?.run {
                setVaBankCode(this)
            }
        }
        fetchLimits()
        fetchCategories()
    }

    fun onContactSelected(contact: Contact) {
        customerId = contact.customerId
        selectedBankAccountCustomer = contact.banks?.firstOrNull()
        viewState.value = currentViewState().copy(customerName = contact.name)
        checkProfileCompletion()
    }

    fun checkProfileCompletion() {
        bookEntity = businessUseCase.getBusinessById(this.bookId)
        bookEntity?.run {
            if (!this.hasCompletedProfileWithoutOwnerName()) {
                profileIncompleteEvent.value = ProfileIncompleteEvent.ShowProfileDialog
            } else getBankAccounts()
        }
    }

    fun onNoteChanged(note: String) {
        this.note = note
    }

    fun onAmountChanged(editedAmount: Long) {
        val belowThreshold = isBelowThreshold(editedAmount)
        amount = BigDecimal(editedAmount)
        selectedPaymentMethod = null
        vaBankCode = ""
        val isAboveThreshold = isAboveThreshold(editedAmount)
        refreshAdminAndDiscountFee()
        if (belowThreshold || isAboveThreshold) {
            viewState.value = currentViewState().copy(
                showNominalError = belowThreshold || isAboveThreshold,
                isAboveThresholdReached = isAboveThreshold,
                hasPaymentMethod = false,
                discountFeeText = ""
            )
        } else {
            viewState.value = currentViewState().copy(
                showNominalError = false,
                isAboveThresholdReached = false,
                hasPaymentMethod = false,
                discountFeeText = ""
            )
            healthCheck()
            calculateSummary()
        }
    }

    fun isBelowThreshold(amount: Long) = amount.toDouble() < viewState.value!!.minimumAmountThreshold

    fun isAboveThreshold(amount: Long): Boolean {
        val limits = if (isPaymentIn()) paymentInLimits else paymentOutLimits
        if (limits?.remainingDailyTrxLimit == null || limits.perTrxLimit == null) return false
        // Whitelist limits should be null for other users(SUPREME/non-whitelisted ADVANCED users)
        if (limits.whitelistLimits?.remainingTrxAmountLimit != null) {
            return (amount > limits.whitelistLimits.remainingTrxAmountLimit.orNil)
        }
        return (amount > limits.remainingDailyTrxLimit.orNil) || (amount > limits.perTrxLimit.orNil)
    }

    private fun getBankAccounts() = viewModelScope.launch {
        val result: ApiResponse<List<BankAccount>>
        if (isPaymentIn()) {
            if (merchantBankAccount == null) {
                viewState.value = currentViewState().copy(
                    showBankLoading = true,
                    showLoading = false,
                    showError = false
                )
                result = paymentUseCase.getMerchantBankAccounts(bookId)
            } else {
                handleSuccessBankAccount(merchantBankAccount)
                return@launch
            }
        } else {
            if (customerId.isNullOrBlank()) {
                setViewState(currentViewState().copy(bankAccountCustomer = selectedBankAccountCustomer))
                return@launch
            }
            viewState.value = currentViewState().copy(
                showBankLoading = true,
                showLoading = false,
                showError = false
            )
            result = paymentUseCase.getCustomerBankAccounts(bookId, customerId!!)
        }
        when (result) {
            is ApiSuccessResponse -> handleSuccessBankAccount(result.body)
            is ApiErrorResponse -> {
                // NOTE: Don't show error if last event was to show book validation error
                if (eventStatus.value !is Event.BookValidationError) {
                    handleErrorApi(API_BANK_ACCOUNTS, result.errorMessage)
                }
            }
            else -> handleErrorApi(API_BANK_ACCOUNTS)
        }
    }

    private fun handleSuccessBankAccount(list: List<BankAccount>?) {
        val size = list?.size ?: 0
        val hasShownBankListCoachmark =
            onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTOR_INPUT_BANK_ACCOUNT)
        if (size > 0)  {
            /*
                Note: customer bank account is coming from local db which might not have the latest data
                due to failure in sync. So here we update any already selected customer bank account.
             */
            list?.find { it.accountNumber == getSelectedBankAccount()?.accountNumber }?.let {
                getSelectedBankAccount()?.apply {
                    isSelected = it.isSelected
                    flag = it.flag
                    message = it.message
                    isQrisBank = it.isQrisBank
                }
            }
            if(getSelectedBankAccount() == null) setSelectedBankAccount(list?.firstOrNull())
        } else {
            if (isPaymentIn()) {
                selectedBankAccount = null
                viewState.value = currentViewState().copy(bankAccount = selectedBankAccount)
            } else {
                selectedBankAccountCustomer = null
                viewState.value = currentViewState().copy(bankAccountCustomer = selectedBankAccountCustomer)
            }
        }
        if (isPaymentIn()) {
            featurePrefManager.setHasBankAccount(size > 0, sessionManager.businessId)
            merchantBankAccount = list
        }
        viewState.value = currentViewState().copy(
            showBankLoading = false,
            hasShownBankListCoachmark = hasShownBankListCoachmark
        )
        if (isPaymentIn() || currentViewState().hasPaymentMethod) healthCheck()
    }

    fun setSelectedBankAccount(bankAccount: BankAccount?) = viewModelScope.launch {
        if (isPaymentIn()) {
            selectedBankAccount = bankAccount
            viewState.value = currentViewState().copy(bankAccount = selectedBankAccount)
        } else {
            selectedBankAccountCustomer = bankAccount
            if (currentViewState().customerName.isNullOrBlank()) {
                viewState.value = currentViewState().copy(customerName = bankAccount?.accountHolderName)
                hideEditButton = true
                customerUseCase.saveContactAsCustomer(
                    Contact(
                        currentViewState().customerName,
                        "",
                        "",
                        customerId
                    )
                )
            } else {
                hideEditButton = false
            }
            viewState.value = currentViewState().copy(
                bankAccountCustomer = selectedBankAccountCustomer,
                hideEditButton = hideEditButton
            )
        }
        healthCheck()
    }

    fun clearSelectedPaymentMethod() {
        selectedPaymentMethod = null
        vaBankCode = ""
        viewState.value = currentViewState().copy(hasPaymentMethod = false)
    }

    fun setVaBankCode(paymentMethod: PaymentMethod) {
        selectedPaymentMethod = paymentMethod
        vaBankCode = paymentMethod.code.orEmpty()
        viewState.value =
            currentViewState().copy(
                hasPaymentMethod = true,
                paymentMethodError = false,
                discountFeeText = ""
            )
        paymentMethodEventStatus.value = PaymentMethodEvent.ShowVaBankCodeData(paymentMethod)
        adminFee = null
        healthCheck()
    }

    fun healthCheck() = viewModelScope.launch {
        val belowThreshold = amount.toDouble() < viewState.value?.minimumAmountThreshold.orNil
        val isAboveThreshold = isAboveThreshold(amount.toLong())
         val validForOverview = getSelectedBankAccount() != null &&
                !belowThreshold && !isAboveThreshold &&
                (currentViewState().hasPaymentMethod || paymentType == PaymentConst.TYPE_PAYMENT_IN)
        if (validForOverview) {
            if (isPaymentIn()) getPaymentInOverview() else getPaymentOutOverview()
        }
        if (amount < BigDecimal(viewState.value!!.minimumAmountThreshold) || getSelectedBankAccount() == null ||
            (!currentViewState().hasPaymentMethod && paymentType == PaymentConst.TYPE_PAYMENT_OUT)
        ) return@launch
        doHealthCheck()
        if (currentViewState().healthState != HEALTH_ERROR)
            viewState.value = currentViewState().copy(showBankLoading = false, showLoading = false)
    }

    private suspend fun doHealthCheck() {
        val request = PaymentHealthCheckRequest(
            moneyOutChannels = listOf(getSelectedBankAccount()?.bankCode ?: "")
        )
        when (val response = paymentUseCase.doHealthCheck(request)) {
            is ApiSuccessResponse -> {
                viewState.value = currentViewState().copy(healthState = HEALTH_OK)
                iterateProviderHealthResponse(response.body.providers)
                if (currentViewState().healthState != HEALTH_ERROR) {
                    iterateMoneyOutHealthResponse(response.body.moneyOut)
                    if (!isPaymentIn() && currentViewState().healthState != HEALTH_ERROR) {
                        iterateMoneyInHealthResponse(response.body.moneyIn?.paymentOut)
                    }
                }
            }
            else -> {}
        }
        if (currentViewState().healthState != HEALTH_ERROR) {
            val calendar = Calendar.getInstance()
            val hourNow = calendar.get(Calendar.HOUR_OF_DAY)
            if (hourNow < 7 || hourNow >= 23)
                viewState.value = currentViewState().copy(healthState = HEALTH_WARNING)
        }
    }

    private fun getPaymentInOverview() = viewModelScope.launch {
        customerId ?: return@launch
        if (!currentViewState().showLoading) viewState.value =
            currentViewState().copy(showLoading = true, showError = false)
        val request = PaymentOverviewRequest(
            amount = amount,
            description = note,
            bankAccountId = getSelectedBankAccount()?.bankAccountId,
            accountId = bookId,
            customerId = customerId!!,
            customerName = customer?.name.orEmpty(),
            note = note,
            bankCode = getSelectedBankAccount()!!.bankCode!!
        )
        when (val result =
            paymentUseCase.getPaymentInOverview(request.accountId, customerId!!, request)) {
            is ApiSuccessResponse -> {
                discount = result.body.discountFee
                adminFee = result.body.fee
                loyaltyDiscount = result.body.loyaltyDiscount
                viewState.value = currentViewState().copy(
                    showSummary = true,
                    showLoading = false,
                    discountFeeText = result.body.discountFeeText.orEmpty(),
                    remainingFreeQuota = result.body.remainingFreeQuota,
                    enableButton = true
                )
                receivedAmount = result.body.amount
                calculateSummary(result.body.destinationBankInformation)
            }
            is ApiErrorResponse -> handleErrorApi(API_PAYMENT_OVERVIEW, result.errorMessage)
            else -> handleErrorApi(API_PAYMENT_OVERVIEW)
        }
    }

    private fun getPaymentOutOverview() = viewModelScope.launch {
        customerId ?: return@launch
        if (!currentViewState().showLoading) viewState.value =
            currentViewState().copy(showLoading = true, showError = false)
        val request = DisbursementOverviewRequest(
            amount = amount,
            description = note,
            customerBankAccountId = getSelectedBankAccount()?.bankAccountId,
            accountId = bookId,
            customerId = customerId!!,
            customerName = customer?.name.orEmpty(),
            vaBankCode = vaBankCode
        )
        when (val result =
            paymentUseCase.getPaymentOutOverview(request.accountId, customerId!!, request)) {
            is ApiSuccessResponse -> {
                viewState.value = currentViewState().copy(
                    showSummary = true,
                    showLoading = false,
                    discountFeeText = result.body.discountFeeText.orEmpty(),
                    remainingFreeQuota = result.body.remainingFreeQuota,
                    enableButton = true
                )
                discount = result.body.discountFee.orNil
                adminFee = result.body.fee
                loyaltyDiscount = result.body.loyaltyDiscount
                totalTransfer = result.body.totalTransfer
                calculateSummary(result.body.destinationBankInformation)
            }
            is ApiErrorResponse -> handleErrorApi(API_PAYMENT_OVERVIEW, result.errorMessage)
            else -> handleErrorApi(API_PAYMENT_OVERVIEW)
        }
    }

    fun calculateSummary(destinationBankInformation: DestinationBankInformation? = null) {
        val formattedAmount = Utility.formatAmount(amount.toDouble())
        if (adminFee == null) {
            eventStatus.value = Event.ShowSummaryData( formattedAmount, formattedAmount)
            return
        }
        if (isPaymentIn()) {
            val amountReceived = amount.toDouble() + discount.orNil + loyaltyDiscount?.tierDiscount.orNil - adminFee.orNil
            eventStatus.value = Event.ShowSummaryData(
                Utility.formatAmount(amountReceived),
                formattedAmount, destinationBankInformation
            )
        } else {
            val total = amount.toDouble() - discount.orNil - loyaltyDiscount?.tierDiscount.orNil + adminFee.orNil
            eventStatus.value = Event.ShowSummaryData(
                formattedAmount,
                Utility.formatAmount(total), destinationBankInformation
            )
        }
    }

    fun getTotalAmountForMoneyOut(): Double {
        return adminFee?.let {
            amount.toDouble() - discount.orNil - loyaltyDiscount?.tierDiscount.orNil + it
        } ?: run {
            amount.toDouble()
        }
    }

    private fun iterateMoneyInHealthResponse(list: List<HealthStatus>?) {
        if (list != null) {
            for (status in list) {
                if (vaBankCode == status.name && false == status.enabled) {
                    viewState.value = currentViewState().copy(
                        healthState = HEALTH_ERROR,
                        healthMessage = status.message
                    )
                }
            }
        }
    }

    private fun iterateProviderHealthResponse(list: List<HealthStatus>?) {
        var currentMessage = ""
        if (list != null) {
            for (status in list) {
                if (true == status.enabled) return
                else currentMessage = status.message ?: ""
            }
        }
        viewState.value =
            currentViewState().copy(healthState = HEALTH_ERROR, healthMessage = currentMessage)
    }

    private fun iterateMoneyOutHealthResponse(list: List<HealthStatus>?) {
        if (list != null) {
            for (status in list) {
                if (false == status.enabled) {
                    viewState.value = currentViewState().copy(
                        healthState = HEALTH_ERROR,
                        healthMessage = status.message
                    )
                }
            }
        }
    }

    fun onBackPressed() {
        if (isPaymentIn() && currentViewState().hasSubmit) {
            eventStatus.value = Event.RedirectToMainActivity(featurePrefManager.paymentTabEnabled())
        } else {
            eventStatus.value = Event.OnNormalBackPressed
        }
    }

    fun setHasShownInputBankCoachmark() {
        onboardingPrefManager.setHasFinishedForId(OnboardingPrefManager.TUTOR_INPUT_BANK_ACCOUNT)
    }

    fun createDisbursement(from:String?) {
        val bankAccountError = getSelectedBankAccount() == null
        val paymentMethodError =
            !currentViewState().hasPaymentMethod && paymentType == PaymentConst.TYPE_PAYMENT_OUT
        if (amount < BigDecimal(viewState.value!!.minimumAmountThreshold) || bankAccountError || paymentMethodError) {
            viewState.value = currentViewState().copy(
                paymentMethodError = paymentMethodError,
                showNominalError = amount < BigDecimal(viewState.value!!.minimumAmountThreshold)
            )
            eventStatus.value = Event.ShowSnackbarError
            return
        }
        var categoryId: String? = null
        var recordIn: String? = null
        if (transactionType == AppConst.CASH) {
            categoryId =
                if (isPaymentIn()) AppConst.CATEGORY_CASH_IN else AppConst.CATEGORY_CASH_OUT
            recordIn = PaymentConst.RECORD_IN_CASH
        } else {
            recordIn = PaymentConst.RECORD_IN_DEBT
        }
        if (customerId.isNullOrBlank()) {
            customerId = customerUseCase.saveContactAsCustomer(
                Contact(
                    currentViewState().customerName,
                    "",
                    "",
                    ""
                )
            )
        }
        if (isPaymentIn()) createInDisbursement(categoryId, recordIn, from)
        else createOutDisbursement(categoryId, recordIn, from)
    }

    private fun createInDisbursement(categoryId: String?, recordIn: String, from: String?) =
        viewModelScope.launch {
            if (customerId == null) return@launch
            if (!currentViewState().showLoading) viewState.value =
                currentViewState().copy(showLoading = true, showError = false)
            FeaturePrefManager.getInstance()
                .setExitWithoutCompletingPayment(false, PaymentConst.TYPE_PAYMENT_IN, null)
            val result = paymentUseCase.requestPayment(
                bookId,
                customerId!!,
                PaymentCollection.newCollectionRequest(
                    amount = amount.toLong(),
                    bankAccountId = getSelectedBankAccount()?.bankAccountId,
                    description = note,
                    customerName = currentViewState().customerName,
                    extras = PaymentExtras(
                        accounting = PaymentAccountingExtras(categoryId = categoryId),
                        recordIn = recordIn
                    ),
                    paymentCategoryId = paymentInCategory?.paymentCategoryId,
                    receivedAmount = receivedAmount
                )
            )

            when (result) {
                is ApiSuccessResponse -> {
                    paymentPrefManager.setLastPaymentType(paymentType)
                    val key = PaymentUtils.getKeyForSelectedCategoryId(paymentType)
                    paymentPrefManager.setSelectedPaymentCategoryId(
                        key, paymentInCategory?.paymentCategoryId
                    )
                    val props = AppAnalytics.PropBuilder()
                    props.put("entry_point", entryPoint)
                    props.put("inwards_amount", amount)
                    props.put("bank", getSelectedBankAccount()?.bankCode)
                    props.put(AnalyticsConst.USE_CASE,paymentInCategory?.title)
                    props.put("use_case_category",paymentInCategory?.name)
                    props.put(AnalyticsConst.PLATFORM_FEE, adminFee.orNil - discount.orNil - loyaltyDiscount?.tierDiscount.orNil)
                    props.put(AnalyticsConst.TRANSACTION_AMOUNT, amount.minus(adminFee?.toBigDecimal() ?: BigDecimal(0)))
                    props.put(AnalyticsConst.TOTAL_AMOUNT, amount)
                    props.put(AnalyticsConst.DISCOUNT, discount)
                    props.put(AnalyticsConst.GROSS_FEE, adminFee.orNil)
                    if(loyaltyDiscount?.tierDiscount.orNil.isNotZero().isTrue)
                        props.put(AnalyticsConst.LOYALTY_DISCOUNT, loyaltyDiscount?.tierDiscount.orNil)

                    if (from.isNotNullOrEmpty()) {
                        props.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                    }
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_PAYMENT_REQUEST_CREATED,
                        props
                    )

                    AppConfigManager.getInstance().setEntryPointForId(AnalyticsConst.HomePage.HOMEPAGE, false)
                    viewState.value = currentViewState().copy(showLoading = false, hasSubmit = true)
                    val message = result.body.template?.getPaymentSharingText(customer)
                    message?.run {
                        eventStatus.value = Event.OnSubmitPaymentInSuccess(message)
                    }
                    AppConfigManager.getInstance().sethasPendingPayment(true)
                    hasSubmit = true
                    paymentNotePreference.saveNote(note)
                }
                is ApiErrorResponse -> handleErrorApi(API_CREATE_PAYMENT, result.errorMessage)
                else -> handleErrorApi(API_CREATE_PAYMENT)
            }
        }

    private fun createOutDisbursement(categoryId: String?, recordIn: String, from: String?) {
        if (customerId == null) return
        viewModelScope.launch {
            viewState.value = currentViewState().copy(showLoading = true, showError = false)
            FeaturePrefManager.getInstance()
                .setExitWithoutCompletingPayment(false, PaymentConst.TYPE_PAYMENT_OUT, null)
            val extras = HashMap<String, String>()
            extras["transaction_type"] = transactionType
            val request = DisbursementOverviewRequest(
                amount = amount,
                description = note,
                customerBankAccountId = getSelectedBankAccount()?.bankAccountId,
                vaBankCode = vaBankCode,
                accountId = bookId,
                customerId = customerId!!,
                customerName = currentViewState().customerName ?: "",
                extras = PaymentExtras(
                    accounting = PaymentAccountingExtras(categoryId = categoryId),
                    recordIn = recordIn
                ),
                paymentCategoryId = paymentOutCategory?.paymentCategoryId,
                totalTransfer = totalTransfer
            )
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.ENTRY_POINT, entryPoint)
            prop.put("bank", vaBankCode)
            prop.put(AnalyticsConst.USE_CASE,paymentOutCategory?.title)
            prop.put(AnalyticsConst.CATEGORY,paymentOutCategory?.name)
            prop.put(AnalyticsConst.PLATFORM_FEE, adminFee.orNil - discount.orNil - loyaltyDiscount?.tierDiscount.orNil)
            prop.put(AnalyticsConst.TRANSACTION_AMOUNT, amount)
            prop.put(AnalyticsConst.TOTAL_AMOUNT, amount.plus(adminFee?.toBigDecimal() ?: BigDecimal(0)))
            prop.put(AnalyticsConst.DISCOUNT, discount)
            prop.put(AnalyticsConst.GROSS_FEE, adminFee.orNil)
            if(loyaltyDiscount?.tierDiscount.orNil.isNotZero().isTrue)
                prop.put(AnalyticsConst.LOYALTY_DISCOUNT, loyaltyDiscount?.tierDiscount.orNil)

            if (from.isNotNullOrEmpty()) {
                prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
            }

            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_PAYMENT_SEND_CREATED,
                prop

            )
            AppConfigManager.getInstance().setEntryPointForId(AnalyticsConst.HomePage.HOMEPAGE, false)
            when (val result = paymentUseCase.createDisbursement(bookId, customerId!!, request)) {
                is ApiSuccessResponse -> {
                    paymentPrefManager.setLastPaymentType(paymentType)
                    paymentNotePreference.saveNote(note)
                    val key = PaymentUtils.getKeyForSelectedCategoryId(paymentType)
                    paymentPrefManager.setSelectedPaymentCategoryId(
                        key,
                        paymentOutCategory?.paymentCategoryId
                    )
                    if (selectedPaymentMethod?.code == SALDO) {
                        eventStatus.value = Event.OpenPaymentDetail(
                            result.body.disbursementId, result.body.customerId
                        )
                    } else {
                        val paymentUrl =
                            result.body.checkoutUrl ?: result.body.paymentInstructionsUrl
                        paymentUrl?.let {
                            eventStatus.value = Event.OpenWebView(
                                it, featurePrefManager.paymentTabEnabled()
                            )
                        }
                    }
                }
                is ApiErrorResponse -> handleErrorApi(API_CREATE_PAYMENT, result.errorMessage)
                else -> handleErrorApi(API_CREATE_PAYMENT)
            }
        }
    }

    fun reloadData() {
        when (failedApiState) {
            API_BANK_ACCOUNTS -> getBankAccounts()
            API_PAYMENT_OVERVIEW -> healthCheck()
            API_CREATE_PAYMENT -> createDisbursement(null)
            else -> {}
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(vs: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = vs
    }

    private suspend fun handleErrorApi(failedApi: Int, message: String = "") =
        withContext(Dispatchers.Main) {
            val isServerError =
                message.isNotBlank() && message != AppConst.NO_INTERNET_ERROR_MESSAGE
            val jsonMessage = try { JSONObject(message) } catch (ex: Exception) { null }
            eventStatus.value = Event.ApiError(isServerError, message, jsonMessage)
            if (failedApi == API_PAYMENT_OVERVIEW) {
                viewState.value = currentViewState().copy(showBankLoading = false, showLoading = false, enableButton = false)
            } else {
                viewState.value = currentViewState().copy(showBankLoading = false, showLoading = false)
            }
            failedApiState = failedApi
        }

    fun isPaymentIn(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_IN

    fun getSelectedBankAccount() = if (isPaymentIn()) selectedBankAccount else selectedBankAccountCustomer

    fun getSelectedCategory() = if (isPaymentIn()) paymentInCategory else paymentOutCategory

    fun setPaymentCategory(paymentCategory: PaymentCategoryItem) {
        if (isPaymentIn()) {
            this.paymentInCategory = paymentCategory
        } else {
            this.paymentOutCategory = paymentCategory
        }
    }

    /**
     * This method gets categories from the local cache and set it to previously selected category
     * If there is no previously selected category, we assign to default category(first item in the list for now)
     */
    private fun autoAssignCategory(
        categories: List<PaymentCategoryItem>, preSelectedCategory: String? = null
    ) {
        val key = PaymentUtils.getKeyForSelectedCategoryId(paymentType)
        val prevCategoryId = PaymentPrefManager.getInstance().getSelectedPaymentCategoryId(key)
        val preSelectedCategoryObj = categories.firstOrNull { it.name == preSelectedCategory }
        val prevCategory = categories.firstOrNull { it.paymentCategoryId == prevCategoryId }
        val selectCategory = when {
            preSelectedCategoryObj != null -> preSelectedCategoryObj
            prevCategory != null -> prevCategory
            else -> categories[0]
        }
        setPaymentCategory(selectCategory)
        eventStatus.value = Event.SetSelectedCategory(selectCategory)
    }

    fun checkDailySaldoLimit() = (amount.toDouble() + adminFee.orNil - discount.orNil - loyaltyDiscount?.tierDiscount.orNil) > selectedPaymentMethod?.dailySaldoLimit.orNil
    fun checkMonthlySaldoLimit() = (amount.toDouble() + adminFee.orNil - discount.orNil - loyaltyDiscount?.tierDiscount.orNil) > selectedPaymentMethod?.monthlySaldoLimit.orNil
}
