package com.bukuwarung.payments

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.app.ActivityCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.databinding.ActivityCustomerListBinding
import com.bukuwarung.payments.adapters.CustomerListAdapter
import com.bukuwarung.payments.adapters.CustomerListAdapter.OnCustomerClickListener
import com.bukuwarung.payments.adapters.CustomerPhoneListAdapter
import com.bukuwarung.payments.utils.InjectorUtils
import com.bukuwarung.payments.viewmodels.CustomerListViewModel
import com.bukuwarung.session.User
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.PermissonUtil
import com.google.firebase.crashlytics.FirebaseCrashlytics

class CustomerListActivity : AppActivity() {
    private val viewModel: CustomerListViewModel by viewModels {
        InjectorUtils.provideCustomerListViewModelFactory(this)
    }
    private lateinit var binding: ActivityCustomerListBinding
    private var type = 0
    private val entryPoint by lazy {
        intent?.getStringExtra(KEY_ENTRY_POINT) ?: AnalyticsConst.LAINNYA
    }

    // means this list only show list of customer with phone number, not their debt
    private val isPhone by lazy {
        intent?.getBooleanExtra(KEY_IS_PHONE, false) ?: false
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_customer_list)

        binding.apply {
            vm = viewModel
            lifecycleOwner = this@CustomerListActivity
        }
        val isFromPaymentTab = entryPoint == AnalyticsConst.PEMBAYARAN
        try {
            val typeString = intent.getStringExtra(KEY_PAYMENT_TYPE)
            if (typeString != null) {
                try {
                    type = Integer.parseInt(typeString)
                } catch (e: Exception) {
                    // do nothing
                }
            } else {
                type = intent.getIntExtra(KEY_PAYMENT_TYPE, 0)
            }
        } catch (e: Exception) {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put("exception", e.toString())
            AppAnalytics.trackEvent("payment_create_cst_error", propBuilder)
            NotificationUtils.alertToast(resources.getString(R.string.error_default))
            e.printStackTrace()
            finish()
        }
        if (isPhone) {
            val adapter = CustomerPhoneListAdapter { phone ->
                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.CUSTOMER_PHONE, phone).put(AnalyticsConst.TYPE,entryPoint)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_ADD_CUSTOMER_PHONEBOOK, prop,false, false, false)
                setResult(Activity.RESULT_OK, Intent().apply { putExtra(PHONE_NUMBER, phone) })
                finish()
            }
            binding.addContactGroup.visibility = View.GONE
            binding.rvCustomers.adapter = adapter
            viewModel.customers.observe(this, Observer {
                adapter.setData(it)
            })
            viewModel.filteredCustomers.observe(this, Observer {
                adapter.setData(it)
            })
        } else {
            val adapter = CustomerListAdapter(OnCustomerClickListener {
                var id: String? = it.customerId
                var bookId: String? = it.bookId
                if (id.isNullOrBlank()) {
                    AppAnalytics.trackEvent(if (isFromPaymentTab) AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER_PHONEBOOK else "payment_lainnya_add_customer_phonebook")
                    id = CustomerRepository.getInstance(applicationContext).saveCustomer(it.name, it.phone)
                    viewModel.reload()
                } else {
                    AppAnalytics.trackEvent(if (isFromPaymentTab) AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_SELECT_CUSTOMER else "payment_lainnya_select_customer")
                }
                if (bookId.isNullOrBlank()) {
                    bookId = currentBook?.bookId ?: User.getBusinessId()
                }
                id?.run {
                    if (isPhone) {
                        setResult(Activity.RESULT_OK, Intent().apply { putExtra(PHONE_NUMBER, it.phone) })
                        finish()
                    }
                }
            })
            binding.rvCustomers.adapter = adapter
            registerObservers(adapter)
        }
        binding.apply {
            setUpToolbarWithHomeUp(toolbar)
            buttonAddContact.setOnClickListener {
                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.TYPE,entryPoint)
                if(isPhone) {
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_ADD_CUSTOMER_MANUAL, prop)
                }
                if (currentBook != null) {
                    if(isFromPaymentTab)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_ADD_CUSTOMER_MANUAL, prop)
                    else if(entryPoint == AnalyticsConst.LAINNYA)
                        AppAnalytics.trackEvent("payment_lainnya_add_customer_manual")
                    AddContactDialog.show(supportFragmentManager, currentBook?.bookId!!, type, entryPoint) { customerId, phone ->
                        prop.put(AnalyticsConst.CUSTOMER_PHONE,phone)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_SAVE_CUSTOMER_MANUAL, prop)
                        if(isPhone) {
                            setResult(Activity.RESULT_OK, Intent().apply { putExtra(PHONE_NUMBER, phone) })
                            finish()
                        }
                    }
                } else {
                    NotificationUtils.alertError("Tidak dapat membuka halaman tambah pelanggan, silakan coba restart aplikasi atau perangkat Anda.")
                    FirebaseCrashlytics.getInstance().log("payment_customer_null_book")
                }
            }
        }


        requestContactPermissions()
    }

    private fun registerObservers(adapter: CustomerListAdapter) {
        viewModel.customers.observe(this, Observer {
            val registeredCustomers = it.filter { customer -> customer.customerId.isNullOrBlank().not() }.distinctBy { cst ->
                cst.name
            }
            val passedDueDateCustomers = it.filter { customer -> customer.isDueDatePassed }.distinctBy { cst ->
                cst.name
            }
            val unregisteredCustomers = it.filter { customer -> customer.customerId.isNullOrBlank() }.toMutableList()

            registeredCustomers.forEach {
                val idx = unregisteredCustomers.indexOfFirst { cst -> cst.phone == it.phone }
                if (idx >= 0)
                    unregisteredCustomers.removeAt(idx)
            }
            passedDueDateCustomers.forEach {
                val idx = unregisteredCustomers.indexOfFirst { cst -> cst.phone == it.phone }
                if (idx >= 0)
                    unregisteredCustomers.removeAt(idx)
            }

            val adapterItems = CustomerListAdapter.generateItems(getString(R.string.activity_customer_list_label_over_due_date), passedDueDateCustomers) +
                    CustomerListAdapter.generateItems(getString(R.string.label_all), registeredCustomers) +
                    CustomerListAdapter.generateItems(getString(R.string.activity_customer_add_from_contact), unregisteredCustomers)

            adapter.submitList(adapterItems)
            adapter.notifyDataSetChanged()
            binding.rvCustomers.scrollToPosition(0)
        })

        viewModel.filteredCustomers.observe(this, Observer {
            adapter.submitList(CustomerListAdapter.generateItems(customers = it))
        })
    }

    private fun requestContactPermissions() {
        if (!PermissonUtil.hasContactPermission()) {
            AppAnalytics.trackEvent("request_contact_permission")
            ActivityCompat.requestPermissions(this, PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && Build.VERSION.SDK_INT >= 23) {
            if (PermissonUtil.hasContactPermission()) {
                viewModel.loadContacts()
                return
            }
            Toast.makeText(this, this.getString(R.string.alert_deny_contact_permission), Toast.LENGTH_LONG).show()
        }
    }

    companion object {
        private const val KEY_PAYMENT_TYPE = "key_payment_type"
        private const val KEY_ENTRY_POINT = "key_entry_point"
        private const val KEY_IS_PHONE = "key_is_phone"
        const val PHONE_NUMBER = "phoneNumber"

        @JvmStatic
        fun createIntent(context: Context, paymentType: Int, entryPoint: String, isPhone: Boolean = false): Intent {
            return Intent(context, CustomerListActivity::class.java).also {
                it.putExtras(bundleOf(
                        Pair(KEY_PAYMENT_TYPE, paymentType.toString()),
                        Pair(KEY_ENTRY_POINT, entryPoint),
                        Pair(KEY_IS_PHONE, isPhone)
                ))
            }
        }
    }

}
