package com.bukuwarung.payments

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.core.widget.addTextChangedListener
import com.bukuwarung.R
import com.bukuwarung.activities.onboarding.LoginActivity
import com.bukuwarung.activities.onboarding.NewLoginActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.extensions.observeEvent
import com.bukuwarung.base_android.extensions.showAlertDialog
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE
import com.bukuwarung.constants.PaymentConst.TYPE_PAYMENT_IN
import com.bukuwarung.databinding.ActivityPaymentBinding
import com.bukuwarung.payments.adapters.PaymentHistoryAdapter
import com.bukuwarung.payments.data.model.DateFilter
import com.bukuwarung.payments.data.model.PaymentFilterDto
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.utils.RangeDateValidator
import com.bukuwarung.payments.viewmodels.PaymentsViewModel
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.DateTimeUtils.DD_MMM_YYYY
import com.bukuwarung.utils.DateTimeUtils.YYYY_MM_DD
import com.google.android.material.chip.Chip
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.CompositeDateValidator
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import androidx.activity.viewModels

@AndroidEntryPoint
class PaymentsActivity : BaseActivity(),
    PaymentFilterBottomSheet.Callback, PaymentHistoryAdapter.Callback {

    private lateinit var binding: ActivityPaymentBinding
    private lateinit var adapter: PaymentHistoryAdapter
    private var cStartDate:String = EMPTY_STRING
    private var cEndDate:String = EMPTY_STRING
    private var calendarMaxRange = DEFAULT_FILTER_CALENDAR_MAX_RANGE

    private val viewModel: PaymentsViewModel by viewModels()
    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID) ?: "" }
    private val bookId by lazy {
        intent?.getStringExtra(BOOK_ID)
    }
    private val billerCode by lazy { intent?.getStringExtra(BILLER_CODE) }
    private val category by lazy { intent?.getStringExtra(CATEGORY)?:"" }
    private val paymentType by lazy { intent?.getIntExtra(PAYMENT_TYPE, -1) }
    private var hasLogSearchEvent = false
    var paymentFilterDto: PaymentFilterDto? = PaymentFilterDto()

    override fun setViewBinding() {
        binding = ActivityPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setupToolbar()
        adapter = PaymentHistoryAdapter(this)

        viewModel.getRemoteConfigFilters()

        with(binding) {
            chipFilterRange.setOnClickListener {
               showDateRangePicker()
            }

            rvHistory.adapter = adapter

            btnFilter.setOnClickListener {
                // when user tap the filter button to open the filter
                val buttonProp =
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ID, AnalyticsConst.FILTER_OPEN)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_LOOKUP, buttonProp)
                paymentFilterDto ?: return@setOnClickListener
                PaymentFilterBottomSheet.createInstance(supportFragmentManager, paymentFilterDto)
            }

            etSearch.addTextChangedListener {
                viewModel.searchByQuery(it.toString())
                logSearchEvent(it.toString())
            }

            ivHelp.setOnClickListener {
                Utilities.launchBrowser(
                    this@PaymentsActivity,
                    RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
                )
            }
        }

        if (paymentType != null && paymentType!! >= 0) {
            val filterDto = when  {
                paymentType == TYPE_PAYMENT_IN -> PaymentFilterDto(arrayListOf(PaymentFilterDto.TYPE_IN))
                paymentType == TYPE_LISTRIK && category.isNotBlank() -> PaymentFilterDto(
                    arrayListOf(category),
                    arrayListOf(PaymentFilterDto.STATUS_COMPLETED)
                )
                else -> PaymentFilterDto(arrayListOf(PaymentFilterDto.TYPE_OUT))
            }
            viewModel.submitFilter(filterDto)
        }


    }

    override fun subscribeState() {
        viewModel.histories.observe(this) {
            adapter.setData(it)
            adapter.notifyDataSetChanged()
        }

        viewModel.alert.observeEvent(this) {
            showAlertDialog(it)
        }

        viewModel.filterDateState.observe(this) {
            when(it){
                is PaymentsViewModel.DetailEvent.FilterDate ->{
                        cStartDate = it.start
                        cEndDate = it.end
                        updateList(it.start, it.end)
                }

                is PaymentsViewModel.DetailEvent.HistoryFilter -> {
                    calendarMaxRange = viewModel.getCalendarMaxRange()
                    for ((index, mFilter) in it.dateFilter.withIndex()) {
                        binding.cgFilter.addView(createChip(mFilter,index == 0)) // select first
                    }
                }
            }

        }




        viewModel.viewState.observe(this) {
            if (paymentFilterDto != it.paymentFilterDto) {
                paymentFilterDto = it.paymentFilterDto
                viewModel.init(
                    customerId,
                    bookId,
                    startDate = cStartDate,
                    endDate = cEndDate,
                    filterDto = paymentFilterDto?:PaymentFilterDto(),
                    billerCode
                )
            }
            updateFilterButtonState()
            with(binding) {
                progressBar.visibility = it.historyLoader.asVisibility()
                tvLabelEmptyState.visibility = it.emptyList.asVisibility()
            }

        }

        viewModel.alertLogin.observeEvent(this) {
            if (SessionManager.getInstance().isRefreshingToken) return@observeEvent
            // we won't show alert if currently still refreshing token
            showAlertDialog(
                message = getString(R.string.session_expired_relogin),
                positiveButtonAction = { _: DialogInterface, _: Int ->
                    run {
                        SessionManager.getInstance().logOutInstance()
                        val shouldShowNewLogin = RemoteConfigUtils.shouldShowNewLoginScreen()
                        val loginIntent = if (shouldShowNewLogin) Intent(
                            this,
                            NewLoginActivity::class.java
                        ) else Intent(this, LoginActivity::class.java)
                        loginIntent.flags =
                            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                        loginIntent.putExtra("skipToLogin", true)
                        startActivity(loginIntent)
                    }
                },
                positiveButton = R.string.close_tutorial
            )
        }

        viewModel.query.observe(this) {
            viewModel.postHistories()
        }
    }

    private fun createChip(dateFilter: DateFilter, selectDefault:Boolean): Chip {
        val chip = layoutInflater.inflate(R.layout.include_chip,  binding.cgFilter, false) as Chip
        return chip.apply {
            text = dateFilter.label
            id = View.generateViewId()
            setOnClickListener {
                binding.chipFilterRange.text = getString(R.string.set_range)
                viewModel.getDates(dateFilter)
            }

            if(selectDefault){
               isChecked = true
                viewModel.getDates(dateFilter)
            }
        }

    }

    private fun setupToolbar() {
        with(binding.toolbar) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_arrow_back)
            setNavigationOnClickListener {
                InputUtils.hideKeyBoardWithCheck(this@PaymentsActivity)
                finish()
            }
        }
    }

    private fun updateFilterButtonState() {
        binding.btnFilter.isChecked = !(paymentFilterDto?.isOriginal()).isTrue
    }

    // to prevent search event being logged everytime user type in the search box
    private fun logSearchEvent(query: String) {
        if (query.isNotEmpty() && !hasLogSearchEvent) {
            val propBuilder =
                AppAnalytics.PropBuilder().put(AnalyticsConst.ID, AnalyticsConst.SEARCH)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_LOOKUP, propBuilder)
            hasLogSearchEvent = true
        }
    }


    override fun onFilterBsDismiss(filterApplied: Boolean, updatedFilterDto: PaymentFilterDto?) {
        updateFilterButtonState()
        if (filterApplied) {
            binding.etSearch.text?.clear()
            updatedFilterDto?.let {
                viewModel.submitFilter(updatedFilterDto)
            }
        }
    }

    private fun updateList(startDate:String,endDate:String){
        viewModel.init(
            customerId,
            bookId,
            startDate = startDate,
            endDate = endDate,
            filterDto = paymentFilterDto?: PaymentFilterDto(),
            billerCode
        )
    }

    private fun showDateRangePicker() {
        val constraintsBuilder = CalendarConstraints.Builder()
        val validators: ArrayList<CalendarConstraints.DateValidator> = ArrayList()
        val dateValidator = RangeDateValidator(calendarMaxRange) //number of days
        validators.add(DateValidatorPointBackward.now())
        validators.add(dateValidator)
        constraintsBuilder.setValidator(CompositeDateValidator.allOf(validators))

        val textOnChip = binding.chipFilterRange.text
        var sTime = 0L
        var eTime = 0L
        if(textOnChip.contains("-")) { // Range is selected
            sTime = DateTimeUtils.getLongFormattedDateTime(cStartDate, YYYY_MM_DD).time
            eTime = DateTimeUtils.getLongFormattedDateTime(cEndDate, YYYY_MM_DD).time
            constraintsBuilder.setOpenAt(sTime)

        }
        val calendarBuilder =  MaterialDatePicker.Builder.dateRangePicker()
            .setTheme(R.style.MaterialCalendarTheme)
            .setTitleText(R.string.select_date_range)
            .setCalendarConstraints(constraintsBuilder.build())

        if(sTime!=0L) // already range selected
        {
            calendarBuilder.setSelection(androidx.core.util.Pair(sTime, eTime))

        }

        val dateRangePicker = calendarBuilder.build()
        dateValidator.setDatePicker(dateRangePicker)

        dateRangePicker.show(supportFragmentManager, dateRangePicker.toString())
        dateRangePicker.addOnPositiveButtonClickListener {
            cStartDate = DateTimeUtils.getFormattedDateTime(it.first, YYYY_MM_DD)
            cEndDate = DateTimeUtils.getFormattedDateTime(it.second, YYYY_MM_DD)
            binding.chipFilterRange.text =
                ("${DateTimeUtils.getFormattedDateTime(it.first, DD_MMM_YYYY)} - ${DateTimeUtils.getFormattedDateTime(it.second, DD_MMM_YYYY)}")
            updateList(cStartDate, cEndDate)

       }

    }

    companion object {
        const val CUSTOMER_ID = "customerId"
        const val CATEGORY = "category"
        const val BOOK_ID = "bookId"
        private const val BILLER_CODE = "billerCode"
        const val PAYMENT_TYPE = "paymentType"
        private const val KEY_FRAGMENT_ID = "key_fragment_id"
        private const val KEY_BUNDLE = "key_bundle"
        var ENTRY_POINT = ""
        const val TYPE_LISTRIK = 5
        const val TYPE_PAYMENT_HISTORY = 2

        @JvmStatic
        fun createIntent(
            context: Context,
            customerId: String? = null,
            bookId: String,
            paymentType: Int,
            entryPoint: String
        ): Intent {
            ENTRY_POINT = entryPoint
            return Intent(context, PaymentsActivity::class.java)
                .also {
                    it.putExtras(
                        bundleOf(
                            Pair(CUSTOMER_ID, customerId ?: ""),
                            Pair(BOOK_ID, bookId),
                            Pair(PAYMENT_TYPE, paymentType)
                        )
                    )
                }
        }

        @JvmStatic
        fun createIntent(
            context: Context,
            fragmentId: Int,
            customerId: String? = null,
            bookId: String,
            paymentType: Int
        ): Intent {
            return Intent(context, PaymentsActivity::class.java)
                .also {
                    it.putExtras(
                        bundleOf(
                            Pair(KEY_FRAGMENT_ID, fragmentId),
                            Pair(CUSTOMER_ID, customerId ?: ""),
                            Pair(BOOK_ID, bookId),
                            Pair(PAYMENT_TYPE, paymentType)
                        )
                    )
                }
        }

        @JvmStatic
        fun createIntent(
            context: Context,
            bundle: Bundle,
            entryPoint: String
        ): Intent {
            ENTRY_POINT = entryPoint
            return Intent(context, PaymentsActivity::class.java)
                .also {
                    it.putExtra(KEY_BUNDLE, bundle)
                    if (!bundle.getString(BOOK_ID).isNullOrBlank()) {
                        it.putExtra(BOOK_ID, bundle.getString(BOOK_ID))
                    }
                    if (!bundle.getString(CUSTOMER_ID).isNullOrBlank()) {
                        it.putExtra(CUSTOMER_ID, bundle.getString(CUSTOMER_ID))
                    }
                    if (!bundle.getString(CATEGORY).isNullOrBlank()){
                        it.putExtra(CATEGORY, bundle.getString(CATEGORY))
                    }
                    it.putExtra(PAYMENT_TYPE, bundle.getInt(PAYMENT_TYPE, -1))
                }
        }
    }

    override fun openOrderDetail(order: PaymentHistory) {
        if (order.detailService?.equals("EDC_ADAPTER", true).isTrue) {
            //do nothing
        } else {
            val orderType = order.type?.let { if (it.equals(PaymentHistory.TYPE_CASHBACK_SETTLEMENT, true)) PaymentHistory.TYPE_PAYMENT_OUT else it }
            startActivity(
                PaymentHistoryDetailsActivity.createIntent(
                    context = this@PaymentsActivity,
                    customerId = order.customerId,
                    orderId = order.orderId.orEmpty(),
                    paymentType = orderType,
                    fromMainActivity = false,
                    displayName = order.displayName
                )
            )
        }
    }

    override fun openOrdersHistory() {

    }


}