package com.bukuwarung.payments.banklist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class BankAccountListViewModel @Inject constructor(
        private val paymentUseCase: PaymentUseCase,
        private val businessUseCase: BusinessUseCase,
        private val sessionManager: SessionManager,
        private val finProUseCase: FinproUseCase,
) : BaseViewModel() {

    sealed class Event {
        data class ShowBankList(val list: List<BankAccount>) : Event()
        data class ShowRefundBankList(val list: List<RefundBankAccount>) : Event()
        object HasOngoingTransaction : Event()
        data class ApiError(val message: String?) : Event()
        data class ReturnSelectedAccount(val currentSelectedAccount: BankAccount) : Event()
        data class OnBackPressed(val hasDeletedAnAccount: Boolean, val currentSelectedAccount: BankAccount?) : Event()
    }

    sealed class ProfileIncompleteEvent {
        object ShowProfileDialog : ProfileIncompleteEvent()
    }

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    val profileIncompleteEvent: MutableLiveData<ProfileIncompleteEvent> = MutableLiveData()
    private var bookId: String = sessionManager.businessId
    private var bookEntity: BookEntity? = null
    private var entryPoint = ""
    private var deletedAccount = BankAccount()
    private var currentSelectedAccount: BankAccount? = null
    private var hasDeletedAnAccount = false
    private var bankAccounts = listOf<BankAccount>()
    private var refundBankAccounts = listOf<RefundBankAccount>()
    private lateinit var liveDataBankAccount: LiveData<List<BankAccount>>
    var mediatorLiveData: MediatorLiveData<List<BankAccount>> = MediatorLiveData()
    private var hasAddedSource = false

    private var customerId: String? = null
    private var paymentType: Int = 0
    private var selectedBankCode: String? = null
    private var selectedAccountNumber: String? = null

    fun init(
        paymentType: Int, entryPoint: String,
        cstId: String?, bookId: String?, selectedBankAccountId: String?,
        selectedBankCode: String? = null, selectedAccountNumber: String? = null
    ) {
        this.entryPoint = entryPoint
        this.paymentType = paymentType
        this.selectedBankCode = selectedBankCode
        this.selectedAccountNumber = selectedAccountNumber
        customerId = cstId
        if (bookId != null) this.bookId = bookId
        checkProfileCompletion(selectedBankAccountId)
    }

    fun setTempDeletedAccount(bankAccount: BankAccount) {
        deletedAccount = bankAccount
    }

    fun checkProfileCompletion(selectedBankAccountId: String?) {
        bookEntity = businessUseCase.getBusinessById(this.bookId)
        bookEntity?.run {
            if (!this.hasCompletedProfileWithoutOwnerName()) {
                profileIncompleteEvent.value = ProfileIncompleteEvent.ShowProfileDialog
            } else if(!isPaymentPpob()) getBankAccounts(selectedBankAccountId)
            else
                getRefundMethods()
        }
    }

    fun addNewBankAccount(bankAccount: BankAccount?) {
        bankAccount ?: return
        val newList = bankAccounts.toMutableList().apply { add(bankAccount) }.toList()
        bankAccounts = newList
        eventStatus.value = Event.ShowBankList(newList)
    }

    private fun getRefundMethods() = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val result = finProUseCase.getRefundMethods(bookId)) {
                is ApiSuccessResponse -> {
                    refundBankAccounts =
                        result.body.firstOrNull()?.refundPaymentMethod ?: emptyList()
                    setEventStatus(Event.ShowRefundBankList(refundBankAccounts))
                }
                is ApiErrorResponse -> setEventStatus(Event.ApiError(result.errorMessage))
                else -> setEventStatus(Event.ApiError(AppConst.NO_INTERNET_ERROR_MESSAGE))
            }
        }
    }

    private fun handleBankAccounts(selectedBankAccountId: String?, list: List<BankAccount>?) = viewModelScope.launch {
        val newList = list ?: emptyList()
        val size = newList.size
        for (i in newList.indices) {
            if (selectedBankAccountId == newList[i].bankAccountId
                || (selectedBankCode == newList[i].bankCode && selectedAccountNumber == newList[i].accountNumber)) {
                newList[i].isSelected = 1
                currentSelectedAccount = newList[i]
            }
        }
        bankAccounts = newList
        mediatorLiveData.value = newList
        if (isPaymentIn() || isForQris())
            FeaturePrefManager.getInstance().setHasBankAccount(size > 0, sessionManager.businessId)
    }

    private fun getBankAccounts(selectedBankAccountId: String?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            bookId ?: return@withContext
            if (isPaymentIn() || isForQris()) {
                val typeQuery = if (isForQris()) PaymentConst.QRIS_COMPATIBLE_BANKS_QUERY else null
                liveDataBankAccount = paymentUseCase.getBankAccountsFromLocalAndServer(
                    bookId, false, typeQuery
                )
                if(!hasAddedSource) {
                    withContext(Dispatchers.Main) {
                        mediatorLiveData.addSource(liveDataBankAccount) {
                            handleBankAccounts(selectedBankAccountId, it)
                        }
                        hasAddedSource = true
                    }
                }
                return@withContext
            }
            when (val result = paymentUseCase.getCustomerBankAccounts(bookId, customerId!!)) {
                is ApiSuccessResponse -> {
                    handleBankAccounts(selectedBankAccountId, result.body)
                }
                is ApiErrorResponse -> setEventStatus(Event.ApiError(result.errorMessage))
                else -> setEventStatus(Event.ApiError(AppConst.NO_INTERNET_ERROR_MESSAGE))
            }
        }
    }

    fun setCurrentSelectedAccount(bankAccount: BankAccount) {
        currentSelectedAccount = bankAccount
    }

    fun getCurrentSelectedAccount() {
        currentSelectedAccount?.run {
            eventStatus.value = Event.ReturnSelectedAccount(this)
        }
    }

    fun setQrisBankAccount(settingFirstTime: Boolean, previousSelectedBank: String?, setQrisBank: Boolean) =
        viewModelScope.launch {
        currentSelectedAccount?.run {
            // NOTE: Use case is such that we want user to add a QRIS compatible bank account but
            // don't want to set "is_qris_bank" for that bank account yet.
            this.isQrisBank = setQrisBank
            val prop = AppAnalytics.PropBuilder()
            val event = if (settingFirstTime) {
                prop.put(AnalyticsConst.BANK, this.bankCode)
                AnalyticsConst.EVENT_QRIS_SET_BANK_ACCOUNT
            } else {
                prop.put(AnalyticsConst.CURRENT_BANK, previousSelectedBank)
                AnalyticsConst.EVENT_QRIS_CHANGE_BANK_ACCOUNT
            }
            when (val response = paymentUseCase.addMerchantBankAccount(bookId, this)) {
                is ApiSuccessResponse -> {
                    val bankAccount = response.body
                    prop.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_SUCCESS)
                    if (!settingFirstTime) {
                        prop.put(AnalyticsConst.NEW_BANK, bankAccount.bankCode)
                    }
                    AppAnalytics.trackEvent(event, prop)
                    setEventStatus(Event.ReturnSelectedAccount(bankAccount))
                }
                is ApiErrorResponse -> {
                    prop.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                    AppAnalytics.trackEvent(event, prop)
                    setEventStatus(Event.ApiError(response.errorMessage))
                }
                else -> {
                    prop.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                    AppAnalytics.trackEvent(event, prop)
                    setEventStatus(Event.ApiError(AppConst.NO_INTERNET_ERROR_MESSAGE))
                }
            }
        }
    }

    fun onBackPressed() {
        eventStatus.value = Event.OnBackPressed(hasDeletedAnAccount, currentSelectedAccount)
    }

    fun deleteBankAccount(bankAccount: BankAccount = deletedAccount) = viewModelScope.launch {
        bankAccount.bankAccountId ?: return@launch
        withContext(Dispatchers.IO) {
            val response = if (isPaymentIn() || isForQris()) {
                paymentUseCase.deleteMerchantBankAccount(bookId, bankAccount.bankAccountId)
            } else {
                paymentUseCase.deleteCustomerBankAccount(bookId, customerId!!, bankAccount.bankAccountId)
            }
            if (response is ApiEmptyResponse) {
                // request succeed
                AppAnalytics.trackEvent(if (isPaymentIn() || isForQris()) AnalyticsConst.EVENT_PAYMENT_DELETE_USER_BANK else AnalyticsConst.EVENT_PAYMENT_DELETE_RECIPIENT_BANK,
                        AppAnalytics.PropBuilder()
                                .put(AnalyticsConst.ENTRY_POINT2, entryPoint)
                                .put(if (isPaymentIn() || isForQris()) "delete_user_bank" else "deleted_recipient_bank", bankAccount.bankCode))
                val currentList = bankAccounts.toMutableList()
                currentList.firstOrNull { it.bankAccountId == bankAccount.bankAccountId }?.let {
                    currentList.remove(it)
                }
                bankAccounts = currentList
                hasDeletedAnAccount = true
                if (currentSelectedAccount == null || currentSelectedAccount?.bankAccountId == bankAccount.bankAccountId) {
                    currentSelectedAccount =
                        if (currentList.isNotEmpty()) currentList.first() else null
                    currentSelectedAccount?.isSelected = 1
                }
                setEventStatus(Event.ShowBankList(currentList))
                if (isPaymentIn() && currentSelectedAccount == null)
                    FeaturePrefManager.getInstance().setHasBankAccount(false, sessionManager.businessId)
            } else if (response is ApiErrorResponse) {
                if (response.statusCode == 422)
                    setEventStatus(Event.HasOngoingTransaction)
                else setEventStatus(Event.ApiError(response.errorMessage))
            } else setEventStatus(Event.ApiError(""))
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun isPaymentIn(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_IN

    fun isPaymentOut(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_OUT

    fun isPaymentPpob(): Boolean = paymentType == PaymentConst.TYPE_PPOB

    fun isForQris(): Boolean = paymentType == PaymentConst.TYPE_QRIS_INT
}
