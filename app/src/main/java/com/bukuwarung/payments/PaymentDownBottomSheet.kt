package com.bukuwarung.payments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.databinding.PaymentDownBottomSheetBinding
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isTrue

class PaymentDownBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        private const val IS_SERVICE_DOWN = "isServiceDown"
        private const val MESSAGE = "message"
        private const val HIDE_CLOSE_BUTTON = "hideCloseButton"
        const val TAG = "PaymentDownBottomSheet"
        fun createInstance(isServiceDown: Boolean, message: String?, hideCloseButton: Boolean = false): PaymentDownBottomSheet {
            val bottomSheet = PaymentDownBottomSheet()
            val bundle = Bundle()
            bundle.putBoolean(IS_SERVICE_DOWN, isServiceDown)
            bundle.putString(MESSAGE, message)
            bundle.putBoolean(HIDE_CLOSE_BUTTON, hideCloseButton)
            bottomSheet.arguments = bundle
            return bottomSheet
        }
    }

    interface PaymentDownBsListener {
        fun onButtonClicked()
    }

    private val isServiceDown by lazy {
        arguments?.getBoolean(IS_SERVICE_DOWN) ?: false
    }
    private val message by lazy {
        arguments?.getString(MESSAGE)
    }

    private val hideCloseButton by lazy { arguments?.getBoolean(HIDE_CLOSE_BUTTON).isTrue }

    private var listener: PaymentDownBsListener? = null
    private var _binding: PaymentDownBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        _binding = PaymentDownBottomSheetBinding.inflate(inflater, container, false)

        binding.ivClose.setOnClickListener {
            dismiss()
        }
        binding.ivClose.visibility = hideCloseButton.not().asVisibility()
        if (isServiceDown) {
            binding.ivIcPaymentDown.setImageResource(R.drawable.ic_server_busy)
            binding.tvPaymentDownTitle.setText(R.string.disturbance_message)
            binding.tvPaymentDownBody.text =
                if (message.isNullOrBlank()) getString(R.string.try_later) else message
            binding.btnPaymentDown.setText(R.string.back)
            binding.btnPaymentDown.setOnClickListener {
                dismiss()
                listener?.onButtonClicked()
            }
        } else {
            binding.btnPaymentDown.setOnClickListener {
                dismiss()
                listener?.onButtonClicked()
            }
        }
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? PaymentDownBsListener }
        if (context is PaymentDownBsListener) listener = context
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
