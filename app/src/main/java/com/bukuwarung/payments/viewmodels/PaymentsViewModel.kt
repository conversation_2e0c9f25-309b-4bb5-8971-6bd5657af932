package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.DateFilter
import com.bukuwarung.payments.data.model.PaymentFilterDto
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.utils.DateTimeUtils.YYYY_MM_DD
import com.bukuwarung.utils.DateTimeUtils.getFormattedDateTime
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class PaymentsViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : BaseViewModel() {
    data class ViewState(
        val historyLoader: Boolean = true,
        val emptyList: Boolean = true,
        var paymentFilterDto: PaymentFilterDto = PaymentFilterDto()
    )


    sealed class DetailEvent {
        data class FilterDate(
            val start: String = EMPTY_STRING,
            val end: String = EMPTY_STRING,
        ) : DetailEvent()

        data class HistoryFilter(val dateFilter: List<DateFilter>) : DetailEvent()
    }


    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    private val filterData = MutableLiveData<DetailEvent>()
    val filterDateState: LiveData<DetailEvent> = filterData
    private val historiesList = mutableListOf<PaymentHistory>()
    private val _histories = MutableLiveData<List<PaymentHistory>>()
    val histories: LiveData<List<PaymentHistory>> = _histories

    private val _alert = MutableLiveData<Event<String>>()

    private val _alertLogin = MutableLiveData<Event<Void>>()
    val alertLogin: LiveData<Event<Void>> = _alertLogin

    private val _query = MutableLiveData("")
    val query: LiveData<String> = _query

    private var paymentFilterDto: PaymentFilterDto = PaymentFilterDto()

    private fun currentViewState(): ViewState = viewState.value!!

    private var customerId: String? = null
    private var bookId: String = EMPTY_STRING
    private var calendarMaxRange: Int = 30

    fun init(
        customerId: String?,
        bookId: String? = "",
        startDate: String?,
        endDate: String?,
        filterDto: PaymentFilterDto = paymentFilterDto,
        billerCode: String?
    ) {
        this.customerId = customerId
        this.bookId = bookId ?: EMPTY_STRING
        fetchBaseOnFilter(filterDto, true, startDate, endDate, billerCode)
    }


    private fun fetchBaseOnFilter(
        filterDto: PaymentFilterDto,
        isInit: Boolean = false,
        startDate: String?,
        endDate: String?,
        billerCode: String?
    ) = viewModelScope.launch {
        if (paymentFilterDto == filterDto && !isInit) return@launch
        paymentFilterDto = filterDto
        viewState.value = currentViewState().copy(historyLoader = true, emptyList = false)

        val typeFilters =
            if (paymentFilterDto.typeFilters.contains(PaymentFilterDto.TYPE_ALL)) {
                null
            } else paymentFilterDto.typeFilters

        val statusFilters =
            if (paymentFilterDto.statusFilters.contains(PaymentFilterDto.STATUS_ALL)) {
                null
            } else paymentFilterDto.statusFilters

        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getOrders(
                bookId,
                customerId,
                if (startDate.isNullOrEmpty()) null else startDate,
                if (endDate.isNullOrEmpty()) null else endDate,
                typeFilters,
                statusFilters,
                billerCode = billerCode
            )) {
                is ApiSuccessResponse -> {
                    historiesList.apply {
                        clear()
                        addAll(response.body)
                    }
                    withContext(Dispatchers.Main) {
                        postHistories()
                        viewState.value = currentViewState().copy(
                            historyLoader = false,
                            emptyList = response.body.count() == 0
                        )
                    }
                }
                is ApiErrorResponse -> {
                    withContext(Dispatchers.Main) {
                        if (response.statusCode == 401) {
                            _alertLogin.value = Event.of(null)
                        } else {
                            _alert.value = Event.of(response.errorMessage)
                        }
                        viewState.value =
                            currentViewState().copy(historyLoader = false, emptyList = false)
                    }
                }
                is ApiEmptyResponse -> {
                    withContext(Dispatchers.Main) {
                        viewState.value =
                            currentViewState().copy(historyLoader = false, emptyList = true)
                    }
                }
            }
        }
    }

    fun searchByQuery(query: String) {
        if (_query.value != query) {
            _query.postValue(query)
        }
    }

    fun postHistories() = viewModelScope.launch {
        val data = if (query.value.isNullOrBlank().isTrue) {
            historiesList
        } else {
            historiesList.filter { it.displayName?.startsWith(query.value ?: "", true).isTrue }
        }
        _histories.postValue(data)
    }

    fun submitFilter(filter: PaymentFilterDto) {
        viewState.value = currentViewState().copy(paymentFilterDto = filter)
    }


    fun getRemoteConfigFilters() {
        val config = RemoteConfigUtils.getPaymentConfigs()
        calendarMaxRange = config.customCalendarMaxRange ?: DEFAULT_FILTER_CALENDAR_MAX_RANGE
    }

    fun getCalendarMaxRange() = calendarMaxRange

    fun getDates(dateFilterDay: DateFilter) {
        val presetVal = dateFilterDay.presetValue
        presetVal?.let {
            when (presetVal) {
                PaymentConst.DATE_PRESET.TODAY -> {
                    val cal = Calendar.getInstance()
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(cal.time.time, YYYY_MM_DD),
                            end = getFormattedDateTime(cal.time.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.YESTERDAY -> {
                    val cal = Calendar.getInstance()
                    cal.add(Calendar.DATE, -1)
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(cal.time.time, YYYY_MM_DD),
                            end = getFormattedDateTime(cal.time.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.THIS_WEEK -> {
                    val cal = Calendar.getInstance()
                    val endTime = cal.time
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    val minus =
                        if (dayOfWeek == 1) -6 else 0 - dayOfWeek + 2 //get Monday for this week
                    cal.add(Calendar.DATE, minus)
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(cal.time.time, YYYY_MM_DD),
                            end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                        )

                }
                PaymentConst.DATE_PRESET.LAST_WEEK -> {
                    val cal = Calendar.getInstance()
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    val minus = if (dayOfWeek == 1) -6 else 0 - dayOfWeek + 2
                    val lastWeekMonday = minus - 7
                    cal.add(Calendar.DATE, lastWeekMonday)
                    val sTime = cal.time
                    cal.add(Calendar.DATE, 6)
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(sTime.time, YYYY_MM_DD),
                            end = getFormattedDateTime(cal.time.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.THIS_MONTH -> {
                    val cal = Calendar.getInstance()
                    val endTime = cal.time
                    cal[Calendar.DAY_OF_MONTH] = 1
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(cal.time.time, YYYY_MM_DD),
                            end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.LAST_MONTH -> {
                    val aCalendar = Calendar.getInstance()
                    aCalendar.add(Calendar.MONTH, -1)
                    aCalendar[Calendar.DATE] = 1
                    val sTime = aCalendar.time
                    aCalendar[Calendar.DATE] = aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = aCalendar.time
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(sTime.time, YYYY_MM_DD),
                            end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.THIS_YEAR -> {
                    val cal = Calendar.getInstance()
                    val endTime = cal.time
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    val sTime = cal.time
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(sTime.time, YYYY_MM_DD),
                            end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.LAST_YEAR -> {
                    val cal = Calendar.getInstance()
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    cal.add(Calendar.YEAR, -1)
                    val sTime = cal.time
                    cal.set(Calendar.MONTH, 11)
                    cal[Calendar.DATE] = cal.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = cal.time
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(sTime.time, YYYY_MM_DD),
                            end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                        )
                }
                PaymentConst.DATE_PRESET.ALL -> {
                    filterData.value = DetailEvent.FilterDate(
                        start = EMPTY_STRING,
                        end = EMPTY_STRING
                    )
                }

                else -> { // Default TODAY
                    val cal = Calendar.getInstance()
                    filterData.value =
                        DetailEvent.FilterDate(
                            start = getFormattedDateTime(cal.time.time, YYYY_MM_DD),
                            end = getFormattedDateTime(cal.time.time, YYYY_MM_DD)
                        )
                }
            }
        } ?: run {
            val startDate = dateFilterDay.startDays
            val endDate = dateFilterDay.endDays
            if (startDate != null && endDate != null) {
                var cal = Calendar.getInstance()
                cal.add(Calendar.DATE, startDate)
                val sTime = cal.time
                cal = Calendar.getInstance()
                cal.add(Calendar.DATE, endDate)
                val endTime = cal.time
                filterData.value =
                    DetailEvent.FilterDate(
                        start = getFormattedDateTime(sTime.time, YYYY_MM_DD),
                        end = getFormattedDateTime(endTime.time, YYYY_MM_DD)
                    )
            }
        }

    }


}