<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/homepage_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/main_content_frame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content" >

            <com.bukuwarung.baseui.DefaultViewPager
                android:id="@+id/main_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="56dp" />

            <include layout="@layout/tab_navigation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/main_container"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/snackbar_guideline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/navigation_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="start">

        <include layout="@layout/activity_side_nav" />

    </LinearLayout>
</androidx.drawerlayout.widget.DrawerLayout>
